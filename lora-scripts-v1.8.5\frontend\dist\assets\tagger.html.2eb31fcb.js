const e=JSON.parse(`{"key":"v-51615306","path":"/tagger.html","title":"Tagger \u6807\u6CE8\u5DE5\u5177","lang":"en-US","frontmatter":{"type":"tagger","code":"Schema.intersect([\\n    Schema.object({\\n        path: Schema.string().role('folder').required().description(\\"\u56FE\u7247\u6587\u4EF6\u5939\u8DEF\u5F84\\"),\\n        threshold: Schema.number().role(\\"slider\\").min(0).max(1).step(0.01).default(0.5).description(\\"\u9608\u503C\\"),\\n        interrogator_model: Schema.union([\\"wd-convnext-v3\\", \\"wd-swinv2-v3\\", \\"wd-vit-v3\\", \\"wd14-convnextv2-v2\\", \\"wd14-swinv2-v2\\", \\"wd14-vit-v2\\", \\"wd14-moat-v2\\"]).default(\\"wd14-convnextv2-v2\\").description(\\"Tagger \u6A21\u578B\\"),\\n        additional_tags: Schema.string().role('folder').description(\\"\u9644\u52A0\u63D0\u793A\u8BCD (\u9017\u53F7\u5206\u9694)\\"),\\n        replace_underscore: Schema.boolean().default(true).description(\\"\u4F7F\u7528\u7A7A\u683C\u4EE3\u66FF\u4E0B\u5212\u7EBF\\"),\\n        escape_tag: Schema.boolean().default(true).description(\\"\u5C06\u7ED3\u679C\u4E2D\u7684\u62EC\u53F7\u8FDB\u884C\u8F6C\u4E49\u5904\u7406\\"),\\n        batch_input_recursive: Schema.boolean().default(false).description(\\"\u9012\u5F52\u641C\u7D22\u5B50\u6587\u4EF6\u5939\u56FE\u7247\\"),\\n        batch_output_action_on_conflict: Schema.union([\\"ignore\\", \\"copy\\", \\"prepend\\"]).default(\\"copy\\").description(\\"\u82E5\u5DF2\u7ECF\u5B58\u5728\u8BC6\u522B\u7684 Tag \u6587\u4EF6\uFF0C\u5219\\"),\\n    }).description(\\"Tagger \u53C2\u6570\u8BBE\u7F6E\\"),\\n]);\\n"},"excerpt":"","headers":[],"filePathRelative":"tagger.md"}`);export{e as data};
