# EvoTune网络架构图 - 顶会论文标准

## 1. 详细版网络架构图（主图）

### 1.1 Mermaid代码（详细版）

```mermaid
graph TB
    subgraph "Input Layer 输入层"
        A["Input 输入<br/>x_t ∈ ℝ^(B×C×H×W)<br/>timestep t ∈ [0,1000]"]
    end
    
    subgraph "EvoTune Scheduler EvoTune调谐器"
        B["Time Normalization 时间步标准化<br/>t̃ = t/1000<br/>(Eq. 1)"]
        C["Phase Computation 相位计算<br/>φ_early = σ(10(t̃-0.8))<br/>φ_late = σ(10(0.2-t̃))<br/>(Eq. 2-3)"]
        D["Parameter Generation 参数生成<br/>b₁(t), b₂(t), s₁(t), s₂(t)<br/>w_attn(t)<br/>(Eq. 4-8)"]
    end
    
    subgraph "Timestep-aware Agent Attention 时间步感知Agent注意力"
        E["Agent Generation Agent生成<br/>A = AdaptivePool(x)<br/>A ∈ ℝ^(B×49×C)<br/>(Eq. 10)"]
        F["QKV Computation QKV计算<br/>Q,K = Linear(A)<br/>V = DepthwiseConv(x)<br/>(Eq. 11-12)"]
        G["Bi-directional Attention 双向注意力<br/>Attn₁, Attn₂<br/>(Eq. 13-14)"]
        H["Feature Fusion 特征融合<br/>Y = Attn₂·(Attn₁·V) + V<br/>(Eq. 15)"]
        I["Timestep Modulation 时间步调制<br/>Y_out = Y × w_attn(t)<br/>(Eq. 16)"]
    end
    
    subgraph "Timestep-aware Frequency Processing 时间步感知频域处理"
        J["FFT Transform FFT变换<br/>X_freq = FFT2D(Y_out)<br/>(Eq. 17)"]
        K["Adaptive Filtering 自适应滤波<br/>Mask(u,v) based on s₁(t), s₂(t)<br/>τ(t) = 7 + ⌊5(1-t̃)⌋<br/>(Eq. 18-19)"]
        L["IFFT Transform IFFT逆变换<br/>X_enhanced = IFFT2D(X_filtered)<br/>(Eq. 21)"]
    end
    
    subgraph "Output Layer 输出层"
        M["Enhanced Features 增强特征<br/>→ Original UNet 原始UNet<br/>(Eq. 22)"]
    end
    
    A --> B
    B --> C
    C --> D
    
    A --> E
    E --> F
    F --> G
    G --> H
    
    D --> I
    H --> I
    I --> J
    
    J --> K
    D --> K
    K --> L
    L --> M
    
    style A fill:#e3f2fd
    style D fill:#fff3e0
    style I fill:#f3e5f5
    style K fill:#fce4ec
    style M fill:#e8f5e8
```

## 2. 简化版网络架构图（概览图）

### 2.1 Mermaid代码（简化版）

```mermaid
graph TB
    subgraph "Input 输入"
        A["x_t, t"]
    end
    
    subgraph "EvoTune Scheduler<br/>EvoTune调谐器"
        B["Timestep Tuning<br/>时间步调谐<br/>(Eq. 1-8)"]
    end
    
    subgraph "Timestep-aware Processing<br/>时间步感知处理"
        C["Agent Attention<br/>Agent注意力<br/>(Eq. 9-16)"]
        D["Frequency Processing<br/>频域处理<br/>(Eq. 17-21)"]
    end
    
    subgraph "Output 输出"
        E["Enhanced Features<br/>增强特征<br/>(Eq. 22)"]
    end
    
    A --> B
    B --> C
    B --> D
    A --> C
    C --> D
    D --> E
    
    style A fill:#e3f2fd
    style B fill:#fff3e0
    style C fill:#f3e5f5
    style D fill:#fce4ec
    style E fill:#e8f5e8
```

## 3. 公式与网络模块对应关系

### 3.1 EvoTune调谐器模块

| 网络模块 | 对应公式 | 公式内容 | 功能说明 |
|----------|----------|----------|----------|
| Time Normalization<br/>时间步标准化 | Eq. 1 | $\tilde{t} = \frac{t}{1000}$ | 将时间步标准化到[0,1] |
| Phase Computation<br/>相位计算 | Eq. 2-3 | $\phi_{early} = \sigma(10(\tilde{t}-0.8))$<br/>$\phi_{late} = \sigma(10(0.2-\tilde{t}))$ | 计算早期和后期相位函数 |
| Parameter Generation<br/>参数生成 | Eq. 4-8 | $b_1(t) = 1.5 + 0.3 \times \phi_{early} \times 0.5$<br/>$s_1(t) = 0.8 + 0.3 \times \phi_{late} \times 0.4$<br/>$w_{attn}(t) = 0.5 + 0.5\sigma(8(0.3-\tilde{t}))$ | 生成自适应参数 |

### 3.2 时间步感知Agent注意力模块

| 网络模块 | 对应公式 | 公式内容 | 功能说明 |
|----------|----------|----------|----------|
| Agent Generation<br/>Agent生成 | Eq. 10 | $A = \text{AdaptiveAvgPool2d}(X)$ | 生成49个Agent tokens |
| QKV Computation<br/>QKV计算 | Eq. 11-12 | $Q,K = \text{Linear}(A)$<br/>$V = \text{DepthwiseConv2d}(X)$ | 计算查询、键、值矩阵 |
| Bi-directional Attention<br/>双向注意力 | Eq. 13-14 | $\text{Attn}_1 = \text{softmax}(\frac{Q \cdot K^T}{\sqrt{d}})$<br/>$\text{Attn}_2 = \text{softmax}(\frac{K \cdot Q^T}{\sqrt{d}})$ | 计算双向注意力权重 |
| Feature Fusion<br/>特征融合 | Eq. 15 | $Y = \text{Attn}_2 \cdot (\text{Attn}_1 \cdot V) + V$ | 融合注意力特征 |
| Timestep Modulation<br/>时间步调制 | Eq. 16 | $Y_{out} = Y \times w_{attn}(t)$ | **关键创新**：时间步权重调制 |

### 3.3 时间步感知频域处理模块

| 网络模块 | 对应公式 | 公式内容 | 功能说明 |
|----------|----------|----------|----------|
| FFT Transform<br/>FFT变换 | Eq. 17 | $X_{freq} = \text{FFT2D}(Y_{out})$ | 空间域到频域变换 |
| Adaptive Filtering<br/>自适应滤波 | Eq. 18-19 | $\tau(t) = 7 + \lfloor 5(1-\tilde{t}) \rfloor$<br/>$\text{Mask}(u,v) = \begin{cases} s_1(t) & \text{if } |u-u_c|,|v-v_c| \leq \tau(t) \\ 1 & \text{otherwise} \end{cases}$ | **关键创新**：基于时间步的自适应滤波 |
| IFFT Transform<br/>IFFT逆变换 | Eq. 21 | $X_{enhanced} = \text{IFFT2D}(X_{filtered})$ | 频域到空间域变换 |

### 3.4 完整前向传播

| 网络模块 | 对应公式 | 公式内容 | 功能说明 |
|----------|----------|----------|----------|
| Enhanced Features<br/>增强特征 | Eq. 22 | $\hat{x}_{t-1} = \text{EvoTune}(x_t, t)$ | 完整的EvoTune前向传播 |

## 4. 顶会论文标准设计特点

### 4.1 视觉设计原则
- **层次清晰**：使用subgraph明确模块边界
- **双语标注**：英文+中文，符合国际期刊标准
- **公式对应**：每个模块都标注对应的公式编号
- **颜色编码**：不同功能模块使用不同颜色
- **数据流向**：清晰的箭头指示数据流向

### 4.2 模块功能标注
- **输入层**：蓝色，标注数据维度
- **调谐器**：橙色，核心控制模块
- **注意力**：紫色，时间步感知处理
- **频域**：粉色，自适应滤波处理
- **输出层**：绿色，最终增强特征

### 4.3 创新点突出
- **时间步调制**：Eq. 16，Agent Attention的关键创新
- **自适应滤波**：Eq. 18-19，频域处理的关键创新
- **统一调谐**：Eq. 1-8，整个系统的核心创新

## 5. 论文使用建议

### 5.1 图表布局
```
Figure 1: EvoTune网络架构图（详细版）
- 主图：展示完整的网络结构和数据流向
- 标注：每个模块的功能和对应公式

Figure 2: EvoTune网络架构图（简化版）  
- 概览图：突出主要模块和创新点
- 用于摘要或介绍部分
```

### 5.2 图注说明
```
Figure 1. EvoTune网络架构图。EvoTune包含三个主要模块：(1) EvoTune调谐器根据时间步生成自适应参数(Eq. 1-8)；(2) 时间步感知Agent注意力通过w_attn(t)实现时间步调制(Eq. 9-16)；(3) 时间步感知频域处理通过s₁(t), s₂(t)实现自适应滤波(Eq. 17-21)。所有模块协同工作，实现时间步感知的特征增强。
```

### 5.3 与文本对应
- **方法论部分**：详细版图 + 完整公式推导
- **摘要部分**：简化版图 + 核心创新描述
- **实验部分**：参数演化图 + 消融实验

## 6. 导出和使用

### 6.1 高质量导出
1. 复制Mermaid代码到 https://mermaid.live/
2. 调整图形大小和样式
3. 导出为SVG格式（矢量图，适合论文）
4. 或导出为高分辨率PNG（≥300 DPI）

### 6.2 专业工具制作
- **推荐**：基于此设计在TikZ、Draw.io或Visio中重新制作
- **优势**：更精细的控制和更专业的排版
- **格式**：PDF矢量格式，适合顶级期刊

### 6.3 颜色方案（RGB值）
- 输入层：#e3f2fd (蓝色)
- 调谐器：#fff3e0 (橙色)  
- 注意力：#f3e5f5 (紫色)
- 频域：#fce4ec (粉色)
- 输出层：#e8f5e8 (绿色)

这个设计完全符合顶会论文的标准，清晰展示了EvoTune的创新架构和公式对应关系。
