"""
EvoTune模型评估指标计算模块
包含SSIM、PSNR、NIQE等图像质量评估指标

作者: AI Assistant
日期: 2025-01-11
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import cv2
from typing import Tuple, Dict, List, Optional, Union
import math
from scipy import ndimage
from scipy.stats import entropy
import warnings

warnings.filterwarnings('ignore')


class SSIMMetric(nn.Module):
    """
    结构相似性指数(SSIM)计算
    """
    def __init__(self, window_size: int = 11, sigma: float = 1.5, channel: int = 3):
        super().__init__()
        self.window_size = window_size
        self.sigma = sigma
        self.channel = channel
        
        # 创建高斯窗口
        self.window = self.create_window(window_size, sigma, channel)

    def create_window(self, window_size: int, sigma: float, channel: int):
        """
        创建高斯窗口
        """
        coords = torch.arange(window_size, dtype=torch.float32)
        coords -= window_size // 2
        
        g = torch.exp(-(coords ** 2) / (2 * sigma ** 2))
        g /= g.sum()
        
        window_1d = g.unsqueeze(1)
        window_2d = window_1d.mm(window_1d.t()).float().unsqueeze(0).unsqueeze(0)
        window = window_2d.expand(channel, 1, window_size, window_size).contiguous()
        
        return window

    def forward(self, img1: torch.Tensor, img2: torch.Tensor) -> torch.Tensor:
        """
        计算SSIM
        """
        if img1.device != self.window.device:
            self.window = self.window.to(img1.device)
        
        mu1 = F.conv2d(img1, self.window, padding=self.window_size//2, groups=self.channel)
        mu2 = F.conv2d(img2, self.window, padding=self.window_size//2, groups=self.channel)
        
        mu1_sq = mu1.pow(2)
        mu2_sq = mu2.pow(2)
        mu1_mu2 = mu1 * mu2
        
        sigma1_sq = F.conv2d(img1 * img1, self.window, padding=self.window_size//2, groups=self.channel) - mu1_sq
        sigma2_sq = F.conv2d(img2 * img2, self.window, padding=self.window_size//2, groups=self.channel) - mu2_sq
        sigma12 = F.conv2d(img1 * img2, self.window, padding=self.window_size//2, groups=self.channel) - mu1_mu2
        
        C1 = 0.01 ** 2
        C2 = 0.03 ** 2
        
        ssim_map = ((2 * mu1_mu2 + C1) * (2 * sigma12 + C2)) / ((mu1_sq + mu2_sq + C1) * (sigma1_sq + sigma2_sq + C2))
        
        return ssim_map.mean()


class PSNRMetric:
    """
    峰值信噪比(PSNR)计算
    """
    @staticmethod
    def calculate(img1: torch.Tensor, img2: torch.Tensor, max_val: float = 1.0) -> float:
        """
        计算PSNR
        """
        mse = F.mse_loss(img1, img2)
        if mse == 0:
            return float('inf')
        
        psnr = 20 * torch.log10(max_val / torch.sqrt(mse))
        return psnr.item()


class NIQEMetric:
    """
    自然图像质量评估器(NIQE)
    基于自然场景统计的无参考图像质量评估
    """
    def __init__(self):
        # NIQE参数
        self.patch_size = 96
        self.overlap = 0
        self.mu_prisparam = np.array([
            0.906, 0.895, 0.955, 0.658, 0.78, 0.654, 0.901, 0.897, 0.957, 0.666, 
            0.791, 0.664, 0.899, 0.894, 0.955, 0.663, 0.787, 0.661, 0.899, 0.894, 
            0.955, 0.663, 0.787, 0.661, 0.899, 0.894, 0.955, 0.663, 0.787, 0.661, 
            0.899, 0.894, 0.955, 0.663, 0.787, 0.661
        ])
        
        self.cov_prisparam = np.array([
            [0.0015, 0.0014, 0.0013, 0.0012, 0.0011, 0.0010],
            [0.0014, 0.0015, 0.0014, 0.0013, 0.0012, 0.0011],
            [0.0013, 0.0014, 0.0015, 0.0014, 0.0013, 0.0012],
            [0.0012, 0.0013, 0.0014, 0.0015, 0.0014, 0.0013],
            [0.0011, 0.0012, 0.0013, 0.0014, 0.0015, 0.0014],
            [0.0010, 0.0011, 0.0012, 0.0013, 0.0014, 0.0015]
        ])

    def calculate(self, img: np.ndarray) -> float:
        """
        计算NIQE分数
        """
        if len(img.shape) == 3:
            img = cv2.cvtColor(img, cv2.COLOR_RGB2GRAY)
        
        img = img.astype(np.float64)
        
        # 提取特征
        features = self.extract_features(img)
        
        # 计算质量分数
        mu_distparam = np.mean(features, axis=0)
        cov_distparam = np.cov(features.T)
        
        # 计算马氏距离
        # 确保协方差矩阵维度匹配
        feature_dim = len(mu_distparam)
        if cov_distparam.shape[0] != feature_dim:
            # 如果维度不匹配，使用对角协方差矩阵
            cov_distparam = np.diag(np.var(features, axis=0))

        # 使用适当大小的先验协方差矩阵
        if feature_dim <= self.cov_prisparam.shape[0]:
            cov_prior = self.cov_prisparam[:feature_dim, :feature_dim]
        else:
            # 如果特征维度更大，扩展先验协方差矩阵
            cov_prior = np.eye(feature_dim) * 0.001

        invcov_param = np.linalg.pinv((cov_prior + cov_distparam) / 2)
        mu_diff = mu_distparam - self.mu_prisparam[:feature_dim]

        niqe_score = np.sqrt(mu_diff @ invcov_param @ mu_diff.T)
        
        return float(niqe_score)

    def extract_features(self, img: np.ndarray) -> np.ndarray:
        """
        提取NIQE特征
        """
        h, w = img.shape
        features = []
        
        # 分块处理
        for i in range(0, h - self.patch_size + 1, self.patch_size - self.overlap):
            for j in range(0, w - self.patch_size + 1, self.patch_size - self.overlap):
                patch = img[i:i+self.patch_size, j:j+self.patch_size]
                
                # 计算局部特征
                patch_features = self.compute_patch_features(patch)
                features.append(patch_features)
        
        return np.array(features)

    def compute_patch_features(self, patch: np.ndarray) -> np.ndarray:
        """
        计算图像块特征
        """
        # 归一化
        patch = (patch - np.mean(patch)) / (np.std(patch) + 1e-10)
        
        # 计算梯度
        dx = ndimage.sobel(patch, axis=1)
        dy = ndimage.sobel(patch, axis=0)
        
        # 计算统计特征
        features = []
        
        # 原始图像统计
        features.extend([np.mean(patch), np.std(patch), np.var(patch)])
        
        # 梯度统计
        features.extend([np.mean(dx), np.std(dx), np.var(dx)])
        features.extend([np.mean(dy), np.std(dy), np.var(dy)])
        
        # 高阶统计
        features.extend([self.compute_skewness(patch), self.compute_kurtosis(patch)])
        
        return np.array(features)

    @staticmethod
    def compute_skewness(data: np.ndarray) -> float:
        """计算偏度"""
        mean = np.mean(data)
        std = np.std(data)
        if std == 0:
            return 0
        return np.mean(((data - mean) / std) ** 3)

    @staticmethod
    def compute_kurtosis(data: np.ndarray) -> float:
        """计算峰度"""
        mean = np.mean(data)
        std = np.std(data)
        if std == 0:
            return 0
        return np.mean(((data - mean) / std) ** 4) - 3


class WaterOilQualityMetrics:
    """
    专门针对水油区域的质量评估指标
    """
    def __init__(self):
        self.ssim_metric = SSIMMetric()

    def calculate_water_oil_ssim(self, 
                                generated: torch.Tensor, 
                                reference: torch.Tensor,
                                water_mask: Optional[torch.Tensor] = None,
                                oil_mask: Optional[torch.Tensor] = None) -> Dict[str, float]:
        """
        计算水油区域的SSIM
        """
        results = {}
        
        # 整体SSIM
        results['overall_ssim'] = self.ssim_metric(generated, reference).item()
        
        if water_mask is not None:
            # 水区域SSIM
            water_gen = generated * water_mask
            water_ref = reference * water_mask
            results['water_ssim'] = self.ssim_metric(water_gen, water_ref).item()
        
        if oil_mask is not None:
            # 油区域SSIM
            oil_gen = generated * oil_mask
            oil_ref = reference * oil_mask
            results['oil_ssim'] = self.ssim_metric(oil_gen, oil_ref).item()
        
        return results

    def calculate_texture_consistency(self, 
                                    generated: torch.Tensor,
                                    reference: torch.Tensor) -> Dict[str, float]:
        """
        计算纹理一致性指标
        """
        # 转换为numpy格式
        gen_np = generated.detach().cpu().numpy()
        ref_np = reference.detach().cpu().numpy()
        
        if len(gen_np.shape) == 4:  # [B, C, H, W]
            gen_np = gen_np[0].transpose(1, 2, 0)
            ref_np = ref_np[0].transpose(1, 2, 0)
        
        # 转换为灰度图
        if gen_np.shape[-1] == 3:
            gen_gray = cv2.cvtColor((gen_np * 255).astype(np.uint8), cv2.COLOR_RGB2GRAY)
            ref_gray = cv2.cvtColor((ref_np * 255).astype(np.uint8), cv2.COLOR_RGB2GRAY)
        else:
            gen_gray = (gen_np.squeeze() * 255).astype(np.uint8)
            ref_gray = (ref_np.squeeze() * 255).astype(np.uint8)
        
        # 计算纹理特征
        gen_texture = self.extract_texture_features(gen_gray)
        ref_texture = self.extract_texture_features(ref_gray)
        
        # 计算相似性
        texture_similarity = np.corrcoef(gen_texture, ref_texture)[0, 1]
        
        return {
            'texture_similarity': float(texture_similarity),
            'texture_mse': float(np.mean((gen_texture - ref_texture) ** 2))
        }

    def extract_texture_features(self, img: np.ndarray) -> np.ndarray:
        """
        提取纹理特征
        """
        # LBP特征
        lbp = self.compute_lbp(img)
        
        # 梯度特征
        grad_x = cv2.Sobel(img, cv2.CV_64F, 1, 0, ksize=3)
        grad_y = cv2.Sobel(img, cv2.CV_64F, 0, 1, ksize=3)
        
        # 统计特征
        features = [
            np.mean(lbp), np.std(lbp),
            np.mean(grad_x), np.std(grad_x),
            np.mean(grad_y), np.std(grad_y),
            np.mean(img), np.std(img)
        ]
        
        return np.array(features)

    def compute_lbp(self, img: np.ndarray, radius: int = 1, n_points: int = 8) -> np.ndarray:
        """
        计算局部二值模式(LBP)
        """
        h, w = img.shape
        lbp = np.zeros((h, w), dtype=np.uint8)
        
        for i in range(radius, h - radius):
            for j in range(radius, w - radius):
                center = img[i, j]
                code = 0
                
                for p in range(n_points):
                    angle = 2 * np.pi * p / n_points
                    x = int(round(i + radius * np.cos(angle)))
                    y = int(round(j + radius * np.sin(angle)))
                    
                    if 0 <= x < h and 0 <= y < w:
                        if img[x, y] >= center:
                            code |= (1 << p)
                
                lbp[i, j] = code
        
        return lbp


class EvoTuneEvaluator:
    """
    EvoTune模型综合评估器
    """
    def __init__(self):
        self.ssim_metric = SSIMMetric()
        self.niqe_metric = NIQEMetric()
        self.water_oil_metrics = WaterOilQualityMetrics()

    def evaluate_generation_quality(self, 
                                  generated_images: torch.Tensor,
                                  reference_images: torch.Tensor,
                                  water_masks: Optional[torch.Tensor] = None,
                                  oil_masks: Optional[torch.Tensor] = None) -> Dict[str, float]:
        """
        综合评估生成质量
        """
        results = {}
        
        # 基础指标
        results['ssim'] = self.ssim_metric(generated_images, reference_images).item()
        results['psnr'] = PSNRMetric.calculate(generated_images, reference_images)
        
        # NIQE指标
        gen_np = generated_images[0].detach().cpu().numpy().transpose(1, 2, 0)
        gen_np = (gen_np * 255).astype(np.uint8)
        results['niqe'] = self.niqe_metric.calculate(gen_np)
        
        # 水油区域特定指标
        if water_masks is not None or oil_masks is not None:
            water_oil_results = self.water_oil_metrics.calculate_water_oil_ssim(
                generated_images, reference_images, water_masks, oil_masks
            )
            results.update(water_oil_results)
        
        # 纹理一致性
        texture_results = self.water_oil_metrics.calculate_texture_consistency(
            generated_images, reference_images
        )
        results.update(texture_results)
        
        return results

    def batch_evaluate(self, 
                      generated_batch: torch.Tensor,
                      reference_batch: torch.Tensor) -> Dict[str, List[float]]:
        """
        批量评估
        """
        batch_results = {
            'ssim': [],
            'psnr': [],
            'niqe': []
        }
        
        for i in range(generated_batch.shape[0]):
            gen_img = generated_batch[i:i+1]
            ref_img = reference_batch[i:i+1]
            
            # SSIM和PSNR
            ssim_score = self.ssim_metric(gen_img, ref_img).item()
            psnr_score = PSNRMetric.calculate(gen_img, ref_img)
            
            batch_results['ssim'].append(ssim_score)
            batch_results['psnr'].append(psnr_score)
            
            # NIQE
            gen_np = gen_img[0].detach().cpu().numpy().transpose(1, 2, 0)
            gen_np = (gen_np * 255).astype(np.uint8)
            niqe_score = self.niqe_metric.calculate(gen_np)
            batch_results['niqe'].append(niqe_score)
        
        return batch_results


# 测试函数
def test_evaluation_metrics():
    """
    测试评估指标
    """
    print("=== EvoTune评估指标测试 ===")
    
    # 创建测试数据
    generated = torch.randn(2, 3, 256, 256)
    reference = torch.randn(2, 3, 256, 256)
    
    # 创建评估器
    evaluator = EvoTuneEvaluator()
    
    # 单张图像评估
    results = evaluator.evaluate_generation_quality(
        generated[0:1], reference[0:1]
    )
    
    print("单张图像评估结果:")
    for metric, value in results.items():
        print(f"  {metric}: {value:.4f}")
    
    # 批量评估
    batch_results = evaluator.batch_evaluate(generated, reference)
    
    print("\n批量评估结果:")
    for metric, values in batch_results.items():
        print(f"  {metric}: 均值={np.mean(values):.4f}, 标准差={np.std(values):.4f}")
    
    print("\n=== 评估指标测试完成 ===")


if __name__ == "__main__":
    test_evaluation_metrics()
