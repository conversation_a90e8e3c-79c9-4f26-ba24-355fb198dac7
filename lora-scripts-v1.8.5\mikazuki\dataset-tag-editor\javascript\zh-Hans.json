{"Maximum resolution of gallery thumbnails (0 to disable)": "图库缩略图的最大分辨率 (0 为禁用)", "Directory to save temporary files": "保存临时文件的目录", "Cleanup temporary files on startup": "启动时清理临时文件", "Column number of image galleries": "图库的列数", "regex to read caption from filename": "从文件名中读取描述的正则表达式", "Replace underbar (_) in tags with whitespace ( )": "将标签中的下划线 (_) 替换为空格 ( )", "Use CPU to interrogate": "使用 CPU 来反推 Tag", "Main": "主菜单", "Settings": "设置", "Unload": "卸载", "Invert selection": "反向选择", "Select Tags": "选择标签", "Remove": "移除", "Search and Replace": "搜索与替换", "Move or Delete": "移动或删除", "Destination Directory": "目标文件夹", "Dataset Images": "数据集图片", "Invert DELETE cannot be undone. The files will be deleted completely.": "删除操作无法撤销，文件会被完全删除。", "1. The tags common to all displayed images are shown in comma separated style.": "1. 所有显示图像的共同标签要用逗号分隔的样式显示。\n\n ", "2. When changes are applied, all tags in each displayed images are replaced.": "当应用更改时，所有所显示的图像的标签将会被替换", "3. If you change some tags into blank, they will be erased.": "如果你更改某些标签到空白处，他们将会被移除", "4. If you add some tags to the end, they will be added to the end/beginning of the text file.": "如果你将一些标签添加至末端，它们将被添加在文本文件的开始/末尾。", "5. Changes are not applied to the text files until the \"Save all changes\" button is pressed.": "在按下保存所有更改的按钮之前，所有在文本文件中所做的更改将不会生效", "Add selection [Enter]": "添加选择 [输入]", "All Displayed Ones": "所有已显示的", "AND": "与", "Apply changes to ALL displayed images": "将更改应用于所有已显示的图像", "Apply changes to filtered images": "应用更改至过滤后的图片", "Apply changes to selected image": "将更改应用于所选图像", "Apply selection filter": "应用选择过滤器", "Backup original text file (original file will be renamed like filename.000, .001, .002, ...)": "备份原本 txt 文件  (原文件会被重命名，类似于：.000, .001, .002, ...)", "Batch Edit Captions": "批量编辑描述", "Booru Score Threshold": "Booru 极限阈值", "Caption Backup File": "描述备份文件", "Caption File Ext": "文件扩展名", "Caption of Selected Image": "被选图片的描述", "Caption Text File": "描述文本文件", "Changes are not applied to the text files until the \"Save all changes\" button is pressed.": "更改不会被应用至文本文件，直到 “保存所有更改” 按钮被按下", "Common Tags": "原标签 (Common Tags)", "Copy and Overwrite": "复制与覆写", "Copy caption from selected images automatically": "自动从被选图片中复制描述", "Dataset directory": "数据集目录", "Deselect visible tags": "取消选择可见标签", "Each Tags": "每个标签", "Edit Caption": "编辑描述", "Edit Caption of Selected Image": "编辑被选图片的描述", "Edit Tags": "编辑标签 （Edit Tags）", "Entire Caption": "整个描述", "Filter by Selection": "通过选择过滤", "Filter by Tags": "通过标签过滤", "Filter Images": "过滤图片", "Filter Images by Tags": "通过标签过滤图片", "Filter Logic": "过滤逻辑", "Force image gallery to use temporary files": "强制图库使用临时文件", "If Empty": "如果为空", "Interrogators": "反推算法", "Load": "加载", "Load caption from filename if no text file exists": "当文本文件不存在时，从文件名中加载描述", "Max resolution of temporary files": "临时文件最大分辨率", "Move or Delete Files": "移动/删除文件", "Negative Filter": "Negative 过滤器", "New text file will be created if you are using filename as captions.": "如果您使用文件名作为描述，将创建新的文本文件", "Note: Moved or deleted images will be unloaded.": "备注：已移动或已删除的图像将被卸载。", "Only Selected Tags": "仅选中标签", "OR": "或", "Original Text = \"A, A, B, C\" Common Tags = \"B, A\" Edit Tags = \"X, Y\"": "原文本 = \"A, A, B, C\" 常用标签 = \"B, A\" 编辑标签 = \"X, Y\"", "Original Text = \"A, B, C\" Common Tags = \"(nothing)\" Edit Tags = \"X, Y\"": "原文本 = \"A, B, C\" 常用标签 = \"(nothing)\" 编辑标签 = \"X, Y\"", "Original Text = \"A, B, C, D, E\" Common Tags = \"A, B, D\" Edit Tags = \", X, \"": "原文本 = \"A, B, C, D, E\" 常用标签 = \"A, B, D\" 编辑标签 = \", X, \"", "Positive Filter": "Positive 过滤器", "Read Caption from Selected Image": "从选择的图片中读取描述", "Reload/Save Settings (config.json)": "重新载入/保存设置 (config.json)", "Reload settings": "重新载入设置", "Remove duplicated tag": "删除重复标签", "Remove duplicate tags": "移除重复标签", "Remove selected tags": "移除选中标签", "Remove selection [Delete]": "移除选择 [删除]", "Replace new-line character with comma": "用逗号替换换行符", "Replace Text": "替换文本", "Restore settings to default": "恢复至默认设置", "Result = \"A, B, C, X, Y\" (add X and Y to the end (default))": "结果 = \"A, B, C, X, Y\" （将 X 和 Y 添加至末尾(默认)）", "Result = \"X, C, E\" (A->\"\", B->X, D->\"\")": "结果 = \"X, C, E\" (A->\"\", B->X, D->\"\")", "Result = \"X, Y, A, B, C\" (add X and Y to the beginning (\"Prepend additional tags\" checked))": "结果 = \"X, Y, A, B, C\" （将 X 和 Y 添加至开头(请勾选“将 tags 添加至开头”)）", "Result = \"Y, Y, X, C\" (B->X, A->Y)": "结果 = \"Y, Y, X, C\" (B->X, A->Y)", "Save all changes": "保存所有更改", "Save current settings": "保存当前设置", "Search and Replace for all images displayed.": "搜索并替换所有显示的图像", "Search and Replace in": "在何处搜索和替换", "Search Tags": "检索标签", "Search tags / Filter images by tags": "搜索标签 / 按标签筛选图像", "Search tags / Filter images by tags (INCLUSIVE)": "通过搜索 / 筛选标签来过滤图片（包含）", "selected": "选中的", "Selected Image :": "选择图片", "Selected One": "选择一个", "Selected Tags": "选中的标签", "selected tags from the images displayed.": "来自显示图像中已选择的标签", "Select images from the left gallery.": "从左侧选择图片", "Select visible tags": "选择可见标签", "Show description of how to edit tags": "显示如何编辑标签", "Show only the tags selected in the Positive Filter": "只展示在 Positive 过滤器中的被选中标签", "Sort caption on save": "保存时对描述排序", "Sort tags in the images displayed.": "在已显示的图像中排序标签", "Suffix": "后缀名", "tags from the images displayed.": "显示图像的标签", "Target": "目标", "Target dataset num: ": "目标数据集数量：", "This extension works well with text captions in comma-separated style (such as the tags generated by DeepBooru interrogator).": "使用以逗号分隔的描述会让插件更好的工作。（比如由 DeepBooru  反推生成的标签）", "Use Custom Threshold (Booru)": "使用自定义阈值 (<PERSON><PERSON><PERSON>)", "Use Custom Threshold (WDv1.4 Tagger)": "使用自定义阈值 (WDv1.4 标签器)", "Use Interrogator Caption": "使用反推打标", "Use kohya-ss's finetuning metadata json": "使用 kohya-ss 的微调 metadata json", "Use raw CLIP token to calculate token count (without emphasis or embeddings)": "使用原始 CLIP 词元数计算词元长度（不含 emphasis 或 embeddings）", "Warn if changes in caption is not saved": "在描述的更改未被保存时发出警告", "Adaptive (Gaussian)": "自适应（高斯）", "Adaptive (Mean)": "自适应（平均值）", "➕ Add": "➕ 添加", "Add ALL Displayed": "添加所有当前显示的图片", "Affine": "仿射", "🔄 All Reset": "🔄 全部重置", "Alphabetical Order": "字母顺序", "Amount schedule": "强度计划", "Anime Remove Background": "动漫移除背景模式", "Append": "追加至末尾", "Ascending": "升序", "- Background": "- 背景", "Blend factor max": "最大混合系数", "Blend factor slope": "混合斜率系数", "Block size": "区块大小", "- Body": "- 身体", "- Camera": "- 镜头", "- Canvas Size": "- 画布尺寸", "Checkpoint": "模型", "Checkpoint schedule": "模型表", "Clear ALL filters": "清除所有过滤器", "Clear selection": "清空选择", "Clear tag filters": "标签清除过滤器", "CLIP Skip": "CLIP 终止层数", "CLIP skip schedule": "CLIP 终止层数调度计划", "Closer is brighter": "越近越亮", "Color coherence": "颜色一致性", "Color correction factor": "颜色校正系数", "Color force Grayscale": "强制颜色空间为灰度", "Comp alpha schedule": "透明度组合调度计划", "Comp mask type": "合成蒙版类型", "Comp save extra frames": "保存合成过程所有额外帧", "Contrast schedule": "对比度调度计划", "ControlNet Video Input": "ControlNet 视频输入", "📋 Copy to clipboard": "📋 复制到剪切板", "CRF": "固定码率因子 (CRF)", "Dataset Load Settings": "数据集加载设置", "Dataset Tag Editor": "Dataset Tag Editor", "Decoder Tile Size": "解码器分块大小", "❌ Del": "❌ 删除", "DELETE File(s)": "删除文件", "Delete Imgs": "删除图像", "Depth": "Depth (深度)", "Depth (Midas/Adabins)": "深度模式 (Midas / Adabins)", "Descending": "降序", "Difference": "差分", "💾 Download image": "💾 下载图片", "Edit common tags.": "编辑常见标签", "Elliptic Limbs": "椭圆四肢", "Enable checkpoint scheduling": "启用模型调度计划", "Enable CLIP skip scheduling": "启用 CLIP 终止层数调度", "Enable guided images mode": "启用图像引导模式", "Enable MultiDiffusion": "启用 MultiDiffusion", "Enable sampler scheduling": "启用采样方法调度计划", "Enable steps scheduling": "启用迭代步数调度计划", "Enable Subseed scheduling": "启用第二种子调度计划", "Encoder Color Fix": "编码器颜色修复", "Encoder Tile Size": "编码器分块大小", "End blur width": "结束模糊宽度", "exampls: Change base alpha from 0.1 to 0.9": "例：从 0.1 至 0.9 改变基层 α", "extension to be installed.": " 插件", "Fast Decoder": "使用快速解码器", "Fast Encoder": "使用快速编码器", "First frame as init image": "第一帧作为初始化图像", "Fixed Roll": "固定旋转", "Frequency": "频率", "Full res mask": "全分辨率蒙版", "Full res mask padding": "全分辨率蒙版的填充预留像素宽度", "Generate human masks": "生成人工蒙版", "*Get depth from uploaded video*": "*从上传的视频中获取深度*", "Hybrid composite": "混合合成模式", "Hybrid motion": "混合运动模式", "If you want to display the original model as well for comparison": "如果你同时想显示原本模型以做对比", "Image File": "图像文件", "Image height": "图像高度", "Images to use for keyframe guidance": "用于关键帧引导的图像", "Image strength schedule": "图像强度调度计划", "Image width": "图像宽度", "Interrogate Result": "反推结果", "Interrogate Selected Image": "反推所选图片", "Interrogator Settings": "反推器设置", "interrupt": "中止", "json input path (Optional, only for append results)": "json 输入路径（可选，只用于追加结果）", "json path": "json 路径", "Keep Imgs": "保留原图", "Length": "长度", "Load from subdirectories": "从子目录中加载", "Location": "FFmpeg 所在位置", "Low fps": "低帧率", "Make GIF": "制作 GIF", "Mask brightness adjust": "蒙版亮度调整", "Mask contrast adjust": "蒙版对比度调整", "Mask schedule": "蒙版调度计划", "MiDaS weight (vid2depth)": "MiDaS 权重 (vid2depth)", "Mixed": "混合", "Moved or deleted images will be unloaded.": "被移动或被删除的图片会被卸载。", "Move File(s)": "移动文件", "Move VAE to GPU": "将 VAE 移动到 GPU", "Noise mask schedule": "噪声蒙版调度计划", "Noise schedule": "噪声调度计划", "- Others": "- 其他", "Overlay mask": "覆盖蒙版", "Overwrite": "覆写", "Overwrite extracted frames": "覆盖已有帧", "Overwrite image size": "覆盖图像尺寸", "Parseq Manifest (JSON or URL)": "参数定序器配置文件（JSON 或者链接）", "PNGs": "图片（PNG格式）", "Prefix": "前缀", "Prepend": "添加到开头", "Prepend additional tags": "将额外标签前置", "Requires the": "需要安装 ", "reroll": "回滚", "Reset Tile Size": "重置分块大小", "Sampler": "采样方法", "Sampler schedule": "采样方法调度计划", "Search Text": "搜索文本", "Seed iter N": "种子迭代量 N", "Send this image to ControlNet.": "将图片发送至 ControlNet", "Sigma schedule": "Sigma 调度计划", "Simple": "单一值", "Simple (Auto-value)": "单一值（自动取值）", "Skip video creation": "跳过视频生成", "Sort by": "排序方式", "Sort Order": "排序方式", "Sort tags": "排序标签", "Soundtrack path": "音轨路径", "Step": "迭代步数", "Steps schedule": "迭代步数调度计划", "Store frames in ram": "将帧保存到内存", "Subseed schedule": "第二种子调度计划", "Subseed strength schedule": "第二种子强度调度计划", "Target size type": "目标尺寸类型", "Thresholding Mode": "阈值模式", "Threshold schedule": "阈值调度计划", "Threshold Value Lower": "阈值下限", "Threshold Value Upper": "阈值上限", "Token Length": "词元长度", "Transform Center Y": "旋转中心点 Y 轴", "Truncate tags by token count": "按词元长度截断标签", "Truncate tags by token count.": "按词元长度截断标签", "Tweening frames schedule": "中间计算帧调度计划", "uploaded video FPS": "上传视频的帧率", "Upscale factor": "放大倍数", "Upscale model": "超分模型", "Use regex": "使用正则表达式", "WDv1.4 Tagger Score Threshold": "WDv1.4 标签器评分阈值", "Will upscale the image by the selected scale factor; use width and height sliders to set tile size": "按照选定的比例放大图像；使用长/宽拖动条以设置分块大小", "Will upscale the image depending on the selected target size type": "根据选定的目标尺寸类型放大图片"}