"""
EvoTune Agent Attention机制
实现公式(5-7)的49-token空间注意力和时间步感知调制
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Tuple, Optional
import math
import logging

logger = logging.getLogger(__name__)

class AgentAttention(nn.Module):
    """
    Agent Attention机制
    使用49个agent tokens实现高效的空间注意力
    """
    
    def __init__(self, 
                 channels: int,
                 num_agents: int = 49,
                 agent_dim: int = 64,
                 dropout: float = 0.1):
        """
        初始化Agent Attention
        
        Args:
            channels: 输入特征通道数
            num_agents: agent token数量 (默认49 = 7x7)
            agent_dim: agent token维度
            dropout: dropout概率
        """
        super().__init__()
        
        self.channels = channels
        self.num_agents = num_agents
        self.agent_dim = agent_dim
        self.sqrt_agent_dim = math.sqrt(agent_dim)
        
        # Agent token生成网络
        self.agent_generator = nn.Sequential(
            nn.AdaptiveAvgPool2d((7, 7)),  # 生成7x7=49个区域
            nn.Conv2d(channels, agent_dim, 1),
            nn.LayerNorm([agent_dim, 7, 7]),
            nn.ReLU(inplace=True),
            nn.Conv2d(agent_dim, agent_dim, 1)
        )
        
        # Agent间注意力
        self.agent_q = nn.Linear(agent_dim, agent_dim)
        self.agent_k = nn.Linear(agent_dim, agent_dim)
        self.agent_v = nn.Linear(agent_dim, agent_dim)
        
        # 空间注意力投影
        self.spatial_q = nn.Conv2d(channels, agent_dim, 1)
        self.spatial_k = nn.Conv2d(agent_dim, agent_dim, 1)
        self.spatial_v = nn.Conv2d(channels, channels, 1)
        
        # 输出投影
        self.output_proj = nn.Conv2d(channels, channels, 1)
        
        # Dropout
        self.dropout = nn.Dropout(dropout)
        
        # 初始化权重
        self._init_weights()
        
        logger.info(f"AgentAttention initialized: {channels}→{agent_dim}, {num_agents} agents")
    
    def _init_weights(self):
        """初始化网络权重"""
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    nn.init.zeros_(m.bias)
            elif isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.zeros_(m.bias)
    
    def generate_agent_tokens(self, x: torch.Tensor) -> torch.Tensor:
        """
        公式(5): Agent token生成
        A = AdaptivePool(Reshape(H_encoder))
        
        Args:
            x: 输入特征 [B, C, H, W]
            
        Returns:
            agents: Agent tokens [B, num_agents, agent_dim]
        """
        B, C, H, W = x.shape
        
        # 生成agent tokens [B, agent_dim, 7, 7]
        agents = self.agent_generator(x)
        
        # 重塑为序列格式 [B, num_agents, agent_dim]
        agents = agents.view(B, self.agent_dim, -1).transpose(1, 2)
        
        return agents
    
    def compute_agent_attention(self, agents: torch.Tensor) -> torch.Tensor:
        """
        Agent间注意力计算
        A_agent = softmax(A·Q @ A·K^T / √d) @ A·V
        
        Args:
            agents: Agent tokens [B, num_agents, agent_dim]
            
        Returns:
            attended_agents: 注意力增强的agent tokens [B, num_agents, agent_dim]
        """
        B, N, D = agents.shape
        
        # 计算Q, K, V
        q = self.agent_q(agents)  # [B, N, D]
        k = self.agent_k(agents)  # [B, N, D]
        v = self.agent_v(agents)  # [B, N, D]
        
        # 计算注意力分数
        attn_scores = torch.matmul(q, k.transpose(-2, -1)) / self.sqrt_agent_dim  # [B, N, N]
        attn_weights = F.softmax(attn_scores, dim=-1)
        attn_weights = self.dropout(attn_weights)
        
        # 应用注意力
        attended_agents = torch.matmul(attn_weights, v)  # [B, N, D]
        
        return attended_agents
    
    def compute_spatial_attention(self, x: torch.Tensor, agents: torch.Tensor) -> torch.Tensor:
        """
        空间注意力计算
        A_spatial = softmax(H·Q @ A·K^T / √d)
        
        Args:
            x: 输入特征 [B, C, H, W]
            agents: Agent tokens [B, num_agents, agent_dim]
            
        Returns:
            spatial_attn: 空间注意力权重 [B, H*W, num_agents]
        """
        B, C, H, W = x.shape
        
        # 计算空间查询
        spatial_q = self.spatial_q(x)  # [B, agent_dim, H, W]
        spatial_q = spatial_q.view(B, self.agent_dim, H * W).transpose(1, 2)  # [B, H*W, agent_dim]
        
        # 计算agent键
        agent_k = self.spatial_k(agents.transpose(1, 2).view(B, self.agent_dim, 7, 7))  # [B, agent_dim, 7, 7]
        agent_k = agent_k.view(B, self.agent_dim, -1).transpose(1, 2)  # [B, num_agents, agent_dim]
        
        # 计算注意力分数
        attn_scores = torch.matmul(spatial_q, agent_k.transpose(-2, -1)) / self.sqrt_agent_dim  # [B, H*W, num_agents]
        spatial_attn = F.softmax(attn_scores, dim=-1)
        spatial_attn = self.dropout(spatial_attn)
        
        return spatial_attn
    
    def apply_spatial_attention(self, x: torch.Tensor, spatial_attn: torch.Tensor, agents: torch.Tensor) -> torch.Tensor:
        """
        应用空间注意力
        Y = A_spatial · (A_agent · V) + V
        
        Args:
            x: 输入特征 [B, C, H, W]
            spatial_attn: 空间注意力权重 [B, H*W, num_agents]
            agents: 增强的agent tokens [B, num_agents, agent_dim]
            
        Returns:
            output: 注意力增强的特征 [B, C, H, W]
        """
        B, C, H, W = x.shape
        
        # 计算空间值
        spatial_v = self.spatial_v(x)  # [B, C, H, W]
        spatial_v_flat = spatial_v.view(B, C, H * W).transpose(1, 2)  # [B, H*W, C]
        
        # Agent tokens投影到输出空间
        agent_values = F.adaptive_avg_pool2d(
            agents.transpose(1, 2).view(B, self.agent_dim, 7, 7), 
            (1, 1)
        ).view(B, self.agent_dim)  # [B, agent_dim]
        
        # 扩展agent values到空间维度
        agent_values_expanded = agent_values.unsqueeze(1).expand(B, self.num_agents, self.agent_dim)  # [B, num_agents, agent_dim]
        
        # 应用空间注意力
        attended_features = torch.matmul(spatial_attn, agent_values_expanded)  # [B, H*W, agent_dim]
        
        # 投影到原始通道数
        if self.agent_dim != C:
            proj_layer = nn.Linear(self.agent_dim, C, device=x.device, dtype=x.dtype)
            attended_features = proj_layer(attended_features)
        
        # 重塑回空间格式
        attended_features = attended_features.transpose(1, 2).view(B, C, H, W)  # [B, C, H, W]
        
        # 残差连接
        output = attended_features + spatial_v
        
        return output
    
    def forward(self, x: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        Agent Attention前向传播
        
        Args:
            x: 输入特征 [B, C, H, W]
            
        Returns:
            output: 注意力增强的特征 [B, C, H, W]
            agents: Agent tokens [B, num_agents, agent_dim]
            spatial_attn: 空间注意力权重 [B, H*W, num_agents]
        """
        # 公式(5): 生成agent tokens
        agents = self.generate_agent_tokens(x)
        
        # Agent间注意力
        attended_agents = self.compute_agent_attention(agents)
        
        # 公式(6): 计算空间注意力
        spatial_attn = self.compute_spatial_attention(x, attended_agents)
        
        # 公式(7a): 应用注意力
        output = self.apply_spatial_attention(x, spatial_attn, attended_agents)
        
        # 输出投影
        output = self.output_proj(output)
        
        return output, attended_agents, spatial_attn


class TimestepAwareAgentAttention(nn.Module):
    """
    时间步感知的Agent Attention
    实现公式(7b)的时间步调制
    """
    
    def __init__(self, 
                 channels: int,
                 num_agents: int = 49,
                 agent_dim: int = 64,
                 dropout: float = 0.1):
        """
        初始化时间步感知Agent Attention
        
        Args:
            channels: 输入特征通道数
            num_agents: agent token数量
            agent_dim: agent token维度
            dropout: dropout概率
        """
        super().__init__()
        
        self.agent_attention = AgentAttention(
            channels=channels,
            num_agents=num_agents,
            agent_dim=agent_dim,
            dropout=dropout
        )
        
        logger.info(f"TimestepAwareAgentAttention initialized for {channels} channels")
    
    def forward(self, x: torch.Tensor, w_attn: torch.Tensor) -> torch.Tensor:
        """
        时间步感知的Agent Attention前向传播
        
        公式(7a): Y = A_spatial · (A_agent · V) + V
        公式(7b): Y_out = Y × w_attn(t)
        
        Args:
            x: 输入特征 [B, C, H, W]
            w_attn: 时间步感知的注意力权重 [B] or scalar
            
        Returns:
            output: 时间步调制后的特征 [B, C, H, W]
        """
        # 公式(7a): Agent Attention处理
        y, agents, spatial_attn = self.agent_attention(x)
        
        # 公式(7b): 时间步感知调制
        if isinstance(w_attn, torch.Tensor):
            if w_attn.dim() == 0:  # scalar
                y_out = y * w_attn
            else:  # [B]
                w_attn = w_attn.view(-1, 1, 1, 1)  # [B, 1, 1, 1]
                y_out = y * w_attn
        else:  # float
            y_out = y * w_attn
        
        return y_out
