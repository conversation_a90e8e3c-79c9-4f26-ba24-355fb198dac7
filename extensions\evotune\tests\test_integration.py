"""
EvoTune集成测试
测试完整的图像生成质量验证流程
"""

import unittest
import torch
import torch.nn as nn
import numpy as np
import sys
import os
from typing import Dict, Any, List
import time
import json

# 添加模块路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)

from modules.evotune_wrapper import EvoTuneWrapper, wrap_unet_with_evotune
from modules.evotune_controller import EvoTuneController

class MockUNet(nn.Module):
    """模拟UNet用于测试"""
    
    def __init__(self, channels=320):
        super().__init__()
        self.channels = channels
        
        # 模拟编码器
        self.down_blocks = nn.ModuleList([
            nn.Conv2d(4, channels, 3, padding=1),
            nn.Conv2d(channels, channels*2, 3, padding=1),
            nn.Conv2d(channels*2, channels*4, 3, padding=1)
        ])
        
        # 模拟中间块
        self.mid_block = nn.Sequential(
            nn.Conv2d(channels*4, channels*4, 3, padding=1),
            nn.ReLU(),
            nn.Conv2d(channels*4, channels*4, 3, padding=1)
        )
        
        # 模拟解码器
        self.up_blocks = nn.ModuleList([
            nn.ConvTranspose2d(channels*4, channels*2, 3, padding=1),
            nn.ConvTranspose2d(channels*2, channels, 3, padding=1),
            nn.ConvTranspose2d(channels, 4, 3, padding=1)
        ])
        
        # 时间步嵌入
        self.time_embed = nn.Linear(320, channels*4)
    
    def forward(self, sample, timestep, encoder_hidden_states=None, **kwargs):
        """模拟UNet前向传播"""
        B, C, H, W = sample.shape
        
        # 时间步嵌入
        if isinstance(timestep, (int, float)):
            timestep = torch.tensor([timestep] * B, device=sample.device)
        
        t_emb = self.time_embed(torch.randn(B, 320, device=sample.device))
        t_emb = t_emb.view(B, -1, 1, 1)
        
        # 编码器
        h = sample
        skip_connections = []
        
        for i, down_block in enumerate(self.down_blocks):
            h = down_block(h)
            if i < len(self.down_blocks) - 1:
                skip_connections.append(h)
                h = nn.functional.avg_pool2d(h, 2)
        
        # 中间块
        h = self.mid_block(h) + t_emb
        
        # 解码器
        for i, up_block in enumerate(self.up_blocks):
            if i > 0 and skip_connections:
                skip = skip_connections.pop()
                h = nn.functional.interpolate(h, size=skip.shape[-2:])
                h = torch.cat([h, skip], dim=1)
                h = nn.Conv2d(h.shape[1], h.shape[1]//2, 1, device=h.device)(h)
            
            h = up_block(h)
            if i < len(self.up_blocks) - 1:
                h = nn.functional.interpolate(h, scale_factor=2)
        
        return h


class TestEvoTuneWrapper(unittest.TestCase):
    """测试EvoTune包装器"""
    
    def setUp(self):
        self.mock_unet = MockUNet()
        self.wrapper = EvoTuneWrapper(
            self.mock_unet,
            enabled=True,
            target_channels=320,
            performance_monitoring=True
        )
    
    def test_wrapper_initialization(self):
        """测试包装器初始化"""
        self.assertIsNotNone(self.wrapper.evotune_controller)
        self.assertTrue(self.wrapper.enabled)
        self.assertEqual(self.wrapper.target_channels, 320)
    
    def test_enable_disable(self):
        """测试启用/禁用功能"""
        # 测试禁用
        self.wrapper.disable_evotune()
        self.assertFalse(self.wrapper.enabled)
        
        # 测试启用
        self.wrapper.enable_evotune()
        self.assertTrue(self.wrapper.enabled)
    
    def test_forward_pass(self):
        """测试前向传播"""
        batch_size = 2
        sample = torch.randn(batch_size, 4, 64, 64)
        timestep = torch.tensor([100, 800])
        
        # 测试启用EvoTune的前向传播
        output_enabled = self.wrapper(sample, timestep)
        self.assertEqual(output_enabled.shape, sample.shape)
        
        # 测试禁用EvoTune的前向传播
        self.wrapper.disable_evotune()
        output_disabled = self.wrapper(sample, timestep)
        self.assertEqual(output_disabled.shape, sample.shape)
    
    def test_performance_monitoring(self):
        """测试性能监控"""
        sample = torch.randn(2, 4, 64, 64)
        timestep = torch.tensor([100, 800])
        
        # 执行几次前向传播
        for _ in range(5):
            _ = self.wrapper(sample, timestep)
        
        # 检查性能统计
        stats = self.wrapper.get_performance_stats()
        self.assertIn('total_calls', stats)
        self.assertGreater(stats['total_calls'], 0)
    
    def test_modulation_points_config(self):
        """测试调制点配置"""
        new_config = {
            'mid_block': {'type': 'backbone', 'enabled': False, 'priority': 1}
        }
        
        self.wrapper.configure_modulation_points(new_config)
        status = self.wrapper.get_modulation_status()
        
        self.assertIn('modulation_points', status)
        self.assertFalse(status['modulation_points']['mid_block']['enabled'])


class TestImageGenerationQuality(unittest.TestCase):
    """测试图像生成质量"""
    
    def setUp(self):
        self.mock_unet = MockUNet()
        self.wrapper_enabled = EvoTuneWrapper(self.mock_unet, enabled=True)
        self.wrapper_disabled = EvoTuneWrapper(self.mock_unet, enabled=False)
    
    def test_output_consistency(self):
        """测试输出一致性"""
        sample = torch.randn(1, 4, 64, 64)
        timestep = torch.tensor([500])
        
        # 多次运行，检查输出一致性
        outputs = []
        for _ in range(3):
            torch.manual_seed(42)  # 固定随机种子
            output = self.wrapper_enabled(sample, timestep)
            outputs.append(output)
        
        # 检查输出是否一致（在相同随机种子下）
        for i in range(1, len(outputs)):
            torch.testing.assert_close(outputs[0], outputs[i], atol=1e-5, rtol=1e-5)
    
    def test_timestep_sensitivity(self):
        """测试时间步敏感性"""
        sample = torch.randn(1, 4, 64, 64)
        
        # 测试不同时间步的输出
        timesteps = [100, 500, 900]
        outputs = []
        
        for t in timesteps:
            output = self.wrapper_enabled(sample, torch.tensor([t]))
            outputs.append(output)
        
        # 检查不同时间步产生不同输出
        for i in range(len(outputs)):
            for j in range(i+1, len(outputs)):
                # 输出应该有显著差异
                diff = torch.abs(outputs[i] - outputs[j]).mean()
                self.assertGreater(diff.item(), 1e-3)
    
    def test_batch_processing(self):
        """测试批处理"""
        batch_sizes = [1, 2, 4, 8]
        
        for batch_size in batch_sizes:
            sample = torch.randn(batch_size, 4, 64, 64)
            timestep = torch.randint(0, 1000, (batch_size,))
            
            output = self.wrapper_enabled(sample, timestep)
            self.assertEqual(output.shape[0], batch_size)
            self.assertEqual(output.shape[1:], sample.shape[1:])
    
    def test_memory_efficiency(self):
        """测试内存效率"""
        if not torch.cuda.is_available():
            self.skipTest("CUDA not available")
        
        device = torch.device('cuda')
        self.wrapper_enabled.to(device)
        
        sample = torch.randn(4, 4, 128, 128, device=device)
        timestep = torch.tensor([100, 300, 600, 900], device=device)
        
        # 记录初始内存
        torch.cuda.empty_cache()
        initial_memory = torch.cuda.memory_allocated()
        
        # 执行前向传播
        output = self.wrapper_enabled(sample, timestep)
        
        # 记录峰值内存
        peak_memory = torch.cuda.max_memory_allocated()
        memory_usage = peak_memory - initial_memory
        
        # 检查内存使用是否合理（应该小于2GB）
        self.assertLess(memory_usage, 2 * 1024**3)
        
        # 清理
        del output
        torch.cuda.empty_cache()


class TestPerformanceBenchmark(unittest.TestCase):
    """性能基准测试"""
    
    def setUp(self):
        self.mock_unet = MockUNet()
        self.wrapper_enabled = EvoTuneWrapper(self.mock_unet, enabled=True)
        self.wrapper_disabled = EvoTuneWrapper(self.mock_unet, enabled=False)
    
    def test_performance_overhead(self):
        """测试性能开销"""
        sample = torch.randn(2, 4, 64, 64)
        timestep = torch.tensor([100, 800])
        
        # 预热
        for _ in range(5):
            _ = self.wrapper_enabled(sample, timestep)
            _ = self.wrapper_disabled(sample, timestep)
        
        # 测试启用EvoTune的性能
        start_time = time.time()
        for _ in range(20):
            _ = self.wrapper_enabled(sample, timestep)
        enabled_time = time.time() - start_time
        
        # 测试禁用EvoTune的性能
        start_time = time.time()
        for _ in range(20):
            _ = self.wrapper_disabled(sample, timestep)
        disabled_time = time.time() - start_time
        
        # 计算开销
        overhead = (enabled_time - disabled_time) / disabled_time * 100
        
        print(f"EvoTune overhead: {overhead:.2f}%")
        
        # 开销应该在合理范围内（<50%）
        self.assertLess(overhead, 50.0)
    
    def test_scalability(self):
        """测试可扩展性"""
        image_sizes = [(32, 32), (64, 64), (128, 128)]
        times = []
        
        for h, w in image_sizes:
            sample = torch.randn(1, 4, h, w)
            timestep = torch.tensor([500])
            
            # 预热
            _ = self.wrapper_enabled(sample, timestep)
            
            # 测试时间
            start_time = time.time()
            for _ in range(10):
                _ = self.wrapper_enabled(sample, timestep)
            avg_time = (time.time() - start_time) / 10
            times.append(avg_time)
            
            print(f"Size {h}x{w}: {avg_time:.4f}s")
        
        # 检查时间复杂度是否合理（应该大致与像素数成正比）
        pixel_ratios = [h*w / (32*32) for h, w in image_sizes]
        time_ratios = [t / times[0] for t in times]
        
        # 时间比例应该与像素比例相关
        for i in range(1, len(pixel_ratios)):
            self.assertLess(time_ratios[i] / pixel_ratios[i], 2.0)


class TestErrorHandling(unittest.TestCase):
    """测试错误处理"""
    
    def setUp(self):
        self.mock_unet = MockUNet()
        self.wrapper = EvoTuneWrapper(self.mock_unet, enabled=True)
    
    def test_invalid_input_shapes(self):
        """测试无效输入形状"""
        # 测试错误的通道数
        sample = torch.randn(1, 3, 64, 64)  # 应该是4通道
        timestep = torch.tensor([500])
        
        # 应该能处理或给出合理错误
        try:
            output = self.wrapper(sample, timestep)
            # 如果成功，检查输出形状
            self.assertEqual(output.shape[0], 1)
        except Exception as e:
            # 如果失败，应该是合理的错误
            self.assertIsInstance(e, (RuntimeError, ValueError))
    
    def test_extreme_timesteps(self):
        """测试极端时间步"""
        sample = torch.randn(1, 4, 64, 64)
        
        # 测试边界时间步
        extreme_timesteps = [0, 999, -1, 1000, 1500]
        
        for t in extreme_timesteps:
            try:
                output = self.wrapper(sample, torch.tensor([t]))
                self.assertEqual(output.shape, sample.shape)
            except Exception as e:
                # 应该优雅地处理错误
                self.assertIsInstance(e, (RuntimeError, ValueError))
    
    def test_memory_pressure(self):
        """测试内存压力情况"""
        if not torch.cuda.is_available():
            self.skipTest("CUDA not available")
        
        device = torch.device('cuda')
        self.wrapper.to(device)
        
        # 尝试处理大批次
        try:
            large_sample = torch.randn(16, 4, 256, 256, device=device)
            timestep = torch.randint(0, 1000, (16,), device=device)
            
            output = self.wrapper(large_sample, timestep)
            self.assertEqual(output.shape, large_sample.shape)
            
        except RuntimeError as e:
            # 内存不足是可以接受的
            if "out of memory" in str(e).lower():
                self.skipTest("Insufficient GPU memory")
            else:
                raise


def create_test_report(results: Dict[str, Any]) -> str:
    """创建测试报告"""
    report = []
    report.append("# EvoTune Integration Test Report")
    report.append(f"Generated at: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    report.append("")
    
    # 测试结果摘要
    report.append("## Test Summary")
    report.append(f"- Total tests: {results['total_tests']}")
    report.append(f"- Passed: {results['passed']}")
    report.append(f"- Failed: {results['failed']}")
    report.append(f"- Success rate: {results['success_rate']:.1f}%")
    report.append("")
    
    # 性能基准
    if 'performance' in results:
        report.append("## Performance Benchmark")
        perf = results['performance']
        report.append(f"- EvoTune overhead: {perf.get('overhead', 'N/A')}")
        report.append(f"- Memory usage: {perf.get('memory_usage', 'N/A')}")
        report.append(f"- Scalability: {perf.get('scalability', 'N/A')}")
        report.append("")
    
    # 错误详情
    if results['failed'] > 0 and 'errors' in results:
        report.append("## Error Details")
        for error in results['errors']:
            report.append(f"- {error}")
        report.append("")
    
    # 建议
    report.append("## Recommendations")
    if results['success_rate'] >= 95:
        report.append("- ✅ EvoTune is ready for production use")
    elif results['success_rate'] >= 80:
        report.append("- ⚠️ EvoTune needs minor fixes before production")
    else:
        report.append("- ❌ EvoTune requires significant fixes")
    
    return "\n".join(report)


def run_integration_tests():
    """运行集成测试"""
    test_classes = [
        TestEvoTuneWrapper,
        TestImageGenerationQuality,
        TestPerformanceBenchmark,
        TestErrorHandling
    ]
    
    results = {
        'total_tests': 0,
        'passed': 0,
        'failed': 0,
        'errors': [],
        'performance': {}
    }
    
    for test_class in test_classes:
        suite = unittest.TestLoader().loadTestsFromTestCase(test_class)
        runner = unittest.TextTestRunner(verbosity=2, stream=open(os.devnull, 'w'))
        result = runner.run(suite)
        
        results['total_tests'] += result.testsRun
        results['passed'] += result.testsRun - len(result.failures) - len(result.errors)
        results['failed'] += len(result.failures) + len(result.errors)
        
        # 收集错误信息
        for failure in result.failures:
            results['errors'].append(f"{failure[0]}: {failure[1]}")
        for error in result.errors:
            results['errors'].append(f"{error[0]}: {error[1]}")
    
    results['success_rate'] = (results['passed'] / results['total_tests']) * 100
    
    # 生成报告
    report = create_test_report(results)
    
    # 保存报告
    with open('evotune_test_report.md', 'w') as f:
        f.write(report)
    
    print(report)
    return results['success_rate'] >= 80


if __name__ == '__main__':
    success = run_integration_tests()
    if success:
        print("\n✅ Integration tests passed!")
    else:
        print("\n❌ Integration tests failed!")
        sys.exit(1)
