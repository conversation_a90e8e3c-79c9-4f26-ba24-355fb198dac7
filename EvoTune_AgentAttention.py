"""
EvoTune集成Agent Attention模块
专门针对水油场景的注意力机制优化
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Optional, Tuple

# 从原始Agent Attention代码中提取核心功能
def img2windows(img, H_sp, W_sp):
    """将图像分割为窗口"""
    B, C, H, W = img.shape
    img_reshape = img.view(B, C, H // H_sp, H_sp, W // W_sp, W_sp)
    img_perm = img_reshape.permute(0, 2, 4, 3, 5, 1).contiguous().reshape(-1, H_sp * W_sp, C)
    return img_perm

def windows2img(img_splits_hw, H_sp, W_sp, H, W):
    """将窗口重组为图像"""
    B = int(img_splits_hw.shape[0] / (H * W / H_sp / W_sp))
    img = img_splits_hw.view(B, H // H_sp, W // W_sp, H_sp, W_sp, -1)
    img = img.permute(0, 1, 3, 2, 4, 5).contiguous().view(B, H, W, -1)
    return img

class WaterOilAgentAttention(nn.Module):
    """
    针对水油场景优化的Agent Attention
    
    核心创新：
    1. 水平分割模式 - 适应水油的水平分布特征
    2. 自适应agent数量 - 根据水油区域动态调整
    3. 时间步感知 - 不同扩散阶段使用不同注意力强度
    """
    
    def __init__(self, dim, resolution, num_heads=8, agent_num=49, 
                 water_oil_mode=True, attn_drop=0., proj_drop=0.):
        super().__init__()
        self.dim = dim
        self.resolution = resolution
        self.num_heads = num_heads
        self.agent_num = agent_num
        self.water_oil_mode = water_oil_mode
        
        head_dim = dim // num_heads
        self.scale = head_dim ** -0.5
        
        # 针对水油场景的分割策略
        if water_oil_mode:
            # 确保分割尺寸能整除分辨率
            self.split_size = min(7, resolution)
            # 找到合适的分割尺寸
            for size in [8, 4, 2, 1]:
                if resolution % size == 0:
                    self.split_size = size
                    break
            self.H_sp = self.W_sp = self.split_size
        else:
            # 标准分割
            self.split_size = 7
            self.H_sp = self.W_sp = self.split_size
        
        # 卷积获取V
        self.get_v = nn.Conv2d(dim, dim, kernel_size=3, stride=1, padding=1, groups=dim)
        
        # QK投影
        self.qk = nn.Linear(dim, dim * 2, bias=False)
        self.proj = nn.Linear(dim, dim)
        
        # Dropout
        self.attn_drop = nn.Dropout(attn_drop)
        self.proj_drop = nn.Dropout(proj_drop)
        
        # 位置偏置 - 针对水油场景优化
        self.agent_bias = nn.Parameter(torch.zeros(num_heads, agent_num, self.H_sp, self.W_sp))
        self.spatial_bias = nn.Parameter(torch.zeros(num_heads, self.H_sp * self.W_sp, agent_num))
        
        # 初始化
        nn.init.trunc_normal_(self.agent_bias, std=0.02)
        nn.init.trunc_normal_(self.spatial_bias, std=0.02)
        
        # Agent池化
        pool_size = int(agent_num ** 0.5)
        self.pool = nn.AdaptiveAvgPool2d(output_size=(pool_size, pool_size))
        self.softmax = nn.Softmax(dim=-1)
        
        # 水油特征增强器
        self.water_oil_enhancer = nn.Sequential(
            nn.Conv2d(dim, dim, 3, padding=1, groups=dim//4),
            nn.GELU(),
            nn.Conv2d(dim, dim, 1),
            nn.Dropout(0.1)
        )
        
    def im2cswin(self, x):
        """图像到窗口转换"""
        B, N, C = x.shape
        H = W = int(np.sqrt(N))
        x = x.transpose(-2, -1).contiguous().view(B, C, H, W)
        x = img2windows(x, self.H_sp, self.W_sp)
        return x
    
    def get_lepe(self, x, func):
        """获取位置编码"""
        B, N, C = x.shape
        H = W = int(np.sqrt(N))
        x = x.transpose(-2, -1).contiguous().view(B, C, H, W)
        
        H_sp, W_sp = self.H_sp, self.W_sp
        x = x.view(B, C, H // H_sp, H_sp, W // W_sp, W_sp)
        x = x.permute(0, 2, 4, 1, 3, 5).contiguous().reshape(-1, C, H_sp, W_sp)
        
        lepe = func(x)
        lepe = lepe.reshape(-1, C, H_sp * W_sp).permute(0, 2, 1).contiguous()
        x = x.reshape(-1, C, H_sp * W_sp).permute(0, 2, 1).contiguous()
        
        return x, lepe
    
    def forward(self, x, timestep_weight=1.0):
        """
        前向传播
        
        Args:
            x: 输入特征 [B, C, H, W]
            timestep_weight: 时间步权重，控制注意力强度
        """
        B, C, H, W = x.shape
        
        # 转换为序列格式
        x_seq = x.flatten(2).transpose(1, 2)  # [B, H*W, C]
        
        # 窗口化处理
        q = k = self.im2cswin(x_seq)  # [B', H_sp*W_sp, C]
        v, lepe = self.get_lepe(x_seq, self.get_v)
        
        b, n, c = q.shape
        num_heads, head_dim = self.num_heads, self.dim // self.num_heads
        
        # 生成agent tokens
        agent_tokens = self.pool(
            q.reshape(b, self.H_sp, self.W_sp, c).permute(0, 3, 1, 2)
        ).reshape(b, c, -1).permute(0, 2, 1)
        
        # 重塑为多头格式
        q = q.reshape(b, n, num_heads, head_dim).permute(0, 2, 1, 3)
        k = k.reshape(b, n, num_heads, head_dim).permute(0, 2, 1, 3)
        v = v.reshape(b, n, num_heads, head_dim).permute(0, 2, 1, 3)
        agent_tokens = agent_tokens.reshape(b, self.agent_num, num_heads, head_dim).permute(0, 2, 1, 3)
        
        # Agent到空间的注意力
        agent_bias = F.interpolate(
            self.agent_bias, size=(self.H_sp, self.W_sp), mode='bilinear'
        ).reshape(1, num_heads, self.agent_num, -1).repeat(b, 1, 1, 1)
        
        agent_attn = self.softmax(
            (agent_tokens * self.scale) @ k.transpose(-2, -1) + agent_bias
        )
        agent_attn = self.attn_drop(agent_attn)
        agent_v = agent_attn @ v
        
        # 空间到Agent的注意力
        spatial_bias = self.spatial_bias.repeat(b, 1, 1, 1)
        q_attn = self.softmax(
            (q * self.scale) @ agent_tokens.transpose(-2, -1) + spatial_bias
        )
        q_attn = self.attn_drop(q_attn)
        
        # 最终输出
        x_out = q_attn @ agent_v
        x_out = x_out.transpose(1, 2).reshape(b, n, c)
        x_out = x_out + lepe
        
        # 转换回图像格式
        x_out = windows2img(x_out, self.H_sp, self.W_sp, H, W).view(B, -1, C)
        x_out = x_out.transpose(1, 2).reshape(B, C, H, W)
        
        # 投影
        x_out_flat = x_out.flatten(2).transpose(1, 2)
        x_out_flat = self.proj(x_out_flat)
        x_out_flat = self.proj_drop(x_out_flat)
        x_out = x_out_flat.transpose(1, 2).reshape(B, C, H, W)
        
        # 水油特征增强
        enhanced = self.water_oil_enhancer(x_out)
        
        # 根据时间步权重混合
        output = x + timestep_weight * enhanced
        
        return output


class MultiScaleWaterOilAttention(nn.Module):
    """
    多尺度水油注意力模块
    在不同分辨率下应用注意力机制
    """
    
    def __init__(self, dim, resolutions=[64, 32, 16], num_heads=8):
        super().__init__()
        self.attentions = nn.ModuleList([
            WaterOilAgentAttention(
                dim=dim, 
                resolution=res, 
                num_heads=num_heads,
                agent_num=min(49, res*res//4)  # 根据分辨率调整agent数量
            ) for res in resolutions
        ])
        
        # 多尺度融合
        self.fusion = nn.Conv2d(dim * len(resolutions), dim, 1)
        
    def forward(self, x, timestep_weight=1.0):
        """多尺度注意力处理"""
        B, C, H, W = x.shape
        
        outputs = []
        for attention in self.attentions:
            # 调整输入尺寸到对应分辨率
            target_size = attention.resolution
            if H != target_size or W != target_size:
                x_resized = F.interpolate(x, size=(target_size, target_size), mode='bilinear', align_corners=False)
            else:
                x_resized = x
                
            # 应用注意力
            out = attention(x_resized, timestep_weight)
            
            # 调整回原始尺寸
            if out.shape[-2:] != (H, W):
                out = F.interpolate(out, size=(H, W), mode='bilinear', align_corners=False)
                
            outputs.append(out)
        
        # 融合多尺度特征
        fused = torch.cat(outputs, dim=1)
        fused = self.fusion(fused)
        
        return fused


# 测试代码
if __name__ == "__main__":
    print("测试水油Agent Attention模块...")
    
    # 单尺度测试
    attention = WaterOilAgentAttention(dim=64, resolution=32, num_heads=8)
    x = torch.randn(2, 64, 32, 32)
    
    print(f"输入形状: {x.shape}")
    
    # 测试不同时间步权重
    for weight in [0.5, 1.0, 1.5]:
        out = attention(x, timestep_weight=weight)
        print(f"时间步权重{weight}: 输出形状 {out.shape}")
    
    # 多尺度测试
    print("\n测试多尺度注意力...")
    multi_attention = MultiScaleWaterOilAttention(dim=64, resolutions=[64, 32, 16])
    x_large = torch.randn(2, 64, 64, 64)
    
    out_multi = multi_attention(x_large, timestep_weight=1.0)
    print(f"多尺度输入: {x_large.shape} -> 输出: {out_multi.shape}")
    
    print("✅ Agent Attention模块测试完成!")
