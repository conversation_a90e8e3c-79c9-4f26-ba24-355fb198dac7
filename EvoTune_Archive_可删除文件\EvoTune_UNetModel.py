import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.fft as fft
import numpy as np
from typing import Optional, Tuple, Dict, Any
import math

# 导入Agent Attention相关模块
from einops.layers.torch import Rearrange
from timm.models.layers import DropPath, to_2tuple, trunc_normal_


def Fourier_filter(x, threshold, scale):
    """
    改进的傅里叶滤波器，针对水面反射和油污纹理特征优化
    """
    # FFT
    x_freq = fft.fftn(x, dim=(-2, -1))
    x_freq = fft.fftshift(x_freq, dim=(-2, -1))

    B, C, H, W = x_freq.shape
    mask = torch.ones((B, C, H, W), device=x.device)

    crow, ccol = H // 2, W // 2
    mask[..., crow - threshold:crow + threshold, ccol - threshold:ccol + threshold] = scale
    x_freq = x_freq * mask

    # IFFT
    x_freq = fft.ifftshift(x_freq, dim=(-2, -1))
    x_filtered = fft.ifftn(x_freq, dim=(-2, -1)).real

    return x_filtered


def adaptive_fourier_filter(x, timestep, threshold_base=7, scale_base=0.8):
    """
    自适应傅里叶滤波器，根据时间步动态调整滤波参数
    针对不同扩散阶段的水面和油污特征进行优化
    """
    # 根据时间步调整滤波参数
    # 早期时间步(高噪声)：更强的低频保留，突出整体结构
    # 后期时间步(低噪声)：更多高频细节，增强纹理
    timestep_norm = timestep.float() / 1000.0  # 假设最大时间步为1000

    # 动态调整阈值和缩放因子
    threshold = threshold_base + int(5 * (1 - timestep_norm.mean()))
    scale = scale_base + 0.3 * timestep_norm.mean()

    return Fourier_filter(x, threshold, scale)


def img2windows(img, H_sp, W_sp):
    """
    将图像分割为窗口，用于Agent Attention处理
    """
    B, C, H, W = img.shape
    img_reshape = img.view(B, C, H // H_sp, H_sp, W // W_sp, W_sp)
    img_perm = img_reshape.permute(0, 2, 4, 3, 5, 1).contiguous().reshape(-1, H_sp * W_sp, C)
    return img_perm


def windows2img(img_splits_hw, H_sp, W_sp, H, W):
    """
    将窗口重组为图像
    """
    B = int(img_splits_hw.shape[0] / (H * W / H_sp / W_sp))
    img = img_splits_hw.view(B, H // H_sp, W // W_sp, H_sp, W_sp, -1)
    img = img.permute(0, 1, 3, 2, 4, 5).contiguous().view(B, H, W, -1)
    return img


class WaterOilAgentAttention(nn.Module):
    """
    专门针对水和油区域的Agent Attention机制
    基于原始Agent Attention改进，增强对液体纹理的感知能力
    """
    def __init__(self, dim, resolution, idx, split_size=7, dim_out=None, num_heads=8,
                 attn_drop=0., proj_drop=0., agent_num=49, **kwargs):
        super().__init__()
        self.dim = dim
        self.dim_out = dim_out or dim
        self.resolution = resolution
        self.split_size = split_size
        self.num_heads = num_heads
        head_dim = dim // num_heads
        self.agent_num = agent_num
        self.scale = head_dim ** -0.5

        # 设置窗口大小
        if idx == -1:
            H_sp, W_sp = self.resolution, self.resolution
        elif idx == 0:
            H_sp, W_sp = self.resolution, self.split_size
        elif idx == 1:
            W_sp, H_sp = self.resolution, self.split_size
        else:
            print("ERROR MODE", idx)
            exit(0)
        self.H_sp = H_sp
        self.W_sp = W_sp

        # 针对水面和油污特征的卷积层
        self.get_v = nn.Conv2d(dim, dim, kernel_size=(3, 3), stride=(1, 1), padding=1, groups=dim)

        # 水面反射增强卷积
        self.water_enhance = nn.Conv2d(dim, dim, kernel_size=(5, 5), stride=(1, 1), padding=2, groups=dim//4)

        # 油污纹理增强卷积
        self.oil_enhance = nn.Conv2d(dim, dim, kernel_size=(3, 3), stride=(1, 1), padding=1, groups=dim//2)

        self.attn_drop = nn.Dropout(attn_drop)

        # 位置偏置参数
        self.an_bias = nn.Parameter(torch.zeros(num_heads, agent_num, 7, 7))
        self.na_bias = nn.Parameter(torch.zeros(num_heads, agent_num, 7, 7))
        self.ah_bias = nn.Parameter(torch.zeros(1, num_heads, agent_num, H_sp, 1))
        self.aw_bias = nn.Parameter(torch.zeros(1, num_heads, agent_num, 1, W_sp))
        self.ha_bias = nn.Parameter(torch.zeros(1, num_heads, H_sp, 1, agent_num))
        self.wa_bias = nn.Parameter(torch.zeros(1, num_heads, 1, W_sp, agent_num))

        # 初始化偏置参数
        trunc_normal_(self.an_bias, std=.02)
        trunc_normal_(self.na_bias, std=.02)
        trunc_normal_(self.ah_bias, std=.02)
        trunc_normal_(self.aw_bias, std=.02)
        trunc_normal_(self.ha_bias, std=.02)
        trunc_normal_(self.wa_bias, std=.02)

        pool_size = int(agent_num ** 0.5)
        self.pool = nn.AdaptiveAvgPool2d(output_size=(pool_size, pool_size))
        self.softmax = nn.Softmax(dim=-1)

        # 水油区域权重学习
        self.water_oil_weight = nn.Parameter(torch.ones(2))  # [water_weight, oil_weight]

    def im2cswin(self, x):
        B, N, C = x.shape
        H = W = int(np.sqrt(N))
        x = x.transpose(-2, -1).contiguous().view(B, C, H, W)

        # 确保H和W能被窗口大小整除
        if H % self.H_sp != 0 or W % self.W_sp != 0:
            # 调整到最接近的可整除尺寸
            new_H = (H // self.H_sp) * self.H_sp
            new_W = (W // self.W_sp) * self.W_sp
            if new_H == 0:
                new_H = self.H_sp
            if new_W == 0:
                new_W = self.W_sp
            x = F.interpolate(x, size=(new_H, new_W), mode='bilinear', align_corners=False)

        x = img2windows(x, self.H_sp, self.W_sp)
        return x

    def get_lepe(self, x, func):
        B, N, C = x.shape
        H = W = int(np.sqrt(N))
        x = x.transpose(-2, -1).contiguous().view(B, C, H, W)

        H_sp, W_sp = self.H_sp, self.W_sp

        # 确保H和W能被窗口大小整除
        if H % H_sp != 0 or W % W_sp != 0:
            new_H = (H // H_sp) * H_sp
            new_W = (W // W_sp) * W_sp
            if new_H == 0:
                new_H = H_sp
            if new_W == 0:
                new_W = W_sp
            x = F.interpolate(x, size=(new_H, new_W), mode='bilinear', align_corners=False)
            H, W = new_H, new_W

        x = x.view(B, C, H // H_sp, H_sp, W // W_sp, W_sp)
        x = x.permute(0, 2, 4, 1, 3, 5).contiguous().reshape(-1, C, H_sp, W_sp)

        lepe = func(x)
        lepe = lepe.reshape(-1, C, H_sp * W_sp).permute(0, 2, 1).contiguous()

        x = x.reshape(-1, C, self.H_sp * self.W_sp).permute(0, 2, 1).contiguous()
        return x, lepe

    def forward(self, qkv, timestep=None):
        """
        增强的前向传播，融合水面和油污特征处理
        """
        q, k, v = qkv[0], qkv[1], qkv[2]

        # 图像窗口化处理
        H = W = self.resolution
        B, L, C = q.shape
        assert L == H * W, "flatten img_tokens has wrong size"

        q = self.im2cswin(q)
        k = self.im2cswin(k)
        v, lepe = self.get_lepe(v, self.get_v)

        b, n, c = q.shape
        h, w = self.H_sp, self.W_sp
        num_heads, head_dim = self.num_heads, self.dim // self.num_heads

        # 生成agent tokens
        agent_tokens = self.pool(q.reshape(b, h, w, c).permute(0, 3, 1, 2)).reshape(b, c, -1).permute(0, 2, 1)

        # 重塑为多头注意力格式
        q = q.reshape(b, n, num_heads, head_dim).permute(0, 2, 1, 3)
        k = k.reshape(b, n, num_heads, head_dim).permute(0, 2, 1, 3)
        v = v.reshape(b, n, num_heads, head_dim).permute(0, 2, 1, 3)
        agent_tokens = agent_tokens.reshape(b, self.agent_num, num_heads, head_dim).permute(0, 2, 1, 3)

        # 计算位置偏置
        position_bias1 = nn.functional.interpolate(self.an_bias, size=(self.H_sp, self.W_sp), mode='bilinear')
        position_bias1 = position_bias1.reshape(1, num_heads, self.agent_num, -1).repeat(b, 1, 1, 1)
        position_bias2 = (self.ah_bias + self.aw_bias).reshape(1, num_heads, self.agent_num, -1).repeat(b, 1, 1, 1)
        position_bias = position_bias1 + position_bias2

        # Agent注意力计算
        agent_attn = self.softmax((agent_tokens * self.scale) @ k.transpose(-2, -1) + position_bias)
        agent_attn = self.attn_drop(agent_attn)
        agent_v = agent_attn @ v

        # 反向注意力计算
        agent_bias1 = nn.functional.interpolate(self.na_bias, size=(self.H_sp, self.W_sp), mode='bilinear')
        agent_bias1 = agent_bias1.reshape(1, num_heads, self.agent_num, -1).permute(0, 1, 3, 2).repeat(b, 1, 1, 1)
        agent_bias2 = (self.ha_bias + self.wa_bias).reshape(1, num_heads, -1, self.agent_num).repeat(b, 1, 1, 1)
        agent_bias = agent_bias1 + agent_bias2
        q_attn = self.softmax((q * self.scale) @ agent_tokens.transpose(-2, -1) + agent_bias)
        q_attn = self.attn_drop(q_attn)
        x = q_attn @ agent_v

        # 重塑输出
        x = x.transpose(1, 2).reshape(b, n, c)
        x = x + lepe
        x = windows2img(x, self.H_sp, self.W_sp, H, W).view(B, -1, C)

        return x


class TimestepAdaptiveModule(nn.Module):
    """
    时间步自适应模块，根据扩散过程的不同阶段动态调整特征处理策略
    """
    def __init__(self, dim, max_timesteps=1000):
        super().__init__()
        self.dim = dim
        self.max_timesteps = max_timesteps

        # 时间步嵌入
        self.timestep_embed = nn.Sequential(
            nn.Linear(dim, dim * 2),
            nn.SiLU(),
            nn.Linear(dim * 2, dim)
        )

        # 自适应权重生成器
        self.adaptive_weights = nn.Sequential(
            nn.Linear(dim, dim // 4),
            nn.ReLU(),
            nn.Linear(dim // 4, 4)  # [b1, b2, s1, s2]
        )

        # 特征增强模块
        self.feature_enhance = nn.Sequential(
            nn.Conv2d(dim, dim, 3, padding=1),
            nn.GroupNorm(8, dim),
            nn.SiLU(),
            nn.Conv2d(dim, dim, 3, padding=1)
        )

    def get_timestep_embedding(self, timesteps):
        """
        生成时间步嵌入
        """
        half_dim = self.dim // 2
        emb = math.log(10000) / (half_dim - 1)
        emb = torch.exp(torch.arange(half_dim, device=timesteps.device) * -emb)
        emb = timesteps[:, None] * emb[None, :]
        emb = torch.cat([torch.sin(emb), torch.cos(emb)], dim=-1)
        return emb

    def forward(self, x, timesteps):
        """
        根据时间步自适应调整特征
        """
        # 生成时间步嵌入
        t_emb = self.get_timestep_embedding(timesteps)
        t_emb = self.timestep_embed(t_emb)

        # 生成自适应权重
        adaptive_params = self.adaptive_weights(t_emb)  # [B, 4]
        b1, b2, s1, s2 = adaptive_params.chunk(4, dim=-1)

        # 应用特征增强
        B, C, H, W = x.shape
        enhanced_x = self.feature_enhance(x)

        # 根据时间步混合原始特征和增强特征
        timestep_norm = timesteps.float() / self.max_timesteps
        mix_weight = torch.sigmoid(timestep_norm).view(B, 1, 1, 1)

        output = x * (1 - mix_weight) + enhanced_x * mix_weight

        return output, (b1.squeeze(), b2.squeeze(), s1.squeeze(), s2.squeeze())


def timestep_embedding(timesteps, dim, repeat_only=False):
    """
    改进的时间步嵌入函数
    """
    if not repeat_only:
        half_dim = dim // 2
        emb = math.log(10000) / (half_dim - 1)
        emb = torch.exp(torch.arange(half_dim, device=timesteps.device) * -emb)
        emb = timesteps[:, None] * emb[None, :]
        emb = torch.cat([torch.sin(emb), torch.cos(emb)], dim=-1)
        if dim % 2 == 1:  # 处理奇数维度
            emb = torch.cat([emb, torch.zeros_like(emb[:, :1])], dim=-1)
        return emb
    else:
        return torch.randn((timesteps.shape[0], dim), device=timesteps.device)


class UNetModel(nn.Module):
    """
    基础UNet模型
    """
    def __init__(self, model_channels, num_classes=None):
        super().__init__()
        self.model_channels = model_channels
        self.num_classes = num_classes
        self.input_block = nn.Conv2d(3, model_channels, 3, padding=1)
        self.middle_block = nn.Conv2d(model_channels, model_channels, 3, padding=1)
        self.output_block = nn.Conv2d(model_channels, model_channels, 3, padding=1)
        self.final = nn.Conv2d(model_channels, 3, 3, padding=1)


class EvoTune_UNetModel(UNetModel):
    """
    EvoTune U-Net模型：融合时间步自适应调谐和Agent Attention机制
    专门针对积水干扰场景下的油污生成进行优化
    """
    def __init__(
            self,
            b1=1.5,
            b2=1.2,
            s1=0.8,
            s2=0.5,
            model_channels=64,
            num_classes=None,
            resolution=32,
            use_agent_attention=True,
            use_adaptive_fourier=True,
            max_timesteps=1000,
            **kwargs
    ):
        super().__init__(model_channels, num_classes)

        # 基础参数
        self.b1 = b1
        self.b2 = b2
        self.s1 = s1
        self.s2 = s2
        self.resolution = resolution
        self.use_agent_attention = use_agent_attention
        self.use_adaptive_fourier = use_adaptive_fourier
        self.max_timesteps = max_timesteps

        # 时间嵌入层
        self.time_embed = nn.Sequential(
            nn.Linear(model_channels, model_channels * 4),
            nn.SiLU(),
            nn.Linear(model_channels * 4, model_channels)
        )

        # 类别嵌入（如果需要）
        if self.num_classes is not None:
            self.label_emb = nn.Embedding(self.num_classes, model_channels)

        # 时间步自适应模块
        self.timestep_adaptive = TimestepAdaptiveModule(model_channels, max_timesteps)

        # Agent Attention模块（针对水油区域）
        if self.use_agent_attention:
            self.water_oil_attention = WaterOilAgentAttention(
                dim=model_channels,
                resolution=resolution,
                idx=1,  # 使用水平分割模式
                num_heads=8,
                agent_num=49
            )

        # 频域增强模块
        self.freq_enhance = nn.Sequential(
            nn.Conv2d(model_channels, model_channels, 1),
            nn.GroupNorm(8, model_channels),
            nn.SiLU()
        )

        # 特征融合模块
        self.feature_fusion = nn.Sequential(
            nn.Conv2d(model_channels * 2, model_channels, 3, padding=1),
            nn.GroupNorm(8, model_channels),
            nn.SiLU(),
            nn.Conv2d(model_channels, model_channels, 3, padding=1)
        )

        # 水油区域检测模块
        self.water_oil_detector = nn.Sequential(
            nn.Conv2d(model_channels, model_channels // 2, 3, padding=1),
            nn.ReLU(),
            nn.Conv2d(model_channels // 2, 2, 1),  # 2通道：水区域概率，油区域概率
            nn.Sigmoid()
        )

        # 自适应权重学习
        self.adaptive_weight_net = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Flatten(),
            nn.Linear(model_channels, model_channels // 4),
            nn.ReLU(),
            nn.Linear(model_channels // 4, 4),  # [b1, b2, s1, s2]
            nn.Sigmoid()
        )

    def apply_freeu_mechanism(self, h, hs, timestep, adaptive_params=None):
        """
        应用改进的FreeU机制，结合时间步自适应调谐
        """
        if adaptive_params is not None:
            b1, b2, s1, s2 = adaptive_params
        else:
            # 使用自适应权重网络生成参数
            weights = self.adaptive_weight_net(h)
            b1, b2, s1, s2 = weights.chunk(4, dim=-1)
            b1, b2, s1, s2 = b1.squeeze(), b2.squeeze(), s1.squeeze(), s2.squeeze()

            # 基于默认值调整
            b1 = self.b1 + (b1 - 0.5) * 0.5
            b2 = self.b2 + (b2 - 0.5) * 0.5
            s1 = self.s1 + (s1 - 0.5) * 0.3
            s2 = self.s2 + (s2 - 0.5) * 0.3

        # 应用频域滤波
        if self.use_adaptive_fourier:
            h_freq = adaptive_fourier_filter(h, timestep)
            h = h * 0.7 + h_freq * 0.3

        # 骨干特征处理
        if len(hs) > 0:
            skip_feature = hs[-1]

            # 应用FreeU参数
            h_fft = torch.fft.fftn(h, dim=(-2, -1))
            h_fft = torch.fft.fftshift(h_fft, dim=(-2, -1))

            skip_fft = torch.fft.fftn(skip_feature, dim=(-2, -1))
            skip_fft = torch.fft.fftshift(skip_fft, dim=(-2, -1))

            # 动态调整频域特征
            B, C, H, W = h_fft.shape
            center_h, center_w = H // 2, W // 2

            # 骨干特征频域调整
            threshold1 = max(1, int(H * s1.mean().item() * 0.1))
            h_fft[..., center_h-threshold1:center_h+threshold1, center_w-threshold1:center_w+threshold1] *= b1.mean().item()

            # 跳跃连接特征频域调整
            threshold2 = max(1, int(H * s2.mean().item() * 0.1))
            skip_fft[..., center_h-threshold2:center_h+threshold2, center_w-threshold2:center_w+threshold2] *= b2.mean().item()

            # 逆变换
            h = torch.fft.ifftn(torch.fft.ifftshift(h_fft, dim=(-2, -1)), dim=(-2, -1)).real
            skip_feature = torch.fft.ifftn(torch.fft.ifftshift(skip_fft, dim=(-2, -1)), dim=(-2, -1)).real

            # 特征融合
            h = torch.cat([h, skip_feature], dim=1)
            h = self.feature_fusion(h)

        return h

    def forward(self, x, timesteps=None, context=None, y=None, **kwargs):
        """
        EvoTune前向传播：融合时间步自适应调谐和Agent Attention
        """
        assert (y is not None) == (
            self.num_classes is not None), "must specify y if and only if the model is class-conditional"

        # 存储中间特征
        hs = []

        # 时间步嵌入
        if timesteps is not None:
            t_emb = timestep_embedding(timesteps, self.model_channels)
            emb = self.time_embed(t_emb)
        else:
            emb = torch.zeros(x.shape[0], self.model_channels, device=x.device)

        # 类别嵌入
        if self.num_classes is not None and y is not None:
            emb = emb + self.label_emb(y)

        # 输入处理
        h = self.input_block(x)
        hs.append(h)

        # 时间步自适应处理
        if timesteps is not None:
            h_adaptive, adaptive_params = self.timestep_adaptive(h, timesteps)
            h = h + h_adaptive
        else:
            adaptive_params = None

        # 中间处理
        h = self.middle_block(h)

        # 水油区域检测
        water_oil_mask = self.water_oil_detector(h)
        water_mask, oil_mask = water_oil_mask.chunk(2, dim=1)

        # Agent Attention处理（针对水油区域）
        if self.use_agent_attention:
            B, C, H, W = h.shape

            # 调整分辨率以匹配Agent Attention的期望
            # 如果当前分辨率与Agent Attention的分辨率不匹配，进行插值
            if H != self.resolution or W != self.resolution:
                h_resized = F.interpolate(h, size=(self.resolution, self.resolution), mode='bilinear', align_corners=False)
            else:
                h_resized = h

            # 将特征图转换为序列格式
            h_seq = h_resized.flatten(2).transpose(1, 2)  # [B, H*W, C]

            # 生成QKV
            qkv = [h_seq, h_seq, h_seq]

            # 应用Agent Attention
            h_attn = self.water_oil_attention(qkv, timesteps)

            # 转换回特征图格式
            attn_B, attn_L, attn_C = h_attn.shape
            attn_H = attn_W = int(np.sqrt(attn_L))
            h_attn = h_attn.transpose(1, 2).view(B, C, attn_H, attn_W)

            # 如果需要，将结果插值回原始尺寸
            if H != attn_H or W != attn_W:
                h_attn = F.interpolate(h_attn, size=(H, W), mode='bilinear', align_corners=False)

            # 根据水油区域mask加权融合
            water_weight = water_mask * 1.2  # 增强水区域注意力
            oil_weight = oil_mask * 1.5     # 增强油区域注意力
            region_weight = torch.max(water_weight, oil_weight)

            h = h * (1 - region_weight) + h_attn * region_weight

        # 频域增强
        if self.use_adaptive_fourier and timesteps is not None:
            h_freq = adaptive_fourier_filter(h, timesteps)
            h_freq = self.freq_enhance(h_freq)

            # 根据水油区域自适应融合频域特征
            freq_weight = (water_mask * 0.3 + oil_mask * 0.7).clamp(0.1, 0.9)
            h = h * (1 - freq_weight) + h_freq * freq_weight

        # 应用改进的FreeU机制
        h = self.apply_freeu_mechanism(h, hs, timesteps, adaptive_params)

        # 输出处理
        h = self.output_block(h)
        h = self.final(h)

        return h

    def get_water_oil_attention_weights(self):
        """
        获取水油区域注意力权重，用于可视化分析
        """
        if hasattr(self, 'water_oil_attention'):
            return self.water_oil_attention.water_oil_weight
        return None

    def set_adaptive_params(self, b1=None, b2=None, s1=None, s2=None):
        """
        手动设置自适应参数
        """
        if b1 is not None:
            self.b1 = b1
        if b2 is not None:
            self.b2 = b2
        if s1 is not None:
            self.s1 = s1
        if s2 is not None:
            self.s2 = s2


# 测试和演示代码
if __name__ == '__main__':
    print("=== EvoTune U-Net Model 测试 ===")

    # 创建模型实例
    model = EvoTune_UNetModel(
        b1=1.5, b2=1.2, s1=0.8, s2=0.5,
        model_channels=64,
        num_classes=10,
        resolution=32,
        use_agent_attention=False,  # 暂时禁用以测试基础功能
        use_adaptive_fourier=True
    )

    # 测试输入
    batch_size = 2
    input_tensor = torch.randn(batch_size, 3, 256, 256)
    timesteps = torch.randint(0, 1000, (batch_size,))
    y = torch.randint(0, 10, (batch_size,))

    print(f"输入尺寸: {input_tensor.shape}")
    print(f"时间步: {timesteps}")
    print(f"类别标签: {y}")

    # 前向传播
    with torch.no_grad():
        output = model(input_tensor, timesteps=timesteps, y=y)

    print(f"输出尺寸: {output.shape}")

    # 检查水油注意力权重
    attention_weights = model.get_water_oil_attention_weights()
    if attention_weights is not None:
        print(f"水油注意力权重: {attention_weights}")

    # 计算模型参数量
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)

    print(f"总参数量: {total_params:,}")
    print(f"可训练参数量: {trainable_params:,}")

    print("\n=== 模型组件测试 ===")

    # 测试时间步自适应模块
    timestep_adaptive = TimestepAdaptiveModule(64)
    test_feature = torch.randn(2, 64, 32, 32)
    test_timesteps = torch.tensor([100, 500])

    enhanced_feature, adaptive_params = timestep_adaptive(test_feature, test_timesteps)
    print(f"时间步自适应输出尺寸: {enhanced_feature.shape}")
    print(f"自适应参数: b1={adaptive_params[0].mean().item():.3f}, b2={adaptive_params[1].mean().item():.3f}, s1={adaptive_params[2].mean().item():.3f}, s2={adaptive_params[3].mean().item():.3f}")

    # 测试Agent Attention模块
    agent_attention = WaterOilAgentAttention(dim=64, resolution=32, idx=1)
    test_seq = torch.randn(2, 1024, 64)  # [B, H*W, C]
    qkv_test = [test_seq, test_seq, test_seq]

    attn_output = agent_attention(qkv_test, test_timesteps)
    print(f"Agent Attention输出尺寸: {attn_output.shape}")

    print("\n=== 测试完成 ===")


# 参数说明和使用指南
"""
EvoTune U-Net模型参数说明：

核心参数：
- b1, b2: FreeU机制的频域调制参数，控制骨干特征和跳跃连接的权重
- s1, s2: FreeU机制的频域缩放参数，控制特征的频域响应范围
- model_channels: 模型通道数，影响模型容量和计算复杂度
- resolution: 输入分辨率，影响Agent Attention的窗口划分
- use_agent_attention: 是否启用Agent Attention机制
- use_adaptive_fourier: 是否启用自适应傅里叶滤波

针对积水干扰场景的优化：
1. 时间步自适应调谐：根据扩散过程动态调整特征处理策略
2. 水油区域Agent Attention：专门增强对液体纹理的感知能力
3. 自适应频域滤波：针对水面反射和油污特征进行频域优化
4. 区域感知特征融合：基于水油区域检测进行自适应特征融合

使用建议：
- 对于水面反射较强的场景，适当增大b1和s1参数
- 对于油污纹理复杂的场景，适当增大b2和s2参数
- 可通过set_adaptive_params方法动态调整参数
- 建议结合LoRA微调进一步优化特定场景的生成效果
"""