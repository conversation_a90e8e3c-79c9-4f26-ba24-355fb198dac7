<!DOCTYPE html>
<html lang="en-US">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <meta name="generator" content="VuePress 2.0.0-beta.49">
    <style>
      :root {
        --c-bg: #fff;
      }
      html.dark {
        --c-bg: #22272e;
      }
      html, body {
        background-color: var(--c-bg);
      }
    </style>
    <script>
      const userMode = localStorage.getItem('vuepress-color-scheme');
			const systemDarkMode = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;
			if (userMode === 'dark' || (userMode !== 'light' && systemDarkMode)) {
				document.documentElement.classList.toggle('dark', true);
			}
    </script>
    <title>训练参数调节 | SD 训练 UI</title><meta name="description" content="">
    <link rel="modulepreload" href="/assets/app.9273d30a.js"><link rel="modulepreload" href="/assets/params.html.c90a6b4c.js"><link rel="modulepreload" href="/assets/params.html.c8cc13ef.js"><link rel="prefetch" href="/assets/index.html.9d7cc666.js"><link rel="prefetch" href="/assets/tageditor.html.66da263e.js"><link rel="prefetch" href="/assets/tagger.html.2eb31fcb.js"><link rel="prefetch" href="/assets/task.html.4e4c8633.js"><link rel="prefetch" href="/assets/tensorboard.html.4a2799a9.js"><link rel="prefetch" href="/assets/index.html.18cf2953.js"><link rel="prefetch" href="/assets/basic.html.48955584.js"><link rel="prefetch" href="/assets/index.html.b97ec799.js"><link rel="prefetch" href="/assets/master.html.54eb6415.js"><link rel="prefetch" href="/assets/tools.html.c0a4659a.js"><link rel="prefetch" href="/assets/about.html.5b0c0de9.js"><link rel="prefetch" href="/assets/settings.html.06993f96.js"><link rel="prefetch" href="/assets/404.html.686caba0.js"><link rel="prefetch" href="/assets/index.html.84bf285d.js"><link rel="prefetch" href="/assets/tageditor.html.66fa7b72.js"><link rel="prefetch" href="/assets/tagger.html.f698ca26.js"><link rel="prefetch" href="/assets/task.html.2f4311fb.js"><link rel="prefetch" href="/assets/tensorboard.html.e5ada3f5.js"><link rel="prefetch" href="/assets/index.html.4696b6e4.js"><link rel="prefetch" href="/assets/basic.html.655a3322.js"><link rel="prefetch" href="/assets/index.html.db1c0354.js"><link rel="prefetch" href="/assets/master.html.94401419.js"><link rel="prefetch" href="/assets/tools.html.1d9df334.js"><link rel="prefetch" href="/assets/about.html.2343ff24.js"><link rel="prefetch" href="/assets/settings.html.0626d062.js"><link rel="prefetch" href="/assets/404.html.cbf82dee.js"><link rel="prefetch" href="/assets/404.310165f5.js"><link rel="prefetch" href="/assets/layout.c140630d.js">
    <link rel="stylesheet" href="/assets/style.04eab9dc.css">
  </head>
  <body>
    <div id="app"><!--[--><div class="theme-container no-navbar"><!--[--><!----><!--]--><div class="sidebar-mask"></div><!--[--><aside class="sidebar" data-v-db8971c4><div class="el-scrollbar" data-v-db8971c4><div class="el-scrollbar__wrap el-scrollbar__wrap--hidden-default" style=""><div class="el-scrollbar__view" style=""><!--[--><div class="sidebar-container" data-v-db8971c4><!----><ul class="sidebar-items" data-v-db8971c4><!--[--><li><a href="/" class="sidebar-item sidebar-heading" aria-label="SD-Trainer"><!--[--><!--]--> SD-Trainer <!--[--><!--]--></a><!----></li><li><a href="/lora/index.md" class="sidebar-item sidebar-heading active" aria-label="LoRA训练"><!--[--><!--]--> LoRA训练 <!--[--><!--]--></a><ul style="" class="sidebar-item-children"><!--[--><li><a href="/lora/basic.md" class="sidebar-item" aria-label="新手"><!--[--><!--]--> 新手 <!--[--><!--]--></a><!----></li><li><a href="/lora/master.md" class="sidebar-item" aria-label="专家"><!--[--><!--]--> 专家 <!--[--><!--]--></a><!----></li><li><a href="/lora/tools.md" class="sidebar-item" aria-label="工具"><!--[--><!--]--> 工具 <!--[--><!--]--></a><!----></li><li><a href="/lora/params.md" class="sidebar-item active" aria-label="参数详解"><!--[--><!--]--> 参数详解 <!--[--><!--]--></a><!----></li><!--]--></ul></li><li><a href="/dreambooth/index.md" class="sidebar-item sidebar-heading" aria-label="Dreambooth 训练"><!--[--><!--]--> Dreambooth 训练 <!--[--><!--]--></a><!----></li><li><a href="/tensorboard.md" class="sidebar-item sidebar-heading" aria-label="Tensorboard"><!--[--><!--]--> Tensorboard <!--[--><!--]--></a><!----></li><li><a href="/tagger.md" class="sidebar-item sidebar-heading" aria-label="WD 1.4 标签器"><!--[--><!--]--> WD 1.4 标签器 <!--[--><!--]--></a><!----></li><li><a href="/tageditor.md" class="sidebar-item sidebar-heading" aria-label="标签编辑器"><!--[--><!--]--> 标签编辑器 <!--[--><!--]--></a><!----></li><li><p tabindex="0" class="sidebar-item sidebar-heading">其他 <!----></p><ul style="display:none;" class="sidebar-item-children"><!--[--><li><a href="/other/settings.md" class="sidebar-item" aria-label="UI 设置"><!--[--><!--]--> UI 设置 <!--[--><!--]--></a><!----></li><li><a href="/other/about.md" class="sidebar-item" aria-label="关于"><!--[--><!--]--> 关于 <!--[--><!--]--></a><!----></li><!--]--></ul></li><!--]--></ul><ul class="sidebar-bottom" data-v-db8971c4><li class="sidebar-item sidebar-heading appearance" data-v-db8971c4> Github <a class="icon" href="https://github.com/Akegarasu/lora-scripts" target="_blank" aria-label="GitHub" data-v-db8971c4><svg xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="xMidYMid meet" viewBox="0 0 24 24" data-v-db8971c4><path d="M12 2C6.475 2 2 6.475 2 12a9.994 9.994 0 0 0 6.838 9.488c.5.087.687-.213.687-.476c0-.237-.013-1.024-.013-1.862c-2.512.463-3.162-.612-3.362-1.175c-.113-.288-.6-1.175-1.025-1.413c-.35-.187-.85-.65-.013-.662c.788-.013 1.35.725 1.538 1.025c.9 1.512 2.338 1.087 2.912.825c.088-.65.35-1.087.638-1.337c-2.225-.25-4.55-1.113-4.55-4.938c0-1.088.387-1.987 1.025-2.688c-.1-.25-.45-1.275.1-2.65c0 0 .837-.262 2.75 1.026a9.28 9.28 0 0 1 2.5-.338c.85 0 1.7.112 2.5.337c1.912-1.3 2.75-1.024 2.75-1.024c.55 1.375.2 2.4.1 2.65c.637.7 1.025 1.587 1.025 2.687c0 3.838-2.337 4.688-4.562 4.938c.362.312.675.912.675 1.85c0 1.337-.013 2.412-.013 2.75c0 .262.188.574.688.474A10.016 10.016 0 0 0 22 12c0-5.525-4.475-10-10-10z" fill="currentColor" data-v-db8971c4></path></svg></a></li><li class="sidebar-item sidebar-heading appearance" data-v-db8971c4> Language <button class="toggle-color-mode-button" data-v-db8971c4><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" data-v-db8971c4><path d=" M12.87 15.07l-2.54-2.51.03-.03c1.74-1.94 2.98-4.17 3.71-6.53H17V4h-7V2H8v2H1v1.99h11.17C11.5 7.92 10.44 9.75 9 11.35 8.07 10.32 7.3 9.19 6.69 8h-2c.73 1.63 1.73 3.17 2.98 4.56l-5.09 5.02L4 19l5-5 3.11 3.11.76-2.04zM18.5 10h-2L12 22h2l1.12-3h4.75L21 22h2l-4.5-12zm-2.62 7l1.62-4.33L19.12 17h-3.24z " fill="currentColor" data-v-db8971c4></path></svg></button></li><li class="sidebar-item sidebar-heading appearance" data-v-db8971c4> 灯泡 <button class="toggle-color-mode-button" title="toggle color mode" data-v-db8971c4><svg style="" class="icon" focusable="false" viewBox="0 0 32 32"><path d="M16 12.005a4 4 0 1 1-4 4a4.005 4.005 0 0 1 4-4m0-2a6 6 0 1 0 6 6a6 6 0 0 0-6-6z" fill="currentColor"></path><path d="M5.394 6.813l1.414-1.415l3.506 3.506L8.9 10.318z" fill="currentColor"></path><path d="M2 15.005h5v2H2z" fill="currentColor"></path><path d="M5.394 25.197L8.9 21.691l1.414 1.415l-3.506 3.505z" fill="currentColor"></path><path d="M15 25.005h2v5h-2z" fill="currentColor"></path><path d="M21.687 23.106l1.414-1.415l3.506 3.506l-1.414 1.414z" fill="currentColor"></path><path d="M25 15.005h5v2h-5z" fill="currentColor"></path><path d="M21.687 8.904l3.506-3.506l1.414 1.415l-3.506 3.505z" fill="currentColor"></path><path d="M15 2.005h2v5h-2z" fill="currentColor"></path></svg><svg style="display:none;" class="icon" focusable="false" viewBox="0 0 32 32"><path d="M13.502 5.414a15.075 15.075 0 0 0 11.594 18.194a11.113 11.113 0 0 1-7.975 3.39c-.138 0-.278.005-.418 0a11.094 11.094 0 0 1-3.2-21.584M14.98 3a1.002 1.002 0 0 0-.175.016a13.096 13.096 0 0 0 1.825 25.981c.164.006.328 0 .49 0a13.072 13.072 0 0 0 10.703-5.555a1.01 1.01 0 0 0-.783-1.565A13.08 13.08 0 0 1 15.89 4.38A1.015 1.015 0 0 0 14.98 3z" fill="currentColor"></path></svg></button></li></ul></div><!--]--></div></div><!--[--><div class="el-scrollbar__bar is-horizontal" style="display:none;"><div class="el-scrollbar__thumb" style="width:0;transform:translateX(0%);"></div></div><div class="el-scrollbar__bar is-vertical" style="display:none;"><div class="el-scrollbar__thumb" style="height:0;transform:translateY(0%);"></div></div><!--]--></div></aside><!--]--><!--[--><main class="page"><!--[--><!--]--><div class="theme-default-content"><!--[--><!--]--><div><h1 id="训练参数调节" tabindex="-1"><a class="header-anchor" href="#训练参数调节" aria-hidden="true">#</a> 训练参数调节</h1><h2 id="设置训练用模型、数据集" tabindex="-1"><a class="header-anchor" href="#设置训练用模型、数据集" aria-hidden="true">#</a> 设置训练用模型、数据集</h2><h3 id="底模选择" tabindex="-1"><a class="header-anchor" href="#底模选择" aria-hidden="true">#</a> 底模选择</h3><p>底模，尽量选祖宗级别的模型练出来的LoRA会更通用。如果在融合模型上训练可能会<strong>仅仅在你训练的底模上生成图片拥有不错的效果</strong> 但是失去了通用性。可以自己抉择</p><p>什么是祖宗级别的模型？</p><p>sd1.5 2.0、novelai 原版泄露模型。也就是非融合模型。融合模型比如 anything 系列融合了一大堆，orangemix系列融合了 anything 和 basil 更灵车了等等。在他们上面训练的会迁移性更差一些。</p><h3 id="训练分辨率" tabindex="-1"><a class="header-anchor" href="#训练分辨率" aria-hidden="true">#</a> 训练分辨率</h3><p>训练时的分辨率 <code>宽,高</code>，可以是非正方形，但必须为64的整数倍。建议使用大于 512x512 且小于 1024x1024 的值，长宽比根据训练集的占比决定，一般来说方形的可以照顾到各种不同的分辨率。如果多数为长图可以使用512x768这种分辨率，如果宽图居多则可以使用768x512等。</p><h3 id="arb-桶" tabindex="-1"><a class="header-anchor" href="#arb-桶" aria-hidden="true">#</a> ARB 桶</h3><p>默认开启 ARB 桶，以允许使用非固定宽高比的图像来训练（简单来说就是不需要手动剪裁了）。ARB 桶在一定程度上会增加训练时间。 <strong>ARB桶分辨率必须大于训练分辨率</strong></p><h2 id="学习率与优化器设置" tabindex="-1"><a class="header-anchor" href="#学习率与优化器设置" aria-hidden="true">#</a> 学习率与优化器设置</h2><h3 id="学习率设置" tabindex="-1"><a class="header-anchor" href="#学习率设置" aria-hidden="true">#</a> 学习率设置</h3><p>UNet和TE的学习率通常是不同的，因为学习难度不同，通常UNet的学习率会比TE高 。</p><p><img src="https://pic.rmb.bdstatic.com/bjh/user/ad01596fb9943094472b0eefb11238f7.png" alt=""> 如图所示，我们希望UNet和TE都处于一个恰好的位置（绿色部分），但是这个值我们不知道。</p><p>如果UNet训练不足，那么生成的图会不像，UNet训练过度会导致面部扭曲或者产生大量色块。TE训练不足会让出图对Prompt的服从度低，TE训练过度则会生成多余的物品。</p><p><strong>总学习步数 = （图片数量 * 重复次数 * epoch）/ 批次大小</strong></p><p>以UNet学习率为1e-4为例，一般来说图片较少的时候训练人物需要至少1000步，训练画风则需要至少2500步，训练概念则需要至少3000步。这里只是最低的步数，图片多则需要更多步数。学习率更大可以适当减少步数，但并非线性关系，使用两倍的学习率需要使用比之前步数的一半更多的步数。</p><p><strong>决定学习率和步数的最好方法是先训练，再测试。一般比较好的初始值为UNet使用1e-4，TE使用5e-5</strong></p><h3 id="学习率调整策略-lr-scheduler" tabindex="-1"><a class="header-anchor" href="#学习率调整策略-lr-scheduler" aria-hidden="true">#</a> 学习率调整策略（lr_scheduler）</h3><p>推荐使用余弦退火cosine。如果开启预热，预热步数应该占总步数的5%-10%。</p><p>如果使用带重启的余弦退火cosine_with_restarts，重启次数不应该超过4次。</p><h3 id="批次大小-batch-size" tabindex="-1"><a class="header-anchor" href="#批次大小-batch-size" aria-hidden="true">#</a> 批次大小 （batch_size）</h3><p>Batch size 越大梯度越稳定，也可以使用更大的学习率来加速收敛，但是占用显存也更大。</p><p>一般而言 2 倍的 batch_size 可以使用两倍的 UNet 学习率，但是TE学习率不能提高太多。</p><h3 id="优化器" tabindex="-1"><a class="header-anchor" href="#优化器" aria-hidden="true">#</a> 优化器</h3><p>这里只介绍最常用的三种:</p><ul><li><strong>AdamW8bit</strong>：启用的int8优化的AdamW优化器，默认选项。</li><li><strong>Lion</strong>：Google Brain发表的新优化器，各方面表现优于AdamW，同时占用显存更小，可能需要更大的batch size以保持梯度更新稳定。</li><li><strong>D-Adaptation</strong>：FB发表的自适应学习率的优化器，调参简单，无需手动控制学习率，但是占用显存巨大(通常需要大于8G)。使用时<strong>设置学习率为1</strong>即可，同时<strong>学习率调整策略使用constant</strong>。需要添加&quot;--optimizer_args decouple=True&quot;来分离UNet和TE的学习率。(这些设置训练UI都会帮你自动处理)</li></ul><h2 id="网络设置" tabindex="-1"><a class="header-anchor" href="#网络设置" aria-hidden="true">#</a> 网络设置</h2><h3 id="网络结构-lora-locon-loha-dylora" tabindex="-1"><a class="header-anchor" href="#网络结构-lora-locon-loha-dylora" aria-hidden="true">#</a> 网络结构（LoRA/LoCon/LoHa/DyLoRA）</h3><p>不同网络结构对应不同的矩阵低秩分解方法。LoRA 是老祖宗，只控制模型中的线性层和1x1卷积层，后续的不同网络结构都是在 LoRA 的基础上进行改进。</p><p>LyCORIS 对其进行改进，添加了其他几种算法：</p><ul><li>LoCon 加入了对卷积层 (Conv) 的控制</li><li>LoHa（哈达玛积）和 LoKr（克罗内克积）</li><li>IA3</li></ul><p>理论上来说 LyCORIS 会比 LoRA 拥有更加强的微调效果，但是也更加容易过拟合。</p><p>需要注意的是，不同的网络结构一般需要对应不同的 dim 以及学习率。</p><h3 id="网络大小" tabindex="-1"><a class="header-anchor" href="#网络大小" aria-hidden="true">#</a> 网络大小</h3><p>网络大小应该根据实际的训练集图片数量和使用的网络结构决定</p><p><img src="https://pic.rmb.bdstatic.com/bjh/user/da73eed680aa44c7be3f557559911d27.jpeg" alt="network_dim"></p><p>上表中值为我自己的角色训练推荐值，训练画风和概念需要适当增加 Linear 部分大小。推荐值并非对各个不同的数据集都是最优的，需要自己实验得出最优。Conv 的大小最好不要超过8。</p><h3 id="网络alpha-network-alpha" tabindex="-1"><a class="header-anchor" href="#网络alpha-network-alpha" aria-hidden="true">#</a> 网络Alpha（network_alpha）</h3><p>alpha在训练期间缩放网络的权重，alpha越小学习越慢，关系可以认为是负线性相关的。</p><p>一般设置为dim/2或者dim/4。如果选择1，则需要提高学习率或者使用D-Adapation优化器。</p><h2 id="高级设置" tabindex="-1"><a class="header-anchor" href="#高级设置" aria-hidden="true">#</a> 高级设置</h2><h3 id="caption-相关" tabindex="-1"><a class="header-anchor" href="#caption-相关" aria-hidden="true">#</a> Caption 相关</h3><h4 id="caption-dropout" tabindex="-1"><a class="header-anchor" href="#caption-dropout" aria-hidden="true">#</a> caption dropout</h4><p>网上关于这几个caption dropout的说明少之又少，甚至作者在文档里面也没有包含这些参数，只能在代码注释里面找到说明。但是caption dropout在某些情况下对模型性能有提升，所以拿出来讲一下。</p><p>caption_dropout_rate：丢弃全部标签的概率，对一个图片概率不使用caption或class token</p><p>caption_dropout_every_n_epochs：每N个epoch丢弃全部标签。</p><p>caption_tag_dropout_rate：按逗号分隔的标签来随机丢弃tag的概率。<strong>如果使用DB+标签的训练方法训练画风</strong>，推荐使用这个参数，能够有效防止tag过拟合，<strong>一般选择0.2-0.5之间的值</strong>。<strong>训练人物则无需开启</strong>。</p><h4 id="token-热身" tabindex="-1"><a class="header-anchor" href="#token-热身" aria-hidden="true">#</a> token 热身</h4><p>两个token热身相关的参数。</p><p>token_warmup_min：最小学习的token数量，token_warmup_step： 在多少步后达到最大token数量。</p><p>token_warmup可以理解为另一种形式的caption dropout，但是如果不随机打乱token，则只会学习前面N个token。本人并未实测过启用这两个参数的效果，有兴趣可以自行实验。</p><h3 id="噪声相关" tabindex="-1"><a class="header-anchor" href="#噪声相关" aria-hidden="true">#</a> 噪声相关</h3><h4 id="噪声偏移-noise-offset" tabindex="-1"><a class="header-anchor" href="#噪声偏移-noise-offset" aria-hidden="true">#</a> 噪声偏移（noise_offset）</h4><p>在训练过程中加入全局的噪声，改善图片的亮度变化范围（能生成更黑或者更白的图片）。</p><p>如果需要开启，<strong>推荐设置值为0.1</strong>，<strong>同时需要增加学习步数作为网络收敛更慢的补偿</strong>。</p><h4 id="多分辨率-金字塔噪声-multires-noise-iterations、multires-noise-discount" tabindex="-1"><a class="header-anchor" href="#多分辨率-金字塔噪声-multires-noise-iterations、multires-noise-discount" aria-hidden="true">#</a> 多分辨率/金字塔噪声 multires_noise_iterations、multires_noise_discount</h4><p>多分辨率/金字塔噪声相关参数。iteration设置在6-8，再高提升不大。discount设置在0.3-0.8之间，更小的值需要更多步数。</p><h3 id="其他一堆参数" tabindex="-1"><a class="header-anchor" href="#其他一堆参数" aria-hidden="true">#</a> 其他一堆参数</h3><ul><li><p><strong>CLIP_SKIP</strong> CLIP模型使用倒数第N层的输出，需要与底模使用的值保持一致，如果是基于NAI的二次元模型，应当使用2。如果是SD1.5等真实模型应当使用1。生成时也应该使用同样的值。</p></li><li><p><strong>Min-SNR-γ</strong> 发表于ICCV2023上的一种加速扩散模型收敛的方法。不同样本批次的学习难度不同导致梯度方向不一致所以收敛慢，于是引入根据信噪比调整学习率比重。 <strong>设置在5左右的值</strong>是实验效果比较好的，但是注意优化器<strong>使用D-Adaptation的时候不适用</strong>，因为学习率是优化器控制的。</p></li><li><p><strong>数据增强相关</strong> 数据增强是在训练时实时对图片做变换的方法，可用于防止过拟合，能用的一共有四种: color_aug, flip_aug, face_crop_aug_range, random_crop。 其中只有翻转（flip_aug）能和cache latent兼容，因为latent可以直接翻转。 <strong>四种都不推荐使用</strong>，因为裁剪图片的两种cropping方法都会导致tag对应不上。color_aug无法启用cache latent导致训练慢，得不偿失。翻转的flip_aug在图像不对称的情况下表现差，会导致无法正确生成人物不对称的特征（刘海、发饰等）。</p></li><li><p><strong>max_grad_norm</strong> 限制模型更新梯度的大小，改善数值稳定性。梯度的范数超过这个值将会被缩放到这个大小，<strong>一般来说无需设置</strong>。</p></li><li><p><strong>gradient_accumulation_steps</strong> 梯度累积步数，用于在小显存上模拟大batch size的效果。<strong>如果显存足够使用4以上的batch size就没必要启用</strong>。</p></li><li><p><strong>log_with、wandb_api_key</strong> 选择logger类型，可选tensorboard或者wandb。使用wandb需要指定api key。</p></li><li><p><strong>prior_loss_weight</strong> DB训练当中先验部分的权重，控制正则化图像的强度，论文中使用的是1的值，<strong>如无特殊情况无需更改</strong>。</p></li><li><p><strong>debug_dataset</strong> 不训练模型，仅输出训练集元数据和训练参数信息，可以用来检查各项设置是否正确。</p></li><li><p><strong>vae_batch_size</strong> cache lantent的时候VAE编码器的batch size，和训练效果无关。<strong>一般来说使用2-4可以加速一点cache latent的过程</strong>。因为VAE编码器本身参数量比较小，实测在Linux机器上8G的显卡也能开启4。Windows下系统占用显存较多，显存小于10G不建议开启。</p></li></ul><div class="custom-container tip"><p class="custom-container-title">TIP</p><p>文档尚未完结~!</p><p>by <a href="https://space.bilibili.com/12566101" target="_blank" rel="noopener noreferrer">秋葉<span><svg class="external-link-icon" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path><polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg><span class="external-link-icon-sr-only">open in new window</span></span></a> &amp; <a href="https://space.bilibili.com/1713054" target="_blank" rel="noopener noreferrer">Impossib1e嗨<span><svg class="external-link-icon" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path><polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg><span class="external-link-icon-sr-only">open in new window</span></span></a></p><p>感谢 Impossib1e嗨 贡献的大量文档</p></div></div><!--[--><!--]--></div><footer class="page-meta"><!----><!----><!----></footer><!----><!--[--><!--]--></main><!--]--></div><!----><!--]--></div>
    <script type="module" src="/assets/app.9273d30a.js" defer></script>
  </body>
</html>
