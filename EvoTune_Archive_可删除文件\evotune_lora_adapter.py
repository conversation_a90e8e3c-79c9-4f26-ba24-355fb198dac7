"""
EvoTune模型的LoRA训练适配器
专门针对变电设备渗漏油场景的LoRA微调

作者: AI Assistant  
日期: 2025-01-11
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Optional, Tuple, Any
import os
import sys
import json

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

from EvoTune_UNetModel import EvoTune_UNetModel, WaterOilAgentAttention, TimestepAdaptiveModule


class EvoTuneLoRAModule(nn.Module):
    """
    EvoTune专用的LoRA模块
    针对水油区域特征进行低秩适应
    """
    def __init__(self, 
                 in_features: int,
                 out_features: int, 
                 rank: int = 4,
                 alpha: float = 1.0,
                 dropout: float = 0.0,
                 water_oil_specific: bool = True):
        super().__init__()
        
        self.in_features = in_features
        self.out_features = out_features
        self.rank = rank
        self.alpha = alpha
        self.water_oil_specific = water_oil_specific
        
        # 标准LoRA分解
        self.lora_A = nn.Linear(in_features, rank, bias=False)
        self.lora_B = nn.Linear(rank, out_features, bias=False)
        
        # 水油区域特定的LoRA分支
        if water_oil_specific:
            self.water_lora_A = nn.Linear(in_features, rank, bias=False)
            self.water_lora_B = nn.Linear(rank, out_features, bias=False)
            
            self.oil_lora_A = nn.Linear(in_features, rank, bias=False)
            self.oil_lora_B = nn.Linear(rank, out_features, bias=False)
            
            # 区域权重学习
            self.region_classifier = nn.Sequential(
                nn.Linear(in_features, in_features // 4),
                nn.ReLU(),
                nn.Linear(in_features // 4, 3),  # [background, water, oil]
                nn.Softmax(dim=-1)
            )
        
        self.dropout = nn.Dropout(dropout)
        self.scaling = alpha / rank
        
        # 初始化权重
        self.reset_parameters()

    def reset_parameters(self):
        """
        初始化LoRA参数
        """
        nn.init.kaiming_uniform_(self.lora_A.weight, a=5**0.5)
        nn.init.zeros_(self.lora_B.weight)
        
        if self.water_oil_specific:
            nn.init.kaiming_uniform_(self.water_lora_A.weight, a=5**0.5)
            nn.init.zeros_(self.water_lora_B.weight)
            nn.init.kaiming_uniform_(self.oil_lora_A.weight, a=5**0.5)
            nn.init.zeros_(self.oil_lora_B.weight)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        前向传播，融合标准LoRA和水油特定LoRA
        """
        # 标准LoRA路径
        lora_output = self.lora_B(self.lora_A(self.dropout(x))) * self.scaling
        
        if self.water_oil_specific:
            # 区域分类
            region_weights = self.region_classifier(x)  # [B, ..., 3]
            bg_weight, water_weight, oil_weight = region_weights.chunk(3, dim=-1)
            
            # 水区域LoRA
            water_output = self.water_lora_B(self.water_lora_A(self.dropout(x))) * self.scaling
            
            # 油区域LoRA  
            oil_output = self.oil_lora_B(self.oil_lora_A(self.dropout(x))) * self.scaling
            
            # 加权融合
            specialized_output = (
                water_output * water_weight + 
                oil_output * oil_weight
            )
            
            # 结合标准LoRA和特化LoRA
            total_weight = water_weight + oil_weight
            output = lora_output * bg_weight + specialized_output * total_weight
        else:
            output = lora_output
        
        return output


class EvoTuneLoRAAdapter:
    """
    EvoTune模型的LoRA适配器
    管理LoRA模块的创建、训练和应用
    """
    
    TARGET_MODULES = [
        # Agent Attention相关模块
        "water_oil_attention.get_v",
        "water_oil_attention.water_enhance", 
        "water_oil_attention.oil_enhance",
        
        # 时间步自适应模块
        "timestep_adaptive.timestep_embed",
        "timestep_adaptive.adaptive_weights",
        "timestep_adaptive.feature_enhance",
        
        # 特征融合模块
        "feature_fusion",
        "freq_enhance",
        
        # 水油检测模块
        "water_oil_detector",
        
        # 基础UNet模块
        "input_block",
        "middle_block", 
        "output_block",
        "final"
    ]

    def __init__(self, 
                 model: EvoTune_UNetModel,
                 rank: int = 4,
                 alpha: float = 1.0,
                 dropout: float = 0.0,
                 target_modules: Optional[List[str]] = None):
        
        self.model = model
        self.rank = rank
        self.alpha = alpha
        self.dropout = dropout
        self.target_modules = target_modules or self.TARGET_MODULES
        
        self.lora_modules = {}
        self.original_modules = {}
        
        # 应用LoRA
        self.apply_lora()

    def apply_lora(self):
        """
        将LoRA模块应用到目标模块
        """
        for name, module in self.model.named_modules():
            if self.should_apply_lora(name, module):
                self.apply_lora_to_module(name, module)

    def should_apply_lora(self, name: str, module: nn.Module) -> bool:
        """
        判断是否应该对模块应用LoRA
        """
        # 检查是否为线性层或卷积层
        if not isinstance(module, (nn.Linear, nn.Conv2d)):
            return False
        
        # 检查是否在目标模块列表中
        for target in self.target_modules:
            if target in name:
                return True
        
        return False

    def apply_lora_to_module(self, name: str, module: nn.Module):
        """
        对特定模块应用LoRA
        """
        if isinstance(module, nn.Linear):
            in_features = module.in_features
            out_features = module.out_features
        elif isinstance(module, nn.Conv2d):
            # 对于卷积层，使用输入和输出通道数
            in_features = module.in_channels
            out_features = module.out_channels
        else:
            return
        
        # 判断是否为水油相关模块
        water_oil_specific = any(keyword in name.lower() 
                               for keyword in ['water', 'oil', 'agent', 'detector'])
        
        # 创建LoRA模块
        lora_module = EvoTuneLoRAModule(
            in_features=in_features,
            out_features=out_features,
            rank=self.rank,
            alpha=self.alpha,
            dropout=self.dropout,
            water_oil_specific=water_oil_specific
        )
        
        # 保存原始模块和LoRA模块
        self.original_modules[name] = module
        self.lora_modules[name] = lora_module
        
        # 替换前向传播
        self.wrap_module_forward(name, module, lora_module)

    def wrap_module_forward(self, name: str, original_module: nn.Module, lora_module: EvoTuneLoRAModule):
        """
        包装模块的前向传播以集成LoRA
        """
        original_forward = original_module.forward
        
        def lora_forward(x):
            # 原始输出
            original_output = original_forward(x)

            # LoRA输出
            if isinstance(original_module, nn.Linear):
                lora_input = x
                lora_output = lora_module(lora_input)
                return original_output + lora_output
            elif isinstance(original_module, nn.Conv2d):
                # 对于卷积层，使用全局平均池化来降维
                B, C, H, W = x.shape
                lora_input = F.adaptive_avg_pool2d(x, (1, 1)).view(B, C)  # [B, C]
                lora_output = lora_module(lora_input)  # [B, out_channels]

                # 将LoRA输出广播到卷积输出的形状
                lora_output = lora_output.view(B, -1, 1, 1).expand_as(original_output)

                return original_output + lora_output * 0.1  # 降低LoRA的影响强度
            else:
                return original_output
        
        original_module.forward = lora_forward

    def save_lora_weights(self, save_path: str):
        """
        保存LoRA权重
        """
        lora_state_dict = {}
        
        for name, lora_module in self.lora_modules.items():
            lora_state_dict[f"{name}.lora_A.weight"] = lora_module.lora_A.weight
            lora_state_dict[f"{name}.lora_B.weight"] = lora_module.lora_B.weight
            
            if lora_module.water_oil_specific:
                lora_state_dict[f"{name}.water_lora_A.weight"] = lora_module.water_lora_A.weight
                lora_state_dict[f"{name}.water_lora_B.weight"] = lora_module.water_lora_B.weight
                lora_state_dict[f"{name}.oil_lora_A.weight"] = lora_module.oil_lora_A.weight
                lora_state_dict[f"{name}.oil_lora_B.weight"] = lora_module.oil_lora_B.weight
                
                for param_name, param in lora_module.region_classifier.named_parameters():
                    lora_state_dict[f"{name}.region_classifier.{param_name}"] = param
        
        # 保存配置信息
        config = {
            'rank': self.rank,
            'alpha': self.alpha,
            'dropout': self.dropout,
            'target_modules': self.target_modules
        }
        
        torch.save({
            'lora_state_dict': lora_state_dict,
            'config': config
        }, save_path)
        
        print(f"LoRA权重已保存到: {save_path}")

    def load_lora_weights(self, load_path: str):
        """
        加载LoRA权重
        """
        checkpoint = torch.load(load_path, map_location='cpu')
        lora_state_dict = checkpoint['lora_state_dict']
        
        for name, lora_module in self.lora_modules.items():
            if f"{name}.lora_A.weight" in lora_state_dict:
                lora_module.lora_A.weight.data = lora_state_dict[f"{name}.lora_A.weight"]
                lora_module.lora_B.weight.data = lora_state_dict[f"{name}.lora_B.weight"]
                
                if lora_module.water_oil_specific:
                    if f"{name}.water_lora_A.weight" in lora_state_dict:
                        lora_module.water_lora_A.weight.data = lora_state_dict[f"{name}.water_lora_A.weight"]
                        lora_module.water_lora_B.weight.data = lora_state_dict[f"{name}.water_lora_B.weight"]
                        lora_module.oil_lora_A.weight.data = lora_state_dict[f"{name}.oil_lora_A.weight"]
                        lora_module.oil_lora_B.weight.data = lora_state_dict[f"{name}.oil_lora_B.weight"]
                    
                    # 加载区域分类器权重
                    for param_name, param in lora_module.region_classifier.named_parameters():
                        key = f"{name}.region_classifier.{param_name}"
                        if key in lora_state_dict:
                            param.data = lora_state_dict[key]
        
        print(f"LoRA权重已从 {load_path} 加载")

    def get_trainable_parameters(self):
        """
        获取可训练的LoRA参数
        """
        trainable_params = []
        for lora_module in self.lora_modules.values():
            trainable_params.extend(lora_module.parameters())
        return trainable_params

    def freeze_base_model(self):
        """
        冻结基础模型参数，只训练LoRA
        """
        for param in self.model.parameters():
            param.requires_grad = False
        
        # 解冻LoRA参数
        for lora_module in self.lora_modules.values():
            for param in lora_module.parameters():
                param.requires_grad = True

    def unfreeze_all(self):
        """
        解冻所有参数
        """
        for param in self.model.parameters():
            param.requires_grad = True


# 训练配置和工具函数
def create_evotune_lora_config(
    rank: int = 4,
    alpha: float = 1.0,
    dropout: float = 0.1,
    learning_rate: float = 1e-4,
    target_modules: Optional[List[str]] = None
) -> Dict[str, Any]:
    """
    创建EvoTune LoRA训练配置
    """
    return {
        'lora_config': {
            'rank': rank,
            'alpha': alpha,
            'dropout': dropout,
            'target_modules': target_modules or EvoTuneLoRAAdapter.TARGET_MODULES
        },
        'training_config': {
            'learning_rate': learning_rate,
            'batch_size': 4,
            'num_epochs': 100,
            'save_every': 10,
            'gradient_accumulation_steps': 4,
            'mixed_precision': True
        },
        'data_config': {
            'image_size': 512,
            'crop_size': 512,
            'flip_prob': 0.5,
            'color_jitter': 0.1
        }
    }


def test_evotune_lora():
    """
    测试EvoTune LoRA适配器
    """
    print("=== EvoTune LoRA适配器测试 ===")
    
    # 创建EvoTune模型
    model = EvoTune_UNetModel(
        model_channels=64,
        resolution=32,
        use_agent_attention=False,  # 暂时禁用
        use_adaptive_fourier=True
    )
    
    print(f"原始模型参数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 应用LoRA适配器
    lora_adapter = EvoTuneLoRAAdapter(
        model=model,
        rank=4,
        alpha=1.0,
        dropout=0.1
    )
    
    # 冻结基础模型
    lora_adapter.freeze_base_model()
    
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"LoRA可训练参数量: {trainable_params:,}")
    print(f"参数效率: {trainable_params / sum(p.numel() for p in model.parameters()) * 100:.2f}%")
    
    # 测试前向传播
    test_input = torch.randn(2, 3, 256, 256)
    test_timesteps = torch.tensor([100, 500])
    
    with torch.no_grad():
        output = model(test_input, timesteps=test_timesteps)
    
    print(f"输出尺寸: {output.shape}")
    
    # 测试保存和加载
    save_path = "test_evotune_lora.pth"
    lora_adapter.save_lora_weights(save_path)
    
    # 清理测试文件
    if os.path.exists(save_path):
        os.remove(save_path)
        print("测试文件已清理")
    
    print("=== LoRA适配器测试完成 ===")


if __name__ == "__main__":
    test_evotune_lora()
