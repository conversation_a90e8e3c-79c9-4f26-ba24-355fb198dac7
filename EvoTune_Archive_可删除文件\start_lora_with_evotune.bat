@echo off
echo ========================================
echo    启动EvoTune LoRA训练环境
echo ========================================
echo.

cd /d "h:\StableDiffusion_Gaijin\lora-scripts-v1.8.5"

echo 检查EvoTune训练脚本...
if exist "train_evotune_lora.py" (
    echo EvoTune训练脚本已安装 ✓
) else (
    echo 警告: EvoTune训练脚本未找到！
    echo 请先运行 install_evotune_to_webui.py
    pause
    exit
)

echo.
echo 初始化EvoTune训练环境...
python train_evotune_lora.py

echo.
echo 启动LoRA训练界面...
call A启动脚本.bat

pause
