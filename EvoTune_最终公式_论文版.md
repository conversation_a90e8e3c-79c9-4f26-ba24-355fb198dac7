# EvoTune最终公式 - 论文版

## 1. 核心创新：时间步感知的协同调谐

EvoTune的核心创新是**统一的时间步感知框架**，通过一个调谐器控制多个执行模块，实现协同优化。

### 1.1 问题定义

给定扩散模型输入 $x_t \in \mathbb{R}^{B \times C \times H \times W}$ 和时间步 $t \in [0, 1000]$，EvoTune的目标是通过时间步感知的参数调谐提升积水干扰下的渗漏油图像生成质量。

## 2. EvoTune时间步调谐器

### 2.1 时间步标准化

$$\tilde{t} = \frac{t}{1000} \tag{1}$$

### 2.2 相位函数设计

基于扩散过程的特性，设计两个相位函数：

**早期相位（结构建立阶段）**：
$$\phi_{early}(t) = \sigma(10(\tilde{t} - 0.8)) \tag{2}$$

**后期相位（细节优化阶段）**：
$$\phi_{late}(t) = \sigma(10(0.2 - \tilde{t})) \tag{3}$$

其中 $\sigma(x) = \frac{1}{1 + e^{-x}}$ 为sigmoid激活函数。

### 2.3 自适应参数生成

基于相位函数生成时间步感知的参数：

**骨干特征权重**：
$$b_1(t) = 1.5 + 0.3 \times \phi_{early}(t) \times 0.5 \tag{4}$$
$$b_2(t) = 1.2 + 0.3 \times \phi_{late}(t) \times 0.3 \tag{5}$$

**频域缩放参数**：
$$s_1(t) = 0.8 + 0.3 \times \phi_{late}(t) \times 0.4 \tag{6}$$
$$s_2(t) = 0.5 + 0.3 \times \phi_{late}(t) \times 0.3 \tag{7}$$

**注意力权重**：
$$w_{attn}(t) = 0.5 + 0.5 \times \sigma(8(0.3 - \tilde{t})) \tag{8}$$

## 3. 时间步感知Agent Attention

### 3.1 输入处理

给定输入特征 $X \in \mathbb{R}^{B \times C \times H \times W}$，首先进行序列化：
$$X_{seq} = \text{Flatten}(X)^T \in \mathbb{R}^{B \times (H \times W) \times C} \tag{9}$$

### 3.2 Agent生成

通过自适应平均池化生成49个Agent tokens：
$$A = \text{AdaptiveAvgPool2d}(X) \in \mathbb{R}^{B \times 49 \times C} \tag{10}$$

### 3.3 QKV计算

**Query和Key**：
$$Q, K = \text{Linear}(A) \in \mathbb{R}^{B \times 49 \times C} \tag{11}$$

**Value**：
$$V = \text{DepthwiseConv2d}(X_{seq}) \in \mathbb{R}^{B \times (H \times W) \times C} \tag{12}$$

### 3.4 双向注意力机制

**Agent到空间注意力**：
$$\text{Attn}_1 = \text{softmax}\left(\frac{Q \cdot K^T}{\sqrt{d}}\right) \in \mathbb{R}^{B \times 49 \times 49} \tag{13}$$

**空间到Agent注意力**：
$$\text{Attn}_2 = \text{softmax}\left(\frac{K \cdot Q^T}{\sqrt{d}}\right) \in \mathbb{R}^{B \times (H \times W) \times 49} \tag{14}$$

### 3.5 特征融合与时间步调制

**特征融合**：
$$Y = \text{Attn}_2 \cdot (\text{Attn}_1 \cdot V) + V \tag{15}$$

**时间步调制（关键创新）**：
$$Y_{out} = Y \times w_{attn}(t) \tag{16}$$

## 4. 时间步感知频域处理

### 4.1 频域变换

$$X_{freq} = \text{FFT2D}(Y_{out}) \tag{17}$$

### 4.2 自适应滤波器设计

**动态阈值**：
$$\tau(t) = 7 + \lfloor 5(1 - \tilde{t}) \rfloor \tag{18}$$

**自适应滤波器**：
$$\text{Mask}(u, v) = \begin{cases}
s_1(t), & \text{if } |u - u_c| \leq \tau(t) \text{ and } |v - v_c| \leq \tau(t) \\
1, & \text{otherwise}
\end{cases} \tag{19}$$

其中 $(u_c, v_c) = (H/2, W/2)$ 为频域中心。

### 4.3 频域滤波与逆变换

**频域滤波**：
$$X_{filtered} = X_{freq} \odot \text{Mask}(u, v) \tag{20}$$

**逆变换**：
$$X_{enhanced} = \text{IFFT2D}(X_{filtered}) \tag{21}$$

## 5. 完整前向传播

EvoTune的完整前向传播可表示为：

$$\hat{x}_{t-1} = \text{EvoTune}(x_t, t) \tag{22}$$

具体步骤：
1. **参数调谐**：$\{b_1(t), b_2(t), s_1(t), s_2(t), w_{attn}(t)\} = \text{Scheduler}(t)$
2. **Agent注意力**：$Y_{out} = \text{AgentAttention}(x_t, w_{attn}(t))$
3. **频域处理**：$X_{enhanced} = \text{FreqProcess}(Y_{out}, s_1(t), s_2(t))$
4. **UNet处理**：$\hat{x}_{t-1} = \text{UNet}_{original}(X_{enhanced}, t)$

## 6. 参数演化验证

### 6.1 关键时间步的参数值

| 时间步 $t$ | $\tilde{t}$ | $\phi_{early}$ | $\phi_{late}$ | $b_1(t)$ | $s_1(t)$ | $w_{attn}(t)$ |
|------------|-------------|----------------|---------------|----------|----------|---------------|
| 900 | 0.900 | 0.731 | 0.001 | 1.610 | 0.800 | 0.504 |
| 500 | 0.500 | 0.047 | 0.047 | 1.507 | 0.806 | 0.584 |
| 100 | 0.100 | 0.001 | 0.731 | 1.500 | 0.888 | 0.916 |

### 6.2 参数演化的物理意义

- **早期阶段** ($t=900$)：$\phi_{early}=0.731$，增强 $b_1$ 建立基础结构
- **中期阶段** ($t=500$)：$\phi_{early}=\phi_{late}=0.047$，参数变化最小，保持平衡
- **后期阶段** ($t=100$)：$\phi_{late}=0.731$，增强 $s_1, w_{attn}$ 优化细节

## 7. 维度一致性验证

### 7.1 Agent Attention维度

以 $B=2, C=64, H=W=32$ 为例：

- **输入**：$x \in \mathbb{R}^{2 \times 64 \times 32 \times 32}$
- **序列化**：$x_{seq} \in \mathbb{R}^{2 \times 1024 \times 64}$
- **Agent**：$A \in \mathbb{R}^{2 \times 49 \times 64}$
- **注意力**：$\text{Attn}_1 \in \mathbb{R}^{2 \times 49 \times 1024}$
- **输出**：$Y_{out} \in \mathbb{R}^{2 \times 64 \times 32 \times 32}$

### 7.2 频域处理维度

- **FFT输入**：$\mathbb{R}^{2 \times 64 \times 32 \times 32}$
- **FFT输出**：$\mathbb{C}^{2 \times 64 \times 32 \times 17}$
- **滤波后**：$\mathbb{C}^{2 \times 64 \times 32 \times 17}$
- **IFFT输出**：$\mathbb{R}^{2 \times 64 \times 32 \times 32}$

## 8. 算法复杂度

### 8.1 时间复杂度

- **时间步调谐**：$O(1)$
- **Agent Attention**：$O(N \times 49 + 49^2) \approx O(N)$，其中 $N = H \times W$
- **频域处理**：$O(N \log N)$
- **总体**：$O(N \log N)$

### 8.2 空间复杂度

- **Agent tokens**：$O(49 \times C)$
- **注意力矩阵**：$O(N \times 49)$
- **频域缓存**：$O(N \times C)$
- **总体**：$O(N \times C)$

## 9. 理论保证

### 9.1 参数有界性

$$\forall t \in [0, 1000]: \begin{cases}
b_1(t) \in [1.500, 1.610] \\
s_1(t) \in [0.800, 0.888] \\
w_{attn}(t) \in [0.504, 0.916]
\end{cases}$$

### 9.2 连续性

所有参数调谐函数基于连续的sigmoid函数，保证参数演化的平滑性。

### 9.3 单调性

- $\phi_{early}(t)$ 关于 $\tilde{t}$ 单调递增
- $\phi_{late}(t)$ 关于 $\tilde{t}$ 单调递减
- 保证参数演化的稳定性和可预测性

## 10. 与现有方法的区别

| 方法 | 参数特性 | 注意力机制 | 频域处理 | 模块关系 |
|------|----------|------------|----------|----------|
| FreeU | 固定参数 | 无 | 固定滤波 | 单一模块 |
| Agent Attention | 固定权重 | 通用场景 | 无 | 独立工作 |
| **EvoTune** | **时间步自适应** | **时间步感知** | **自适应滤波** | **协同调谐** |

## 11. 损失函数

EvoTune保持原始扩散模型的损失函数：

$$\mathcal{L} = \mathbb{E}_{x_0, \epsilon, t}\left[\|\epsilon - \epsilon_\theta(\text{EvoTune}(x_t, t))\|_2^2\right] \tag{23}$$

其中 $\epsilon$ 为添加的噪声，$\epsilon_\theta$ 为预测的噪声。

---

**总结**：EvoTune通过23个核心公式实现了时间步感知的协同调谐，将动态参数调谐、Agent Attention和频域处理统一在一个框架下，为积水干扰的渗漏油图像生成提供了有效的解决方案。
