# EvoTune核心公式 - 统一版（10个以内）

## 1. 公式设计原则

### 1.1 连贯性保证
- **符号一致性**：每个符号在所有公式中含义保持一致
- **步骤清晰**：每个公式代表一个明确的处理步骤
- **逻辑递进**：公式之间有清晰的输入输出关系
- **创新突出**：核心创新点在公式中明确体现
- **统一设计**：b1、b2统一公式，s1、s2统一公式

### 1.2 符号规范（国际标准）
- **标量**：斜体小写 $t, \alpha, d$
- **向量**：粗体小写 $\boldsymbol{x}, \boldsymbol{w}, \boldsymbol{b}, \boldsymbol{s}$
- **矩阵**：粗体大写 $\boldsymbol{X}, \boldsymbol{A}$
- **函数**：正体 $\mathrm{FFT}, \mathrm{softmax}$
- **下标**：正体 $\mathrm{attn}, \mathrm{early}, \mathrm{late}, \mathrm{backbone}, \mathrm{fourier}$

## 2. EvoTune核心公式（9个）

### 公式1：时间步标准化与相位计算
$$\tilde{t} = \frac{t}{T}, \quad \phi_{\mathrm{early}}(\tilde{t}) = \sigma(10(\tilde{t} - 0.8)), \quad \phi_{\mathrm{late}}(\tilde{t}) = \sigma(10(0.2 - \tilde{t})) \tag{1}$$

**符号说明**：
- $t \in [0, 1000]$：当前扩散时间步
- $T = 1000$：总时间步数
- $\tilde{t} \in [0, 1]$：标准化时间步
- $\phi_{\mathrm{early}}(\tilde{t}) \in [0, 1]$：早期相位函数，控制结构建立阶段
- $\phi_{\mathrm{late}}(\tilde{t}) \in [0, 1]$：后期相位函数，控制细节优化阶段
- $\sigma(\cdot)$：Sigmoid激活函数，$\sigma(x) = \frac{1}{1 + e^{-x}}$

**物理意义**：将时间步映射到[0,1]区间，并计算扩散过程不同阶段的激活强度。早期相位在t接近1000时激活，后期相位在t接近0时激活。

**这一步的作用**：为后续的参数调谐提供时间步感知的基础信号。

### 公式2：骨干特征权重统一调谐
$$\boldsymbol{b}(t) = \boldsymbol{b}_0 + \alpha \cdot \boldsymbol{\phi}_{\mathrm{backbone}}(t) \odot \boldsymbol{c}_{\mathrm{backbone}} \tag{2}$$

其中：
$$\boldsymbol{b}_0 = \begin{bmatrix} 1.5 \\ 1.2 \end{bmatrix}, \quad \boldsymbol{\phi}_{\mathrm{backbone}}(t) = \begin{bmatrix} \phi_{\mathrm{early}}(\tilde{t}) \\ \phi_{\mathrm{late}}(\tilde{t}) \end{bmatrix}, \quad \boldsymbol{c}_{\mathrm{backbone}} = \begin{bmatrix} 0.5 \\ 0.3 \end{bmatrix}, \quad \alpha = 0.3$$

**符号说明**：
- $\boldsymbol{b}(t) = [b_1(t), b_2(t)]^{\mathrm{T}} \in \mathbb{R}^2$：时间步感知的骨干特征权重向量
- $\boldsymbol{b}_0 \in \mathbb{R}^2$：骨干特征基础权重向量（来自FreeU经验值）
- $\boldsymbol{\phi}_{\mathrm{backbone}}(t) \in \mathbb{R}^2$：骨干特征专用相位函数向量
- $\boldsymbol{c}_{\mathrm{backbone}} \in \mathbb{R}^2$：骨干特征调谐系数向量
- $\alpha$：进化强度参数，控制调谐幅度
- $\odot$：Hadamard积（逐元素乘法）

**物理意义**：统一调谐骨干特征权重。$b_1$在早期激活增强结构建立，$b_2$在后期激活优化特征表达。

**这一步的作用**：生成用于UNet骨干特征调制的动态权重，实现时间步感知的特征增强。

### 公式3：频域滤波参数统一调谐
$$\boldsymbol{s}(t) = \boldsymbol{s}_0 + \alpha \cdot \boldsymbol{\phi}_{\mathrm{fourier}}(t) \odot \boldsymbol{c}_{\mathrm{fourier}} \tag{3}$$

其中：
$$\boldsymbol{s}_0 = \begin{bmatrix} 0.8 \\ 0.5 \end{bmatrix}, \quad \boldsymbol{\phi}_{\mathrm{fourier}}(t) = \begin{bmatrix} \phi_{\mathrm{late}}(\tilde{t}) \\ \phi_{\mathrm{late}}(\tilde{t}) \end{bmatrix}, \quad \boldsymbol{c}_{\mathrm{fourier}} = \begin{bmatrix} 0.4 \\ 0.3 \end{bmatrix}$$

**符号说明**：
- $\boldsymbol{s}(t) = [s_1(t), s_2(t)]^{\mathrm{T}} \in \mathbb{R}^2$：时间步感知的频域滤波参数向量
- $\boldsymbol{s}_0 \in \mathbb{R}^2$：频域滤波基础参数向量（来自FreeU经验值）
- $\boldsymbol{\phi}_{\mathrm{fourier}}(t) \in \mathbb{R}^2$：频域滤波专用相位函数向量
- $\boldsymbol{c}_{\mathrm{fourier}} \in \mathbb{R}^2$：频域滤波调谐系数向量

**物理意义**：统一调谐频域滤波参数。$s_1$和$s_2$都在后期激活，用于细节纹理优化，其中$s_1$变化幅度更大。

**这一步的作用**：生成用于频域滤波的动态缩放参数，实现时间步感知的频域处理。

### 公式4：注意力权重调谐
$$w_{\mathrm{attn}}(t) = 0.5 + 0.5 \times \sigma(8(0.3 - \tilde{t})) \tag{4}$$

**符号说明**：
- $w_{\mathrm{attn}}(t) \in [0.5, 1.0]$：时间步感知的注意力权重
- $0.5$：注意力基础权重
- $8$：注意力相位函数的陡峭度参数
- $0.3$：注意力激活阈值

**物理意义**：在后期阶段（$\tilde{t} < 0.3$）增强注意力权重，用于精细化处理水油边界。

**这一步的作用**：生成用于Agent Attention调制的动态权重，实现时间步感知的注意力控制。

### 公式5：Agent Token生成 【🔄 修正版】
$$\boldsymbol{A} = \mathrm{AdaptiveAvgPool2d}(\boldsymbol{H}_{\mathrm{encoder}}) \in \mathbb{R}^{B \times 49 \times C} \tag{5-修正}$$

**符号说明**：
- $\boldsymbol{H}_{\mathrm{encoder}} \in \mathbb{R}^{B \times C \times H \times W}$：**UNet编码器的中间特征张量**（修正：不再是原始输入）
- $\boldsymbol{A} \in \mathbb{R}^{B \times 49 \times C}$：Agent token矩阵
- $B$：批次大小
- $C$：通道数
- $H, W$：特征图高度和宽度
- $49 = 7 \times 7$：Agent token数量（7×7网格）

**物理意义**：通过自适应平均池化将**UNet编码器的中间特征**压缩为49个代表性的Agent tokens，用于捕获积水区域的全局空间模式。

**这一步的作用**：将UNet编码器提取的高维特征表示转换为紧凑的Agent表示，为后续的水油区域识别和双向注意力计算提供全局上下文。

### 公式6：双向注意力计算 【🔄 修正版】
$$\begin{aligned}
\boldsymbol{Q}, \boldsymbol{K} &= \mathrm{Linear}(\boldsymbol{A}) \in \mathbb{R}^{B \times 49 \times C} \\
\boldsymbol{V} &= \mathrm{DepthwiseConv2d}(\boldsymbol{H}_{\mathrm{encoder}}) \in \mathbb{R}^{B \times (H \times W) \times C} \\
\boldsymbol{A}_{\mathrm{agent}} &= \mathrm{softmax}\left(\frac{\boldsymbol{Q}\boldsymbol{K}^{\mathrm{T}}}{\sqrt{d}}\right) \in \mathbb{R}^{B \times 49 \times 49} \\
\boldsymbol{A}_{\mathrm{spatial}} &= \mathrm{softmax}\left(\frac{\boldsymbol{K}\boldsymbol{Q}^{\mathrm{T}}}{\sqrt{d}}\right) \in \mathbb{R}^{B \times (H \times W) \times 49}
\end{aligned} \tag{6-修正}$$

**符号说明**：
- $\boldsymbol{Q}, \boldsymbol{K} \in \mathbb{R}^{B \times 49 \times C}$：查询和键矩阵，来自Agent tokens
- $\boldsymbol{V} \in \mathbb{R}^{B \times (H \times W) \times C}$：值矩阵，**来自UNet编码器特征**（修正：不再是原始输入特征）
- $d = C$：缩放因子，等于特征维度
- $\boldsymbol{A}_{\mathrm{agent}} \in \mathbb{R}^{B \times 49 \times 49}$：Agent间的注意力权重矩阵
- $\boldsymbol{A}_{\mathrm{spatial}} \in \mathbb{R}^{B \times (H \times W) \times 49}$：空间位置到Agent的注意力权重矩阵

**物理意义**：计算Agent tokens之间以及UNet编码器特征的空间位置与Agent tokens之间的注意力关系，实现双向信息交互，专门针对积水区域进行建模。

**这一步的作用**：建立全局Agent和UNet中间特征之间的双向注意力连接，为水油区域在UNet特征空间中的精确识别和增强提供基础。

### 公式7：时间步感知注意力调制
$$\boldsymbol{Y} = \boldsymbol{A}_{\mathrm{spatial}} \cdot (\boldsymbol{A}_{\mathrm{agent}} \cdot \boldsymbol{V}) + \boldsymbol{V} \tag{7a}$$
$$\boldsymbol{Y}_{\mathrm{out}} = \boldsymbol{Y} \times w_{\mathrm{attn}}(t) \tag{7b}$$

**符号说明**：
- $\boldsymbol{Y} \in \mathbb{R}^{B \times (H \times W) \times C}$：融合后的注意力特征
- $\boldsymbol{Y}_{\mathrm{out}} \in \mathbb{R}^{B \times (H \times W) \times C}$：时间步调制后的特征
- $w_{\mathrm{attn}}(t)$：来自公式4的时间步感知注意力权重

**物理意义**：
- **公式7a**：通过双向注意力融合Agent和空间特征，并添加残差连接保持原始信息
- **公式7b**：**核心创新** - 使用时间步权重调制注意力强度，后期增强注意力

**这一步的作用**：将Agent注意力的输出与时间步感知权重结合，实现动态的注意力调制。

### 公式8：自适应频域滤波器设计
$$\tau(t) = 7 + \lfloor 5(1 - \tilde{t}) \rfloor \tag{8a}$$
$$\boldsymbol{M}_{u,v}(t) = \begin{cases}
s_1(t), & \text{if } \max(|u - u_c|, |v - v_c|) \leq \tau(t) \\
s_2(t), & \text{if } \tau(t) < \max(|u - u_c|, |v - v_c|) \leq 2\tau(t) \\
1, & \text{otherwise}
\end{cases} \tag{8b}$$

**符号说明**：
- $\tau(t) \in [7, 12]$：时间步感知的频域阈值
- $\boldsymbol{M}_{u,v}(t)$：自适应频域滤波器
- $(u, v)$：频域坐标
- $(u_c, v_c) = (H/2, W/2)$：频域中心坐标
- $s_1(t), s_2(t)$：来自公式3的频域缩放参数向量 $\boldsymbol{s}(t)$
- $\lfloor \cdot \rfloor$：向下取整函数

**物理意义**：
- **公式8a**：动态阈值随时间步变化，早期小阈值（轻度滤波），后期大阈值（强度滤波）
- **公式8b**：**核心创新** - 分层自适应滤波器，低频区域用$s_1(t)$，中频区域用$s_2(t)$，高频区域保持不变

**这一步的作用**：设计时间步感知的频域滤波器，实现对不同频率成分的精细控制。

### 公式9：时间步感知频域处理 【🔄 修正版】
$$\boldsymbol{H}_{\mathrm{freq}} = \mathrm{FFT2D}(\mathrm{Reshape}(\boldsymbol{Y}_{\mathrm{out}}, [B, C, H, W])) \tag{9a-修正}$$
$$\boldsymbol{H}_{\mathrm{filtered}} = \boldsymbol{H}_{\mathrm{freq}} \odot \boldsymbol{M}(t) \tag{9b-修正}$$
$$\boldsymbol{H}_{\mathrm{enhanced}} = \mathrm{IFFT2D}(\boldsymbol{H}_{\mathrm{filtered}}) \tag{9c-修正}$$

**符号说明**：
- $\boldsymbol{H}_{\mathrm{freq}} \in \mathbb{C}^{B \times C \times H \times (W/2+1)}$：**UNet中间特征的频域表示**（修正：不再是一般特征）
- $\boldsymbol{H}_{\mathrm{filtered}} \in \mathbb{C}^{B \times C \times H \times (W/2+1)}$：滤波后的UNet中间特征频域张量
- $\boldsymbol{H}_{\mathrm{enhanced}} \in \mathbb{R}^{B \times C \times H \times W}$：**增强后的UNet中间特征张量**（修正：将继续在UNet中传播）
- $\boldsymbol{M}(t) \in \mathbb{R}^{H \times (W/2+1)}$：来自公式8b的滤波器矩阵
- $\mathrm{Reshape}(\cdot)$：张量重塑函数，将$(B, H \times W, C)$转为$(B, C, H, W)$

**物理意义**：
- **公式9a**：将Agent Attention增强的UNet中间特征转换到频域进行分析，而非对原始输入图像处理
- **公式9b**：**核心创新** - 在UNet特征空间中应用时间步感知的自适应滤波，精细调节积水区域的频率成分
- **公式9c**：转换回空间域，得到经过频域优化的UNet中间特征，该特征将继续在UNet解码器中传播

**这一步的作用**：在UNet特征空间的频域中应用时间步感知的滤波处理，实现对积水区域水纹理细节的精确控制，确保优化后的特征能够在UNet的后续处理中保持和增强。

### 公式10：UNet骨干特征调制 【🆕 新增】
$$\boldsymbol{H}_{\mathrm{backbone}}^{\mathrm{enhanced}} = \boldsymbol{H}_{\mathrm{backbone}} \times b_1(t) \tag{10-新增}$$

**符号说明**：
- $\boldsymbol{H}_{\mathrm{backbone}} \in \mathbb{R}^{B \times C \times H \times W}$：UNet编码器最深层的骨干特征
- $\boldsymbol{H}_{\mathrm{backbone}}^{\mathrm{enhanced}} \in \mathbb{R}^{B \times C \times H \times W}$：调制后的骨干特征
- $b_1(t)$：来自公式2的时间步感知骨干特征权重

**物理意义**：在UNet编码器-解码器连接点应用时间步感知的权重调制，早期阶段增强骨干特征传递，建立积水区域的基础结构。

**这一步的作用**：确保UNet在编码器-解码器的关键连接点能够根据扩散阶段动态调整特征传递强度，为积水区域的结构建立提供优化的特征表示。

### 公式11：UNet跳跃连接特征调制 【🆕 新增】
$$\boldsymbol{H}_{\mathrm{skip}}^{\mathrm{enhanced}} = \boldsymbol{H}_{\mathrm{skip}} \times b_2(t) \tag{11-新增}$$

**符号说明**：
- $\boldsymbol{H}_{\mathrm{skip}} \in \mathbb{R}^{B \times C \times H \times W}$：UNet跳跃连接的特征
- $\boldsymbol{H}_{\mathrm{skip}}^{\mathrm{enhanced}} \in \mathbb{R}^{B \times C \times H \times W}$：调制后的跳跃连接特征
- $b_2(t)$：来自公式2的时间步感知跳跃连接权重

**物理意义**：在UNet跳跃连接处应用时间步感知的权重调制，后期阶段增强跳跃连接特征融合，优化积水区域的细节表达。

**这一步的作用**：确保UNet在跳跃连接处能够根据扩散阶段动态调整特征融合强度，为积水区域的细节优化提供精确的特征控制。

## 3. 公式间的连贯关系

### 3.1 数据流向图 【🔄 修正版】
```
输入: (𝐱ₜ, t) → UNet编码器 → 𝐇_encoder
    ↓
公式1: 时间步标准化 → (t̃, φ_early, φ_late)
    ↓
公式2: 骨干特征调谐 → 𝐛(t) = [b₁(t), b₂(t)]
    ↓
公式3: 频域参数调谐 → 𝐬(t) = [s₁(t), s₂(t)]
    ↓
公式4: 注意力权重调谐 → w_attn(t)
    ↓
公式5: Agent生成 → 𝐀 (从𝐇_encoder)
    ↓
公式6: 双向注意力 → (𝐀_agent, 𝐀_spatial) (使用𝐇_encoder)
    ↓
公式7: 时间步调制 → 𝐘_out (使用w_attn(t))
    ↓
公式8: 滤波器设计 → 𝐌(t) (使用𝐬(t))
    ↓
公式9: 频域处理 → 𝐇_enhanced (使用𝐌(t))
    ↓
公式10: 骨干特征调制 → 𝐇_backbone^enhanced (使用b₁(t))
    ↓
公式11: 跳跃连接调制 → 𝐇_skip^enhanced (使用b₂(t))
    ↓
UNet解码器 → 最终输出
```

### 3.2 符号传递关系 【🔄 修正版】

| 公式 | 输入符号 | 输出符号 | 传递到 |
|------|----------|----------|--------|
| 公式1 | $t$ | $\tilde{t}, \phi_{\mathrm{early}}, \phi_{\mathrm{late}}$ | 公式2,3,4 |
| 公式2 | $\phi_{\mathrm{early}}, \phi_{\mathrm{late}}$ | $\boldsymbol{b}(t) = [b_1(t), b_2(t)]^{\mathrm{T}}$ | 公式10,11 |
| 公式3 | $\phi_{\mathrm{late}}$ | $\boldsymbol{s}(t) = [s_1(t), s_2(t)]^{\mathrm{T}}$ | 公式8b |
| 公式4 | $\tilde{t}$ | $w_{\mathrm{attn}}(t)$ | 公式7b |
| 公式5 | $\boldsymbol{H}_{\mathrm{encoder}}$ | $\boldsymbol{A}$ | 公式6 |
| 公式6 | $\boldsymbol{A}, \boldsymbol{H}_{\mathrm{encoder}}$ | $\boldsymbol{A}_{\mathrm{agent}}, \boldsymbol{A}_{\mathrm{spatial}}, \boldsymbol{V}$ | 公式7a |
| 公式7a | $\boldsymbol{A}_{\mathrm{agent}}, \boldsymbol{A}_{\mathrm{spatial}}, \boldsymbol{V}$ | $\boldsymbol{Y}$ | 公式7b |
| 公式7b | $\boldsymbol{Y}, w_{\mathrm{attn}}(t)$ | $\boldsymbol{Y}_{\mathrm{out}}$ | 公式9a |
| 公式8 | $\tilde{t}, \boldsymbol{s}(t)$ | $\boldsymbol{M}(t)$ | 公式9b |
| 公式9 | $\boldsymbol{Y}_{\mathrm{out}}, \boldsymbol{M}(t)$ | $\boldsymbol{H}_{\mathrm{enhanced}}$ | 公式10,11 |
| 公式10 | $\boldsymbol{H}_{\mathrm{backbone}}, b_1(t)$ | $\boldsymbol{H}_{\mathrm{backbone}}^{\mathrm{enhanced}}$ | UNet解码器 |
| 公式11 | $\boldsymbol{H}_{\mathrm{skip}}, b_2(t)$ | $\boldsymbol{H}_{\mathrm{skip}}^{\mathrm{enhanced}}$ | UNet解码器 |

## 4. 关键参数演化

### 4.1 时间步参数变化

| 时间步 $t$ | $\tilde{t}$ | $\phi_{\mathrm{early}}$ | $\phi_{\mathrm{late}}$ | $b_1(t)$ | $b_2(t)$ | $s_1(t)$ | $s_2(t)$ | $w_{\mathrm{attn}}(t)$ | $\tau(t)$ |
|------------|-------------|-------------------------|------------------------|----------|----------|----------|----------|------------------------|-----------|
| **900** | 0.9 | **0.731** | 0.001 | **1.610** | 1.200 | 0.800 | 0.500 | 0.504 | **7** |
| **500** | 0.5 | 0.047 | 0.047 | 1.507 | 1.204 | 0.806 | 0.504 | 0.584 | **9** |
| **100** | 0.1 | 0.001 | **0.731** | 1.500 | **1.266** | **0.888** | **0.566** | **0.916** | **11** |

### 4.2 物理意义解释

#### 早期阶段 (t=900)
- $\phi_{\mathrm{early}} = 0.731$ → $b_1(t) = 1.610$ ↑：增强骨干特征，建立基础结构
- $b_2(t) = 1.200$：保持基础值，跳跃连接暂不增强
- $w_{\mathrm{attn}}(t) = 0.504$ ↓：注意力较弱，避免过度关注细节
- $\tau(t) = 7$ ↓：小阈值，轻度频域滤波，保持整体结构

#### 后期阶段 (t=100)
- $\phi_{\mathrm{late}} = 0.731$ → $b_2(t) = 1.266$ ↑：增强跳跃连接，优化特征融合
- $\boldsymbol{s}(t) = [0.888, 0.566]$ ↑：频域参数都增强，$s_1$变化更大
- $w_{\mathrm{attn}}(t) = 0.916$ ↑：注意力增强，精细化处理水油边界
- $\tau(t) = 11$ ↑：大阈值，强度频域滤波，增强水纹理真实感

### 4.3 统一公式的优势体现

#### 🎯 **骨干特征统一调谐** ($\boldsymbol{b}(t)$)
- **早期**：$b_1$主导，建立基础结构
- **后期**：$b_2$增强，优化特征表达
- **协同性**：两个参数通过统一的相位函数协调变化

#### 🌊 **频域参数统一调谐** ($\boldsymbol{s}(t)$)
- **同步激活**：$s_1$和$s_2$都在后期激活
- **分层控制**：$s_1$控制低频，$s_2$控制中频
- **幅度差异**：$s_1$变化幅度更大，体现主次关系

## 5. 核心创新点

### 5.1 统一时间步调谐框架（公式1-4）
- **创新**：通过相位函数统一控制多个参数的时间步演化
- **统一设计**：骨干特征权重 $\boldsymbol{b}(t)$ 和频域参数 $\boldsymbol{s}(t)$ 分别采用向量化统一公式
- **优势**：避免参数冲突，确保协同优化，减少超参数数量

### 5.2 时间步感知注意力调制（公式7b）
- **创新**：$\boldsymbol{Y}_{\mathrm{out}} = \boldsymbol{Y} \times w_{\mathrm{attn}}(t)$
- **优势**：注意力强度随扩散阶段动态变化，后期增强精细处理

### 5.3 分层自适应频域滤波（公式8-9）
- **创新**：时间步感知的分层滤波器设计 $\boldsymbol{M}(t)$，使用 $s_1(t)$ 和 $s_2(t)$ 分别控制低频和中频
- **优势**：早期保持结构，后期增强细节，实现精细的频域控制

## 6. 维度一致性验证

### 6.1 关键维度检查

以 $B=2, C=64, H=W=32$ 为例：

| 公式 | 关键张量 | 维度 | 说明 |
|------|----------|------|------|
| 公式2 | $\boldsymbol{b}(t)$ | $2 \times 1$ | 骨干特征权重向量 |
| 公式3 | $\boldsymbol{s}(t)$ | $2 \times 1$ | 频域参数向量 |
| 公式4 | $w_{\mathrm{attn}}(t)$ | 标量 | 注意力权重 |
| 公式5 | $\boldsymbol{A}$ | $2 \times 49 \times 64$ | Agent token矩阵 |
| 公式6 | $\boldsymbol{A}_{\mathrm{agent}}$ | $2 \times 49 \times 49$ | Agent间注意力 |
| 公式6 | $\boldsymbol{A}_{\mathrm{spatial}}$ | $2 \times 1024 \times 49$ | 空间-Agent注意力 |
| 公式7 | $\boldsymbol{Y}_{\mathrm{out}}$ | $2 \times 1024 \times 64$ | 调制后特征 |
| 公式8 | $\boldsymbol{M}(t)$ | $32 \times 17$ | 频域滤波器 |
| 公式9a | $\boldsymbol{X}_{\mathrm{freq}}$ | $2 \times 64 \times 32 \times 17$ | 频域特征 |

### 6.2 矩阵运算验证
- $\boldsymbol{A}_{\mathrm{agent}} \cdot \boldsymbol{V}$: $(2 \times 49 \times 49) \times (2 \times 1024 \times 64) \rightarrow (2 \times 49 \times 64)$
- $\boldsymbol{A}_{\mathrm{spatial}} \cdot (\boldsymbol{A}_{\mathrm{agent}} \cdot \boldsymbol{V})$: $(2 \times 1024 \times 49) \times (2 \times 49 \times 64) \rightarrow (2 \times 1024 \times 64)$

### 6.3 统一公式的维度优势
- **向量化设计**：$\boldsymbol{b}(t)$ 和 $\boldsymbol{s}(t)$ 都是紧凑的向量表示
- **参数效率**：减少了独立参数的数量，提高了计算效率
- **扩展性**：易于扩展到更多参数或不同的UNet架构

## 7. 论文使用建议

### 7.1 方法论部分结构
```
3.1 问题定义与动机
3.2 EvoTune时间步感知调谐框架
    3.2.1 时间步标准化与相位计算 (公式1)
    3.2.2 统一参数调谐设计 (公式2-4)
3.3 Agent Attention机制
    3.3.1 Agent Token生成 (公式5)
    3.3.2 双向注意力计算 (公式6)
    3.3.3 时间步感知调制 (公式7)
3.4 自适应频域处理
    3.4.1 分层滤波器设计 (公式8)
    3.4.2 时间步感知频域处理 (公式9)
```

### 7.2 重点说明
- **公式1**：时间步感知的基础，为所有后续调谐提供信号
- **公式2-3**：**核心创新** - 统一的向量化参数调谐，体现设计的优雅性
- **公式4**：注意力权重的时间步感知设计
- **公式7b**：时间步权重调制的创新性应用
- **公式8-9**：自适应频域滤波的动态特性和分层控制

### 7.3 统一公式的表述优势

#### ✅ **数学表达简洁**
```
原来：4个独立公式
现在：2个统一向量公式 + 1个注意力公式
```

#### ✅ **物理意义清晰**
```
骨干特征：𝐛(t) 统一调谐，体现结构-细节的时间步演化
频域滤波：𝐬(t) 统一调谐，体现低频-中频的协同控制
```

#### ✅ **创新点突出**
```
统一设计 → 参数协同优化
向量化表示 → 计算效率提升
时间步感知 → 动态自适应调谐
```

## 8. 小白理解指南

### 8.1 每个公式的作用（通俗解释）

| 公式 | 通俗解释 | 为什么需要这一步 |
|------|----------|------------------|
| **公式1** | 把时间步t变成0-1之间的数，算出早期和后期的"激活程度" | 让后面的参数知道现在是扩散的哪个阶段 |
| **公式2** | 根据早期/后期激活程度，统一计算骨干特征的两个权重 | 早期建结构用b₁，后期优化用b₂，统一公式更简洁 |
| **公式3** | 根据后期激活程度，统一计算频域滤波的两个参数 | s₁和s₂都是后期用的，统一公式避免冲突 |
| **公式4** | 计算注意力的强度，后期更强 | 后期需要精细处理水油边界，注意力要更强 |
| **公式5** | 把输入图像压缩成49个"代表点" | 太多像素点计算太慢，用49个代表点就够了 |
| **公式6** | 计算代表点之间、代表点与像素点之间的关系 | 找出哪些区域是水，哪些是油，它们怎么相互影响 |
| **公式7** | 用注意力融合特征，然后用时间步权重调节强度 | 把水油信息融合，根据扩散阶段调节处理强度 |
| **公式8** | 设计一个滤波器，不同频率用不同的处理方式 | 低频控制整体结构，中频控制纹理细节 |
| **公式9** | 在频域里应用滤波器，然后转回正常图像 | 频域处理更精确，能更好地控制水纹理 |

### 8.2 符号对应关系总结

| 符号类型 | 符号 | 含义 | 来源/去向 |
|----------|------|------|----------|
| **时间相关** | $t, \tilde{t}$ | 原始时间步，标准化时间步 | 输入 → 公式1 |
| **相位函数** | $\phi_{\mathrm{early}}, \phi_{\mathrm{late}}$ | 早期相位，后期相位 | 公式1 → 公式2,3 |
| **骨干参数** | $\boldsymbol{b}(t) = [b_1(t), b_2(t)]^{\mathrm{T}}$ | 骨干特征权重向量 | 公式2 → UNet |
| **频域参数** | $\boldsymbol{s}(t) = [s_1(t), s_2(t)]^{\mathrm{T}}$ | 频域滤波参数向量 | 公式3 → 公式8 |
| **注意力** | $w_{\mathrm{attn}}(t)$ | 注意力权重 | 公式4 → 公式7b |
| **Agent相关** | $\boldsymbol{A}, \boldsymbol{A}_{\mathrm{agent}}, \boldsymbol{A}_{\mathrm{spatial}}$ | Agent tokens和注意力矩阵 | 公式5 → 公式6 → 公式7 |
| **特征流** | $\boldsymbol{Y}, \boldsymbol{Y}_{\mathrm{out}}, \boldsymbol{X}_{\mathrm{enhanced}}$ | 处理过程中的特征 | 公式7 → 公式9 → UNet |

这9个公式通过统一设计实现了更简洁的数学表达，同时保持了强连贯性和清晰的物理意义！
