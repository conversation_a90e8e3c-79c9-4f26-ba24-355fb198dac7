# EvoTune精简公式 - 国际标准版

## 1. 公式精简策略

### 1.1 合并原则
- **相邻步骤合并**：将连续的计算步骤合并为一个公式
- **相似公式统一**：用参数向量表示多个相似公式
- **常量参数化**：用符号代替具体数值
- **核心创新突出**：保留最关键的创新公式

### 1.2 符号规范（国际标准）
- **标量**：斜体小写 $t, d, \alpha$
- **向量**：粗体小写 $\boldsymbol{x}, \boldsymbol{w}$
- **矩阵**：粗体大写 $\boldsymbol{X}, \boldsymbol{A}$
- **函数**：正体 $\mathrm{FFT}, \mathrm{softmax}$
- **算子**：正体 $\odot$ (Hadamard积)
- **集合/空间**：花体 $\mathbb{R}$

## 2. EvoTune核心公式（5个）

### 公式1：时间步感知参数调谐
$$\boldsymbol{\theta}(t) = \boldsymbol{\theta}_0 + \alpha \boldsymbol{f}(\tilde{t}) \odot \boldsymbol{c} \tag{1}$$

其中：
- $\tilde{t} = t/T$ 为标准化时间步
- $\boldsymbol{\theta}(t) = [b_1(t), b_2(t), s_1(t), s_2(t), w_{\mathrm{attn}}(t)]^{\mathrm{T}}$ 为参数向量
- $\boldsymbol{\theta}_0 = [1.5, 1.2, 0.8, 0.5, 0.5]^{\mathrm{T}}$ 为基础参数向量
- $\boldsymbol{f}(\tilde{t}) = [\phi_{\mathrm{early}}(\tilde{t}), \phi_{\mathrm{late}}(\tilde{t}), \phi_{\mathrm{late}}(\tilde{t}), \phi_{\mathrm{late}}(\tilde{t}), \phi_{\mathrm{attn}}(\tilde{t})]^{\mathrm{T}}$ 为相位函数向量
- $\boldsymbol{c} = [0.5, 0.3, 0.4, 0.3, 0.5]^{\mathrm{T}}$ 为调谐系数向量
- $\alpha = 0.3$ 为进化强度参数
- $\phi_{\mathrm{early}}(\tilde{t}) = \sigma(10(\tilde{t} - 0.8))$，$\phi_{\mathrm{late}}(\tilde{t}) = \sigma(10(0.2 - \tilde{t}))$，$\phi_{\mathrm{attn}}(\tilde{t}) = \sigma(8(0.3 - \tilde{t}))$

### 公式2：时间步感知Agent注意力
$$\boldsymbol{Y}_{\mathrm{out}} = \mathrm{AgentAttn}(\boldsymbol{X}) \times w_{\mathrm{attn}}(t) \tag{2}$$

其中：
$$\mathrm{AgentAttn}(\boldsymbol{X}) = \boldsymbol{A}_2 \cdot (\boldsymbol{A}_1 \cdot \boldsymbol{V}) + \boldsymbol{V}$$

- $\boldsymbol{A}_1 = \mathrm{softmax}(\boldsymbol{Q}\boldsymbol{K}^{\mathrm{T}}/\sqrt{d}) \in \mathbb{R}^{B \times 49 \times 49}$
- $\boldsymbol{A}_2 = \mathrm{softmax}(\boldsymbol{K}\boldsymbol{Q}^{\mathrm{T}}/\sqrt{d}) \in \mathbb{R}^{B \times HW \times 49}$
- $\boldsymbol{Q}, \boldsymbol{K} = \mathrm{Linear}(\mathrm{AdaptivePool}(\boldsymbol{X}))$
- $\boldsymbol{V} = \mathrm{DepthwiseConv}(\boldsymbol{X})$

### 公式3：时间步感知自适应频域滤波
$$\boldsymbol{X}_{\mathrm{enhanced}} = \mathrm{FFT}^{-1}(\mathrm{FFT}(\boldsymbol{Y}_{\mathrm{out}}) \odot \boldsymbol{M}(t)) \tag{3}$$

其中自适应滤波器：
$$\boldsymbol{M}_{u,v}(t) = \begin{cases}
s_1(t), & \text{if } \max(|u - u_c|, |v - v_c|) \leq \tau(t) \\
1, & \text{otherwise}
\end{cases}$$

- $\tau(t) = \tau_0 + \lfloor \beta(1 - \tilde{t}) \rfloor$，其中 $\tau_0 = 7$，$\beta = 5$
- $(u_c, v_c)$ 为频域中心坐标

### 公式4：EvoTune完整前向传播
$$\hat{\boldsymbol{x}}_{t-1} = \mathrm{UNet}_{\mathrm{original}}(\mathrm{EvoTune}(\boldsymbol{x}_t, t), t) \tag{4}$$

其中：
$$\mathrm{EvoTune}(\boldsymbol{x}_t, t) = \mathrm{FreqProcess}(\mathrm{AgentAttn}(\boldsymbol{x}_t, t), t)$$

### 公式5：损失函数
$$\mathcal{L} = \mathbb{E}_{\boldsymbol{x}_0, \boldsymbol{\epsilon}, t}\left[\|\boldsymbol{\epsilon} - \boldsymbol{\epsilon}_{\boldsymbol{\theta}}(\mathrm{EvoTune}(\boldsymbol{x}_t, t))\|_2^2\right] \tag{5}$$

## 3. 公式合并说明

### 3.1 合并策略详解

#### 原23个公式 → 5个核心公式

| 原公式范围 | 合并后 | 合并方式 | 说明 |
|------------|--------|----------|------|
| Eq. 1-8 | **公式1** | 向量化表示 | 将8个参数调谐公式合并为一个向量公式 |
| Eq. 9-16 | **公式2** | 函数封装 | 将Agent注意力的8个步骤封装为一个函数 |
| Eq. 17-21 | **公式3** | 复合函数 | 将频域处理的5个步骤合并为复合函数 |
| Eq. 22 | **公式4** | 保持独立 | 完整前向传播，体现系统整体性 |
| Eq. 23 | **公式5** | 保持独立 | 损失函数，训练目标 |

### 3.2 符号规范化改进

#### 原符号 → 标准符号

| 原符号 | 标准符号 | 类型 | 说明 |
|--------|----------|------|------|
| $x_t$ | $\boldsymbol{x}_t$ | 向量/张量 | 输入特征张量 |
| $X$ | $\boldsymbol{X}$ | 矩阵 | 特征矩阵 |
| $FFT$ | $\mathrm{FFT}$ | 函数 | 快速傅里叶变换 |
| $softmax$ | $\mathrm{softmax}$ | 函数 | 软最大函数 |
| $w_{attn}$ | $w_{\mathrm{attn}}$ | 标量 | 注意力权重（下标正体） |
| $\epsilon_\theta$ | $\boldsymbol{\epsilon}_{\boldsymbol{\theta}}$ | 向量函数 | 参数化噪声预测 |

### 3.3 关键创新体现

#### 公式1：统一时间步调谐
- **创新点**：向量化的参数调谐框架
- **核心**：$\boldsymbol{\theta}(t) = \boldsymbol{\theta}_0 + \alpha \boldsymbol{f}(\tilde{t}) \odot \boldsymbol{c}$
- **意义**：一个公式控制所有时间步感知参数

#### 公式2：时间步感知注意力
- **创新点**：时间步权重调制 $\times w_{\mathrm{attn}}(t)$
- **核心**：Agent注意力与时间步调谐的结合
- **意义**：注意力强度随扩散阶段动态变化

#### 公式3：自适应频域滤波
- **创新点**：时间步感知的滤波器 $\boldsymbol{M}(t)$
- **核心**：$s_1(t)$ 和 $\tau(t)$ 的动态调整
- **意义**：频域处理适应扩散过程需求

## 4. 参数数值对应

### 4.1 关键参数验证

| 时间步 $t$ | $\tilde{t}$ | $b_1(t)$ | $s_1(t)$ | $w_{\mathrm{attn}}(t)$ | 主要功能 |
|------------|-------------|----------|----------|------------------------|----------|
| 900 | 0.9 | 1.610 | 0.800 | 0.504 | 结构建立 |
| 500 | 0.5 | 1.507 | 0.806 | 0.584 | 平衡过渡 |
| 100 | 0.1 | 1.500 | 0.888 | 0.916 | 细节优化 |

### 4.2 向量参数展开

**基础参数向量**：
$$\boldsymbol{\theta}_0 = \begin{bmatrix} 1.5 \\ 1.2 \\ 0.8 \\ 0.5 \\ 0.5 \end{bmatrix} \quad \boldsymbol{c} = \begin{bmatrix} 0.5 \\ 0.3 \\ 0.4 \\ 0.3 \\ 0.5 \end{bmatrix}$$

**相位函数向量**（$t=100$时）：
$$\boldsymbol{f}(0.1) = \begin{bmatrix} 0.001 \\ 0.731 \\ 0.731 \\ 0.731 \\ 0.916 \end{bmatrix}$$

## 5. 论文使用建议

### 5.1 公式布局
```
方法论部分：
- 公式1：时间步感知参数调谐（核心创新）
- 公式2：时间步感知Agent注意力
- 公式3：自适应频域滤波
- 公式4：完整前向传播
- 公式5：损失函数
```

### 5.2 重点说明
- **公式1**：重点解释向量化的参数调谐机制
- **公式2-3**：说明时间步感知如何体现在具体模块中
- **公式4**：展示系统的整体性和模块协同
- **公式5**：说明训练目标保持不变

### 5.3 符号表
建议在论文中添加符号表，说明主要符号的含义：
- $\boldsymbol{x}_t$：时间步$t$的噪声图像
- $\boldsymbol{\theta}(t)$：时间步感知参数向量
- $w_{\mathrm{attn}}(t)$：时间步感知注意力权重
- $\boldsymbol{M}(t)$：时间步感知频域滤波器

## 6. 与网络架构图对应

### 6.1 公式-模块映射
- **公式1** ↔ EvoTune调谐器模块
- **公式2** ↔ 时间步感知Agent注意力模块  
- **公式3** ↔ 时间步感知频域处理模块
- **公式4** ↔ 完整网络架构
- **公式5** ↔ 训练目标

### 6.2 创新点标注
在网络架构图中，可以直接标注：
- "Timestep-aware Parameter Tuning (Eq. 1)"
- "Timestep Modulation × $w_{\mathrm{attn}}(t)$ (Eq. 2)"
- "Adaptive Filtering $\boldsymbol{M}(t)$ (Eq. 3)"

这样的精简公式既保持了数学严谨性，又突出了核心创新，符合顶级期刊的简洁性要求。
