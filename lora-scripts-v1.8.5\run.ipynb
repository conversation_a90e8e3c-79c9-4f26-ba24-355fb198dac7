{"cells": [{"cell_type": "markdown", "id": "5e35269a-ec20-41a3-93a6-da798c3a8401", "metadata": {}, "source": ["# LoRA Train UI: SD-Trainer\n", "\n", "LoRA Training UI By [<PERSON><PERSON><PERSON><PERSON>](https://github.com/Akegarasu)\n", "User Guide：https://github.com/Akegarasu/lora-scripts/blob/main/README.md\n", "\n", "LoRA 训练 By [秋葉aaaki@bilibili](https://space.bilibili.com/12566101)\n", "使用方法：https://www.bilibili.com/read/cv24050162/"]}, {"cell_type": "markdown", "id": "12c2a3d0-9aec-4680-9b8a-cb02cac48de6", "metadata": {}, "source": ["### Run | 运行"]}, {"cell_type": "code", "execution_count": null, "id": "7ae0678f-69df-4a12-a0bc-1325e52e9122", "metadata": {}, "outputs": [], "source": ["import sys\n", "!export HF_HOME=huggingface && $sys.executable gui.py --host 0.0.0.0"]}, {"cell_type": "markdown", "id": "99edaa2b-9ba2-4fde-9b2e-af5dc8bf7062", "metadata": {}, "source": ["## Update | 更新"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### <PERSON><PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!git pull && git submodule init && git submodule update"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 国内镜像加速"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!export GIT_CONFIG_GLOBAL=./assets/gitconfig-cn && export GIT_TERMINAL_PROMPT=false && git pull && git submodule init && git submodule update"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.8"}}, "nbformat": 4, "nbformat_minor": 5}