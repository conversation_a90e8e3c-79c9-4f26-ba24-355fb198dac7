# EvoTune核心公式总结

## 主要公式列表（按网络结构图顺序）

### 1. 时间步调谐模块

| 公式编号 | 公式 | 说明 |
|---------|------|------|
| (1) | $t_{norm} = \frac{t}{T}$ | 时间步标准化，$T=1000$ |
| (2) | $\phi_{early}(t) = \sigma(10 \times (t_{norm} - 0.8))$ | 早期相位函数 |
| (3) | $\phi_{late}(t) = \sigma(10 \times (0.2 - t_{norm}))$ | 后期相位函数 |
| (4) | $\phi_{mid}(t) = 1 - \phi_{early}(t) - \phi_{late}(t)$ | 中期相位函数 |
| (5) | $b_1(t) = b_1^{base} + \alpha \times \phi_{early}(t) \times 0.5$ | 骨干特征权重1 |
| (6) | $b_2(t) = b_2^{base} + \alpha \times \phi_{late}(t) \times 0.3$ | 骨干特征权重2 |
| (7) | $s_1(t) = s_1^{base} + \alpha \times \phi_{late}(t) \times 0.4$ | 频域缩放参数1 |
| (8) | $s_2(t) = s_2^{base} + \alpha \times \phi_{late}(t) \times 0.3$ | 频域缩放参数2 |
| (9) | $w_{attn}(t) = 0.5 + 0.5 \times \sigma(8 \times (0.3 - t_{norm}))$ | 注意力权重 |

### 2. Agent Attention模块

| 公式编号 | 公式 | 说明 |
|---------|------|------|
| (10) | $X_{seq} = \text{Flatten}(X, \text{dim}=2)^T$ | 序列化输入 |
| (11) | $X_{windows} = \text{Reshape}(X_{seq}, [B, \frac{H}{H_{sp}}, \frac{W}{W_{sp}}, H_{sp} \times W_{sp}, C])$ | 窗口分割 |
| (12) | $A = \text{AdaptiveAvgPool2d}(X_{windows})$ | Agent token生成 |
| (13) | $Q, K = \text{Linear}(A)$ | Query和Key计算 |
| (14) | $V = \text{DepthwiseConv2d}(X_{windows})$ | Value计算 |
| (15) | $\text{Attn}_{a2s} = \text{softmax}\left(\frac{Q \cdot K^T}{\sqrt{d}} + B_{agent}\right)$ | Agent到空间注意力 |
| (16) | $\text{Attn}_{s2a} = \text{softmax}\left(\frac{K \cdot Q^T}{\sqrt{d}} + B_{spatial}\right)$ | 空间到Agent注意力 |
| (17) | $X_{out} = \text{Attn}_{s2a} \cdot (\text{Attn}_{a2s} \cdot V) + \text{LEPE}(V)$ | 注意力输出 |
| (18) | $X_{final} = X_{out} \times w_{attn}(t)$ | 时间步权重调制 |

### 3. 自适应频域处理模块

| 公式编号 | 公式 | 说明 |
|---------|------|------|
| (19) | $X_{freq} = \text{FFT2D}(X)$ | 二维快速傅里叶变换 |
| (20) | $X_{freq} = \text{FFTShift}(X_{freq})$ | 频域中心化 |
| (21) | $\text{Mask}_{low}(u, v) = \begin{cases} s_1(t), & \text{if } \|u-u_c\| \leq \tau \text{ and } \|v-v_c\| \leq \tau \\ 1, & \text{otherwise} \end{cases}$ | 自适应滤波器 |
| (22) | $\tau(t) = \tau_{base} + \lfloor 5 \times (1 - t_{norm}) \rfloor$ | 动态阈值 |
| (23) | $X_{filtered} = X_{freq} \odot \text{Mask}_{low}(u, v)$ | 频域滤波 |
| (24) | $X_{enhanced} = \text{IFFT2D}(\text{IFFTShift}(X_{filtered}))$ | 逆变换 |

### 4. 完整前向传播

| 公式编号 | 公式 | 说明 |
|---------|------|------|
| (25) | $\hat{x}_{t-1} = \text{EvoTune}(x_t, t, \theta)$ | 完整前向传播 |
| (26) | $\mathcal{L} = \mathbb{E}_{x_0, \epsilon, t}\left[\|\epsilon - \epsilon_\theta(x_t, t)\|_2^2\right]$ | 损失函数 |

## 关键参数默认值

| 参数 | 符号 | 默认值 | 说明 |
|------|------|--------|------|
| 总时间步 | $T$ | 1000 | 扩散过程总步数 |
| 进化强度 | $\alpha$ | 0.3 | 参数变化幅度控制 |
| 基础骨干权重1 | $b_1^{base}$ | 1.5 | FreeU基础参数 |
| 基础骨干权重2 | $b_2^{base}$ | 1.2 | FreeU基础参数 |
| 基础频域缩放1 | $s_1^{base}$ | 0.8 | FreeU基础参数 |
| 基础频域缩放2 | $s_2^{base}$ | 0.5 | FreeU基础参数 |
| Agent数量 | $N_{agent}$ | 49 | Agent Attention参数 |
| 注意力头数 | $H$ | 8 | 多头注意力参数 |
| 频域阈值基础 | $\tau_{base}$ | 7 | 频域滤波参数 |

## 公式验证结果

### 时间步调谐验证

| 时间步$t$ | $t_{norm}$ | $\phi_{early}$ | $\phi_{late}$ | $b_1(t)$ | $b_2(t)$ | $s_1(t)$ | $s_2(t)$ | $w_{attn}(t)$ |
|-----------|------------|----------------|---------------|----------|----------|----------|----------|---------------|
| 900 | 0.900 | 0.731 | 0.001 | 1.610 | 1.200 | 0.800 | 0.500 | 0.504 |
| 500 | 0.500 | 0.047 | 0.047 | 1.507 | 1.204 | 0.806 | 0.504 | 0.584 |
| 100 | 0.100 | 0.001 | 0.731 | 1.500 | 1.266 | 0.888 | 0.566 | 0.916 |

### 维度验证（示例：B=2, C=64, H=W=32）

| 模块 | 输入维度 | 输出维度 | 中间维度 |
|------|----------|----------|----------|
| 序列化 | $2 \times 64 \times 32 \times 32$ | $2 \times 1024 \times 64$ | - |
| Agent生成 | $2 \times 1024 \times 64$ | $2 \times 49 \times 64$ | - |
| 注意力计算 | $Q,K: 2 \times 49 \times 64$ | $2 \times 1024 \times 64$ | $\text{Attn}: 2 \times 49 \times 1024$ |
| 频域处理 | $2 \times 64 \times 32 \times 32$ | $2 \times 64 \times 32 \times 32$ | $\text{FFT}: 2 \times 64 \times 32 \times 17$ |

## 算法复杂度

### 时间复杂度
- **参数调谐**: $O(1)$
- **Agent Attention**: $O(N \times N_{agent} + N_{agent}^2)$，其中$N = H \times W$
- **频域处理**: $O(N \log N)$
- **总体**: $O(N \log N + N \times N_{agent})$

### 空间复杂度
- **总体**: $O(N + N_{agent} \times C)$

由于$N_{agent} = 49 \ll N = H \times W$，EvoTune引入的额外计算开销相对较小。

## 数学性质

### 1. 参数有界性
$$\forall t \in [0,T]: \quad b_i(t) \in [b_i^{base}, b_i^{base} + 0.5\alpha]$$
$$\forall t \in [0,T]: \quad s_i(t) \in [s_i^{base}, s_i^{base} + 0.4\alpha]$$

### 2. 连续性
所有参数调谐函数基于连续的sigmoid函数，保证参数变化的平滑性。

### 3. 单调性
- $\phi_{early}(t)$关于$t_{norm}$单调递增
- $\phi_{late}(t)$关于$t_{norm}$单调递减
- 保证参数演化的稳定性

### 4. 收敛性
当$\alpha \leq 0.5$时，EvoTune的参数调谐过程在$L_2$范数下收敛。

## 实现注意事项

1. **数值稳定性**: sigmoid函数使用数值稳定实现
2. **精度控制**: 频域处理采用双精度计算
3. **内存优化**: Agent Attention采用分块计算
4. **并行化**: 频域变换支持批量并行处理

这些公式构成了EvoTune方法的完整数学基础，确保了算法的理论正确性和实现可靠性。
