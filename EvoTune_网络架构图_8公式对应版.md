# EvoTune网络架构图 - 8公式对应版

## 🎯 基于您原始UNet图的改进版本

### 📐 **改进后的网络架构图设计**

```
                    时间步 t
                       ↓
              ┌─────────────────┐
              │   公式① 时间步标准化   │
              │    t̃ = t/T      │
              └─────────────────┘
                       ↓
              ┌─────────────────┐
              │  公式②③ 相位函数计算  │
              │ φ_early, φ_late │
              └─────────────────┘
                       ↓
         ┌─────────────────────────────────┐
         │        公式④⑤⑥⑦⑧ 参数生成器        │
         │   b₁(t), b₂(t), s₁(t), s₂(t)   │
         │         w_attn(t)              │
         └─────────────────────────────────┘
                       ↓
    ┌─────────┬─────────┬─────────┬─────────┐
    │  b₁(t)  │  b₂(t)  │s₁(t),s₂(t)│w_attn(t)│
    │骨干特征1 │骨干特征2 │ 频域滤波  │ 注意力  │
    └─────────┴─────────┴─────────┴─────────┘
         ↓         ↓         ↓         ↓

输入图像 → [编码器E1] → [编码器中间层] → [编码器EN] → [解码器D1] → [解码器中间层] → [解码器DN] → 输出图像
    ↓           ↓                                    ↑                                    ↑
    │      ×b₁(t)调制                           ×b₂(t)调制                                │
    │           ↓                                    ↑                                    │
    └─────── 跳跃连接 ────────────────────────────────┘                                    │
                                                                                        │
                                                                              频域滤波×s₁,s₂
                                                                                        │
                                                                                        ↓
                                                                              Agent Attention
                                                                                ×w_attn
```

### 🔍 **详细的模块对应关系**

#### 🏗️ **EvoTune调谐器部分**
```
┌─────────────────────────────────────────┐
│              EvoTune调谐器                │
├─────────────────────────────────────────┤
│  输入: 时间步 t                           │
│  ↓                                      │
│  公式①: t̃ = t/1000                      │
│  ↓                                      │
│  公式②: φ_early = σ(10(t̃-0.8))          │
│  公式③: φ_late = σ(10(0.2-t̃))           │
│  ↓                                      │
│  公式④: b₁(t) = 1.5 + 0.3×φ_early×0.5   │
│  公式⑤: b₂(t) = 1.2 + 0.3×φ_late×0.3    │
│  公式⑥: s₁(t) = 0.8 + 0.3×φ_late×0.4    │
│  公式⑦: s₂(t) = 0.5 + 0.3×φ_late×0.3    │
│  公式⑧: w_attn = 0.5 + 0.5×σ(8(0.3-t̃)) │
│  ↓                                      │
│  输出: b₁(t), b₂(t), s₁(t), s₂(t), w_attn│
└─────────────────────────────────────────┘
```

#### 🎨 **UNet主体结构部分**
```
输入图像 x_t
    ↓
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│  编码器E1    │ →  │ 编码器中间层  │ →  │  编码器EN    │
│             │    │             │    │             │
└─────────────┘    └─────────────┘    └─────────────┘
    ↓                                        ↓
    │                                   ×b₁(t)调制
    │                                        ↓
    │                                ┌─────────────┐
    │                                │  解码器D1    │
    │                                │             │
    │                                └─────────────┘
    │                                        ↑
    │                                   ×b₂(t)调制
    │                                        ↑
    └─────────── 跳跃连接 ─────────────────────┘
                                            ↓
                                    ┌─────────────┐
                                    │ 解码器中间层  │
                                    │             │
                                    └─────────────┘
                                            ↓
                                    ┌─────────────┐
                                    │  解码器DN    │
                                    │             │
                                    └─────────────┘
                                            ↓
                                    频域滤波×s₁(t),s₂(t)
                                            ↓
                                    Agent Attention×w_attn(t)
                                            ↓
                                    输出图像 x_{t-1}
```

### 📊 **公式作用位置标注**

| 公式编号 | 作用位置 | 具体功能 | 在图中的位置 |
|---------|----------|----------|-------------|
| **公式①** | EvoTune调谐器入口 | 时间步标准化 | 调谐器第1层 |
| **公式②** | EvoTune调谐器核心 | 早期相位计算 | 调谐器第2层 |
| **公式③** | EvoTune调谐器核心 | 后期相位计算 | 调谐器第2层 |
| **公式④** | EvoTune调谐器输出 | 骨干特征权重1生成 | 调谐器第3层 |
| **公式⑤** | EvoTune调谐器输出 | 骨干特征权重2生成 | 调谐器第3层 |
| **公式⑥** | EvoTune调谐器输出 | 频域滤波参数1生成 | 调谐器第3层 |
| **公式⑦** | EvoTune调谐器输出 | 频域滤波参数2生成 | 调谐器第3层 |
| **公式⑧** | EvoTune调谐器输出 | 注意力权重生成 | 调谐器第3层 |

### 🎯 **参数应用位置**

| 参数 | 应用位置 | 调制对象 | 物理意义 |
|------|----------|----------|----------|
| **b₁(t)** | 编码器输出 → 解码器输入 | UNet骨干特征 | 增强主干信息传递 |
| **b₂(t)** | 编码器 → 解码器跳跃连接 | UNet跳跃连接 | 增强细节信息传递 |
| **s₁(t)** | 解码器输出 → 频域滤波 | 高频分量 | 控制高频细节保留 |
| **s₂(t)** | 解码器输出 → 频域滤波 | 低频分量 | 控制低频结构保留 |
| **w_attn(t)** | 频域滤波 → Agent Attention | 注意力权重 | 控制水油区域关注度 |

### 🌊 **时间步感知的动态调制过程**

#### 🌅 **早期阶段** (t=800-1000, t̃=0.8-1.0)
```
φ_early ≈ 1, φ_late ≈ 0
    ↓
b₁(t) ≈ 1.575 (增强), b₂(t) ≈ 1.2 (基础)
s₁(t) ≈ 0.8 (基础), s₂(t) ≈ 0.5 (基础)
    ↓
主要增强骨干特征，建立基础结构
```

#### 🌆 **中期阶段** (t=200-800, t̃=0.2-0.8)
```
φ_early ≈ 0, φ_late ≈ 0
    ↓
所有参数接近基础值
    ↓
系统处于平衡状态，稳定过渡
```

#### 🌇 **后期阶段** (t=0-200, t̃=0.0-0.2)
```
φ_early ≈ 0, φ_late ≈ 1
    ↓
b₁(t) ≈ 1.5 (基础), b₂(t) ≈ 1.29 (增强)
s₁(t) ≈ 0.92 (增强), s₂(t) ≈ 0.59 (增强)
    ↓
协同增强跳跃连接和频域滤波，优化细节
```

### 🎨 **绘图建议**

#### 📐 **基于您原始UNet图的改进**
1. **保留您的UNet主体结构**：编码器-解码器-跳跃连接
2. **在上方添加EvoTune调谐器**：显示8个公式的计算流程
3. **用不同颜色的箭头**：标示不同参数的作用位置
4. **添加公式编号标注**：每个模块标注对应的公式

#### 🎯 **颜色编码建议**
- **蓝色**：时间步输入和图像输入/输出
- **紫色**：EvoTune调谐器（公式①②③）
- **橙色**：参数生成（公式④⑤⑥⑦⑧）
- **绿色**：UNet主体结构
- **红色**：参数应用和调制点

#### 📝 **标注要求**
- 每个公式用圆圈标注编号①②③④⑤⑥⑦⑧
- 参数流向用实线箭头
- 数据流向用虚线箭头
- 中文标签，符合学术论文标准

这样的设计既保持了您原始图的优点，又清晰展示了8个公式在网络中的具体作用位置和相互关系！
