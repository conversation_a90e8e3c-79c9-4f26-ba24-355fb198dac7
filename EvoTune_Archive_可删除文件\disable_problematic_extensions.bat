@echo off
echo ========================================
echo    禁用有问题的扩展
echo ========================================
echo.

cd /d "h:\StableDiffusion_Gaijin\sd-webui-aki-v4.4\extensions"

echo 禁用TensorRT扩展...
if exist "Stable-Diffusion-WebUI-TensorRT-main" (
    ren "Stable-Diffusion-WebUI-TensorRT-main" "Stable-Diffusion-WebUI-TensorRT-main.disabled"
    echo ✓ TensorRT扩展已禁用
)

echo 禁用infinite-zoom扩展...
if exist "infinite-zoom-automatic1111-webui" (
    ren "infinite-zoom-automatic1111-webui" "infinite-zoom-automatic1111-webui.disabled"
    echo ✓ infinite-zoom扩展已禁用
)

echo 禁用supermerger扩展...
if exist "sd-webui-supermerger" (
    ren "sd-webui-supermerger" "sd-webui-supermerger.disabled"
    echo ✓ supermerger扩展已禁用
)

echo.
echo 完成！重启WebUI后这些错误将消失。
echo 如需恢复扩展，将.disabled后缀删除即可。
pause
