"""
EvoTune SD WebUI集成脚本
实现与Stable Diffusion WebUI的无缝集成
"""

import gradio as gr
import torch
import logging
from typing import Optional, Dict, Any
import os
import sys

# 添加模块路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)

from modules.evotune_wrapper import EvoTuneWrapper, wrap_unet_with_evotune, EvoTuneMonitor
from modules.evotune_controller import EvoTuneController

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EvoTuneScript:
    """
    EvoTune WebUI脚本类
    """
    
    def __init__(self):
        self.wrapped_unet = None
        self.original_unet = None
        self.monitor = None
        self.enabled = False
        
        # 默认参数
        self.default_params = {
            'b1_range': (1.0, 1.3),
            'b2_range': (0.8, 1.2),
            's1_range': (0.6, 1.0),
            's2_range': (0.4, 0.8),
            'w_attn_range': (0.5, 0.95),
            'target_channels': 320,
            'num_agents': 49,
            'agent_dim': 64
        }
        
        logger.info("EvoTuneScript initialized")
    
    def title(self):
        return "EvoTune"
    
    def show(self, is_img2img):
        return True
    
    def ui(self, is_img2img):
        """创建用户界面"""
        with gr.Group():
            with gr.Accordion("EvoTune Settings", open=False):
                # 主开关
                enabled = gr.Checkbox(
                    label="Enable EvoTune",
                    value=False,
                    info="Enable EvoTune for enhanced water interference generation"
                )
                
                # 参数设置
                with gr.Row():
                    with gr.Column():
                        gr.Markdown("### Backbone Feature Weights")
                        b1_min = gr.Slider(
                            minimum=0.5, maximum=2.0, step=0.1, value=1.0,
                            label="b1 Min", info="Backbone weight minimum"
                        )
                        b1_max = gr.Slider(
                            minimum=0.5, maximum=2.0, step=0.1, value=1.3,
                            label="b1 Max", info="Backbone weight maximum"
                        )
                        
                        b2_min = gr.Slider(
                            minimum=0.5, maximum=2.0, step=0.1, value=0.8,
                            label="b2 Min", info="Skip connection weight minimum"
                        )
                        b2_max = gr.Slider(
                            minimum=0.5, maximum=2.0, step=0.1, value=1.2,
                            label="b2 Max", info="Skip connection weight maximum"
                        )
                    
                    with gr.Column():
                        gr.Markdown("### Frequency Domain Parameters")
                        s1_min = gr.Slider(
                            minimum=0.1, maximum=1.5, step=0.1, value=0.6,
                            label="s1 Min", info="Low frequency filter minimum"
                        )
                        s1_max = gr.Slider(
                            minimum=0.1, maximum=1.5, step=0.1, value=1.0,
                            label="s1 Max", info="Low frequency filter maximum"
                        )
                        
                        s2_min = gr.Slider(
                            minimum=0.1, maximum=1.5, step=0.1, value=0.4,
                            label="s2 Min", info="Mid frequency filter minimum"
                        )
                        s2_max = gr.Slider(
                            minimum=0.1, maximum=1.5, step=0.1, value=0.8,
                            label="s2 Max", info="Mid frequency filter maximum"
                        )
                
                with gr.Row():
                    w_attn_min = gr.Slider(
                        minimum=0.1, maximum=1.0, step=0.05, value=0.5,
                        label="Attention Weight Min", info="Agent attention minimum weight"
                    )
                    w_attn_max = gr.Slider(
                        minimum=0.1, maximum=1.0, step=0.05, value=0.95,
                        label="Attention Weight Max", info="Agent attention maximum weight"
                    )
                
                # 高级设置
                with gr.Accordion("Advanced Settings", open=False):
                    target_channels = gr.Slider(
                        minimum=64, maximum=1024, step=64, value=320,
                        label="Target Channels", info="EvoTune processing channels"
                    )
                    num_agents = gr.Slider(
                        minimum=16, maximum=100, step=1, value=49,
                        label="Number of Agents", info="Agent tokens count (49=7x7)"
                    )
                    agent_dim = gr.Slider(
                        minimum=32, maximum=128, step=16, value=64,
                        label="Agent Dimension", info="Agent token dimension"
                    )
                
                # 性能监控
                with gr.Accordion("Performance Monitoring", open=False):
                    performance_enabled = gr.Checkbox(
                        label="Enable Performance Monitoring",
                        value=True,
                        info="Monitor EvoTune performance impact"
                    )
                    
                    refresh_stats = gr.Button("Refresh Stats")
                    stats_display = gr.JSON(label="Performance Statistics")
                    
                    # 绑定刷新事件
                    refresh_stats.click(
                        fn=self.get_performance_stats,
                        outputs=stats_display
                    )
                
                # 状态显示
                status_display = gr.Textbox(
                    label="EvoTune Status",
                    value="Disabled",
                    interactive=False
                )
                
                # 绑定启用/禁用事件
                enabled.change(
                    fn=self.toggle_evotune,
                    inputs=[enabled, b1_min, b1_max, b2_min, b2_max, 
                           s1_min, s1_max, s2_min, s2_max, w_attn_min, w_attn_max,
                           target_channels, num_agents, agent_dim, performance_enabled],
                    outputs=status_display
                )
        
        return [enabled, b1_min, b1_max, b2_min, b2_max, 
                s1_min, s1_max, s2_min, s2_max, w_attn_min, w_attn_max,
                target_channels, num_agents, agent_dim, performance_enabled]
    
    def toggle_evotune(self, enabled, b1_min, b1_max, b2_min, b2_max,
                      s1_min, s1_max, s2_min, s2_max, w_attn_min, w_attn_max,
                      target_channels, num_agents, agent_dim, performance_enabled):
        """切换EvoTune启用状态"""
        try:
            if enabled:
                # 启用EvoTune
                if self.wrapped_unet is None:
                    # 获取原始UNet
                    unet = self._get_current_unet()
                    if unet is None:
                        return "Error: Could not find UNet model"
                    
                    # 创建包装器
                    self.wrapped_unet = wrap_unet_with_evotune(
                        unet, 
                        enabled=True,
                        target_channels=int(target_channels),
                        performance_monitoring=performance_enabled
                    )
                    
                    # 更新参数
                    self._update_parameters(
                        b1_min, b1_max, b2_min, b2_max,
                        s1_min, s1_max, s2_min, s2_max, 
                        w_attn_min, w_attn_max,
                        int(num_agents), int(agent_dim)
                    )
                    
                    # 替换UNet
                    self._replace_unet(self.wrapped_unet)
                    
                    # 创建监控器
                    self.monitor = EvoTuneMonitor(self.wrapped_unet)
                    
                    self.enabled = True
                    return "EvoTune Enabled Successfully"
                else:
                    self.wrapped_unet.enable_evotune()
                    self.enabled = True
                    return "EvoTune Re-enabled"
            else:
                # 禁用EvoTune
                if self.wrapped_unet is not None:
                    self.wrapped_unet.disable_evotune()
                    self.enabled = False
                    return "EvoTune Disabled"
                else:
                    return "EvoTune Not Initialized"
                    
        except Exception as e:
            logger.error(f"Failed to toggle EvoTune: {e}")
            return f"Error: {str(e)}"
    
    def _get_current_unet(self):
        """获取当前的UNet模型"""
        try:
            # 尝试从shared模块获取
            import modules.shared as shared
            if hasattr(shared, 'sd_model') and shared.sd_model is not None:
                return shared.sd_model.model.diffusion_model
            
            # 备用方法：从全局变量获取
            import modules.sd_models as sd_models
            if hasattr(sd_models, 'model_data') and sd_models.model_data.sd_model is not None:
                return sd_models.model_data.sd_model.model.diffusion_model
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to get UNet: {e}")
            return None
    
    def _replace_unet(self, new_unet):
        """替换UNet模型"""
        try:
            import modules.shared as shared
            if hasattr(shared, 'sd_model') and shared.sd_model is not None:
                # 保存原始UNet
                if self.original_unet is None:
                    self.original_unet = shared.sd_model.model.diffusion_model
                
                # 替换为包装器
                shared.sd_model.model.diffusion_model = new_unet
                logger.info("UNet replaced with EvoTune wrapper")
                
        except Exception as e:
            logger.error(f"Failed to replace UNet: {e}")
            raise
    
    def _update_parameters(self, b1_min, b1_max, b2_min, b2_max,
                          s1_min, s1_max, s2_min, s2_max,
                          w_attn_min, w_attn_max, num_agents, agent_dim):
        """更新EvoTune参数"""
        if self.wrapped_unet is None or self.wrapped_unet.evotune_controller is None:
            return
        
        try:
            # 更新时间步调谐器参数
            scheduler = self.wrapped_unet.evotune_controller.timestep_scheduler
            scheduler.b1_range = (b1_min, b1_max)
            scheduler.b2_range = (b2_min, b2_max)
            scheduler.s1_range = (s1_min, s1_max)
            scheduler.s2_range = (s2_min, s2_max)
            scheduler.w_attn_range = (w_attn_min, w_attn_max)
            
            logger.info("EvoTune parameters updated")
            
        except Exception as e:
            logger.error(f"Failed to update parameters: {e}")
    
    def get_performance_stats(self):
        """获取性能统计信息"""
        if self.wrapped_unet is None:
            return {"status": "EvoTune not initialized"}
        
        try:
            stats = self.wrapped_unet.get_performance_stats()
            status = self.wrapped_unet.get_modulation_status()
            
            return {
                "performance": stats,
                "status": status,
                "enabled": self.enabled
            }
            
        except Exception as e:
            logger.error(f"Failed to get stats: {e}")
            return {"error": str(e)}
    
    def process(self, p, *args):
        """处理生成请求"""
        # 这里可以添加生成前的预处理逻辑
        if self.enabled and self.monitor is not None:
            logger.info("Processing with EvoTune enabled")
        
        return p
    
    def postprocess(self, p, processed, *args):
        """后处理生成结果"""
        # 这里可以添加生成后的后处理逻辑
        if self.enabled and self.monitor is not None:
            # 记录生成统计
            stats = self.wrapped_unet.get_performance_stats()
            logger.info(f"Generation completed with EvoTune: {stats}")
        
        return processed


# 创建脚本实例
def create_evotune_script():
    """创建EvoTune脚本实例"""
    return EvoTuneScript()


# WebUI集成点
try:
    # 尝试注册为WebUI脚本
    import modules.scripts as scripts
    
    class EvoTuneWebUIScript(scripts.Script):
        def __init__(self):
            super().__init__()
            self.evotune_script = EvoTuneScript()
        
        def title(self):
            return self.evotune_script.title()
        
        def show(self, is_img2img):
            return self.evotune_script.show(is_img2img)
        
        def ui(self, is_img2img):
            return self.evotune_script.ui(is_img2img)
        
        def process(self, p, *args):
            return self.evotune_script.process(p, *args)
        
        def postprocess(self, p, processed, *args):
            return self.evotune_script.postprocess(p, processed, *args)
    
    # 注册脚本
    evotune_webui_script = EvoTuneWebUIScript()
    
except ImportError:
    logger.warning("WebUI modules not available, running in standalone mode")
    evotune_script = EvoTuneScript()
