@echo off
echo ========================================
echo    SD WebUI 最小化启动 (排除干扰)
echo ========================================
echo.

cd /d "h:\StableDiffusion_Gaijin\sd-webui-aki-v4.4"

echo 临时禁用所有可能冲突的扩展...

REM 禁用TensorRT
if exist "extensions\Stable-Diffusion-WebUI-TensorRT-main" (
    ren "extensions\Stable-Diffusion-WebUI-TensorRT-main" "extensions\Stable-Diffusion-WebUI-TensorRT-main.disabled"
    echo ✓ TensorRT已禁用
)

REM 禁用infinite-zoom
if exist "extensions\infinite-zoom-automatic1111-webui" (
    ren "extensions\infinite-zoom-automatic1111-webui" "extensions\infinite-zoom-automatic1111-webui.disabled"
    echo ✓ infinite-zoom已禁用
)

REM 禁用supermerger
if exist "extensions\sd-webui-supermerger" (
    ren "extensions\sd-webui-supermerger" "extensions\sd-webui-supermerger.disabled"
    echo ✓ supermerger已禁用
)

REM 禁用其他可能有问题的扩展
if exist "extensions\sd-webui-additional-networks" (
    ren "extensions\sd-webui-additional-networks" "extensions\sd-webui-additional-networks.disabled"
    echo ✓ additional-networks已禁用
)

echo.
echo 使用最小参数启动WebUI...
echo 这将跳过大部分优化，专注于基本功能
echo.

REM 最小化启动参数
set COMMANDLINE_ARGS=--skip-python-version-check --no-half --precision full --disable-safe-unpickle --api

echo 启动参数: %COMMANDLINE_ARGS%
echo.

python launch.py %COMMANDLINE_ARGS%

echo.
echo 恢复扩展...

REM 恢复扩展
if exist "extensions\Stable-Diffusion-WebUI-TensorRT-main.disabled" (
    ren "extensions\Stable-Diffusion-WebUI-TensorRT-main.disabled" "extensions\Stable-Diffusion-WebUI-TensorRT-main"
)

if exist "extensions\infinite-zoom-automatic1111-webui.disabled" (
    ren "extensions\infinite-zoom-automatic1111-webui.disabled" "extensions\infinite-zoom-automatic1111-webui"
)

if exist "extensions\sd-webui-supermerger.disabled" (
    ren "extensions\sd-webui-supermerger.disabled" "extensions\sd-webui-supermerger"
)

if exist "extensions\sd-webui-additional-networks.disabled" (
    ren "extensions\sd-webui-additional-networks.disabled" "extensions\sd-webui-additional-networks"
)

echo 扩展已恢复
pause
