"""
EvoTune完整系统
基于扩散模型的时间步调谐方法，专门针对积水干扰的渗漏油图像生成优化

核心创新：
1. 动态时间步调谐 - 根据扩散过程动态调整FreeU参数
2. Agent Attention集成 - 精确控制水油区域注意力  
3. 多尺度频域处理 - 增强水纹理真实感
4. 自适应特征融合 - 优化水油边界生成质量
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.fft as fft
import numpy as np
from typing import Optional, Tuple, Dict, Any
import math

# 导入我们的模块
from EvoTune_Core import EvoTuneScheduler, EvoTuneUNet
from EvoTune_AgentAttention import WaterOilAgentAttention, MultiScaleWaterOilAttention

class EvoTuneCompleteSystem(nn.Module):
    """
    EvoTune完整系统
    集成所有创新组件的完整解决方案
    """
    
    def __init__(self, 
                 model_channels: int = 64,
                 num_classes: Optional[int] = None,
                 # FreeU基础参数
                 base_b1: float = 1.5,
                 base_b2: float = 1.2,
                 base_s1: float = 0.8,
                 base_s2: float = 0.5,
                 # EvoTune参数
                 evolution_strength: float = 0.3,
                 use_agent_attention: bool = True,
                 use_adaptive_fourier: bool = True,
                 use_multiscale: bool = True,
                 # Agent Attention参数
                 attention_resolutions: list = [64, 32, 16],
                 num_heads: int = 8):
        
        super().__init__()
        
        self.model_channels = model_channels
        self.use_agent_attention = use_agent_attention
        self.use_adaptive_fourier = use_adaptive_fourier
        self.use_multiscale = use_multiscale
        
        # 核心调度器
        self.scheduler = EvoTuneScheduler(
            base_b1, base_b2, base_s1, base_s2, evolution_strength
        )
        
        # 基础UNet结构
        self.input_conv = nn.Conv2d(3, model_channels, 3, padding=1)
        self.middle_conv = nn.Conv2d(model_channels, model_channels, 3, padding=1)
        self.output_conv = nn.Conv2d(model_channels, model_channels, 3, padding=1)
        self.final_conv = nn.Conv2d(model_channels, 3, 3, padding=1)
        
        # 时间嵌入
        self.time_embed = nn.Sequential(
            nn.Linear(model_channels, model_channels * 4),
            nn.GELU(),
            nn.Linear(model_channels * 4, model_channels)
        )
        
        # 类别嵌入（如果需要）
        if num_classes is not None:
            self.label_emb = nn.Embedding(num_classes, model_channels)
        self.num_classes = num_classes
        
        # Agent Attention模块
        if use_agent_attention:
            if use_multiscale:
                self.water_oil_attention = MultiScaleWaterOilAttention(
                    dim=model_channels,
                    resolutions=attention_resolutions,
                    num_heads=num_heads
                )
            else:
                self.water_oil_attention = WaterOilAgentAttention(
                    dim=model_channels,
                    resolution=32,  # 默认分辨率
                    num_heads=num_heads
                )
        
        # 自适应频域处理模块
        if use_adaptive_fourier:
            self.fourier_processor = AdaptiveFourierProcessor(model_channels)
        
        # 特征融合模块
        self.feature_fusion = FeatureFusionModule(model_channels)
        
        # 水油区域检测器（辅助模块）
        self.water_oil_detector = WaterOilRegionDetector(model_channels)
        
    def timestep_embedding(self, timesteps, dim):
        """时间步嵌入"""
        half = dim // 2
        freqs = torch.exp(
            -math.log(10000) * torch.arange(start=0, end=half, dtype=torch.float32) / half
        ).to(device=timesteps.device)
        args = timesteps[:, None].float() * freqs[None]
        embedding = torch.cat([torch.cos(args), torch.sin(args)], dim=-1)
        if dim % 2:
            embedding = torch.cat([embedding, torch.zeros_like(embedding[:, :1])], dim=-1)
        return embedding
    
    def forward(self, x: torch.Tensor, timesteps: torch.Tensor, 
                context: Optional[torch.Tensor] = None, 
                y: Optional[torch.Tensor] = None, **kwargs) -> torch.Tensor:
        """
        EvoTune完整前向传播
        
        Args:
            x: 输入图像 [B, 3, H, W]
            timesteps: 时间步 [B]
            context: 上下文信息（可选）
            y: 类别标签（可选）
        """
        B, C, H, W = x.shape
        
        # 获取自适应参数
        b1, b2, s1, s2 = self.scheduler.get_adaptive_params(timesteps)
        attention_weight = self.scheduler.get_water_oil_attention_weights(timesteps)
        
        # 时间嵌入
        t_emb = self.timestep_embedding(timesteps, self.model_channels)
        t_emb = self.time_embed(t_emb)
        
        # 类别嵌入
        if self.num_classes is not None and y is not None:
            t_emb = t_emb + self.label_emb(y)
        
        # 输入处理
        h = self.input_conv(x)
        
        # 应用Agent Attention（输入阶段）
        if self.use_agent_attention:
            h_attended = self.water_oil_attention(h, timestep_weight=attention_weight)
            h = self.feature_fusion(h, h_attended, weight=b1)
        
        # 中间处理
        h = self.middle_conv(h)
        
        # 应用自适应频域处理
        if self.use_adaptive_fourier:
            h_fourier = self.fourier_processor(h, s1, s2, attention_weight)
            h = self.feature_fusion(h, h_fourier, weight=b2)
        
        # 水油区域检测和增强
        water_oil_mask = self.water_oil_detector(h)
        h = h * (1 + 0.2 * water_oil_mask * attention_weight)  # 增强水油区域
        
        # 输出处理
        h = self.output_conv(h)
        
        # 最终Agent Attention（输出阶段）
        if self.use_agent_attention:
            h_final_attended = self.water_oil_attention(h, timestep_weight=attention_weight * 0.8)
            h = self.feature_fusion(h, h_final_attended, weight=(b1 + b2) / 2)
        
        # 最终输出
        output = self.final_conv(h)
        
        return output


class AdaptiveFourierProcessor(nn.Module):
    """自适应频域处理器"""
    
    def __init__(self, channels):
        super().__init__()
        self.channels = channels
        self.freq_conv = nn.Conv2d(channels, channels, 1)
        
    def forward(self, x, s1, s2, attention_weight):
        """应用自适应频域滤波"""
        B, C, H, W = x.shape
        
        # FFT
        x_freq = fft.fftn(x, dim=(-2, -1))
        x_freq = fft.fftshift(x_freq, dim=(-2, -1))
        
        # 自适应滤波
        center_h, center_w = H // 2, W // 2
        
        # 根据s1, s2和attention_weight调整滤波强度
        low_freq_size = max(1, int(min(H, W) * s1 * attention_weight * 0.1))
        high_freq_scale = 1.0 + s2 * attention_weight * 0.5
        
        # 创建滤波掩码
        mask = torch.ones_like(x_freq)
        mask[..., 
             center_h-low_freq_size:center_h+low_freq_size,
             center_w-low_freq_size:center_w+low_freq_size] *= high_freq_scale
        
        x_freq_filtered = x_freq * mask
        
        # IFFT
        x_freq_filtered = fft.ifftshift(x_freq_filtered, dim=(-2, -1))
        x_filtered = fft.ifftn(x_freq_filtered, dim=(-2, -1)).real
        
        # 特征增强
        x_enhanced = self.freq_conv(x_filtered)
        
        return x_enhanced


class FeatureFusionModule(nn.Module):
    """特征融合模块"""
    
    def __init__(self, channels):
        super().__init__()
        self.fusion_conv = nn.Conv2d(channels * 2, channels, 1)
        self.gate = nn.Sequential(
            nn.Conv2d(channels * 2, channels, 1),
            nn.Sigmoid()
        )
        
    def forward(self, x1, x2, weight=1.0):
        """融合两个特征"""
        combined = torch.cat([x1, x2], dim=1)
        gate = self.gate(combined)
        fused = self.fusion_conv(combined)
        
        # 加权融合
        output = x1 * (1 - gate * weight) + fused * gate * weight
        return output


class WaterOilRegionDetector(nn.Module):
    """水油区域检测器"""
    
    def __init__(self, channels):
        super().__init__()
        self.detector = nn.Sequential(
            nn.Conv2d(channels, channels // 2, 3, padding=1),
            nn.ReLU(),
            nn.Conv2d(channels // 2, 1, 1),
            nn.Sigmoid()
        )
        
    def forward(self, x):
        """检测水油区域"""
        return self.detector(x)


# 测试和演示代码
if __name__ == "__main__":
    print("=" * 60)
    print("    EvoTune完整系统测试")
    print("=" * 60)
    
    # 创建完整系统
    evotune_system = EvoTuneCompleteSystem(
        model_channels=64,
        num_classes=None,
        use_agent_attention=True,
        use_adaptive_fourier=True,
        use_multiscale=True
    )
    
    print(f"模型参数数量: {sum(p.numel() for p in evotune_system.parameters()):,}")
    
    # 测试输入
    batch_size = 2
    x = torch.randn(batch_size, 3, 256, 256)
    timesteps = torch.randint(0, 1000, (batch_size,))
    
    print(f"\n输入形状: {x.shape}")
    print(f"时间步: {timesteps}")
    
    # 前向传播测试
    with torch.no_grad():
        output = evotune_system(x, timesteps)
        print(f"输出形状: {output.shape}")
    
    # 测试不同时间步的参数变化
    print(f"\n时间步调谐测试:")
    scheduler = evotune_system.scheduler
    
    for t in [900, 500, 100]:  # 早期、中期、后期
        b1, b2, s1, s2 = scheduler.get_adaptive_params(torch.tensor([t]))
        attention_weight = scheduler.get_water_oil_attention_weights(torch.tensor([t]))
        print(f"时间步{t:3d}: b1={b1:.3f}, b2={b2:.3f}, s1={s1:.3f}, s2={s2:.3f}, attention={attention_weight:.3f}")
    
    print(f"\n✅ EvoTune完整系统测试成功!")
    print(f"🎯 系统已准备好集成到SD WebUI中")
    print("=" * 60)
