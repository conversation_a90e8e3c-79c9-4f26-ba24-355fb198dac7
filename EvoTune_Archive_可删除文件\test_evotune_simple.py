"""
简单的EvoTune测试脚本
验证EvoTune模块是否正常工作
"""

import sys
import os

# 添加路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_evotune_import():
    """测试EvoTune模块导入"""
    try:
        from EvoTune_UNetModel import EvoTune_UNetModel
        print("✅ EvoTune_UNetModel 导入成功")
        return True
    except ImportError as e:
        print(f"❌ EvoTune_UNetModel 导入失败: {e}")
        return False

def test_evotune_creation():
    """测试EvoTune模型创建"""
    try:
        from EvoTune_UNetModel import EvoTune_UNetModel
        
        model = EvoTune_UNetModel(
            b1=1.5, b2=1.2, s1=0.8, s2=0.5,
            model_channels=64,
            use_agent_attention=False,  # 禁用以避免问题
            use_adaptive_fourier=True
        )
        
        print("✅ EvoTune模型创建成功")
        print(f"   参数量: {sum(p.numel() for p in model.parameters()):,}")
        return True
    except Exception as e:
        print(f"❌ EvoTune模型创建失败: {e}")
        return False

def test_evotune_forward():
    """测试EvoTune前向传播"""
    try:
        import torch
        from EvoTune_UNetModel import EvoTune_UNetModel
        
        model = EvoTune_UNetModel(
            b1=1.5, b2=1.2, s1=0.8, s2=0.5,
            model_channels=64,
            use_agent_attention=False,
            use_adaptive_fourier=True
        )
        
        # 测试输入
        test_input = torch.randn(1, 3, 256, 256)
        test_timesteps = torch.tensor([500])
        
        with torch.no_grad():
            output = model(test_input, timesteps=test_timesteps)
        
        print("✅ EvoTune前向传播成功")
        print(f"   输入尺寸: {test_input.shape}")
        print(f"   输出尺寸: {output.shape}")
        return True
    except Exception as e:
        print(f"❌ EvoTune前向传播失败: {e}")
        return False

def test_webui_integration():
    """测试WebUI集成"""
    try:
        # 检查扩展文件是否存在
        extension_path = "sd-webui-aki-v4.4/extensions/evotune/scripts/evotune_extension.py"
        if os.path.exists(extension_path):
            print("✅ EvoTune扩展文件存在")
            
            # 尝试导入扩展
            sys.path.append("sd-webui-aki-v4.4/extensions/evotune")
            
            # 简单的语法检查
            with open(extension_path, 'r', encoding='utf-8') as f:
                content = f.read()
                if 'EvoTune' in content and 'script_callbacks' in content:
                    print("✅ EvoTune扩展内容正确")
                    return True
                else:
                    print("❌ EvoTune扩展内容有问题")
                    return False
        else:
            print("❌ EvoTune扩展文件不存在")
            return False
    except Exception as e:
        print(f"❌ WebUI集成测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=== EvoTune 简单测试 ===")
    print()
    
    tests = [
        ("模块导入", test_evotune_import),
        ("模型创建", test_evotune_creation),
        ("前向传播", test_evotune_forward),
        ("WebUI集成", test_webui_integration)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"测试 {test_name}...")
        if test_func():
            passed += 1
        print()
    
    print("="*40)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！EvoTune已准备就绪")
        print()
        print("使用方法:")
        print("1. 双击 start_webui_evotune_safe.bat 启动WebUI")
        print("2. 在浏览器中访问 http://localhost:7860")
        print("3. 进入设置 → EvoTune设置 调整参数")
        print("4. 在图生图模式下使用EvoTune")
    else:
        print("⚠️ 部分测试失败，请检查安装")
    
    print("="*40)

if __name__ == "__main__":
    main()
