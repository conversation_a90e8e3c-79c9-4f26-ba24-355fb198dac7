"""
EvoTune用户界面模块
提供独立的用户界面组件
"""

import gradio as gr
import json
import logging
from typing import Dict, Any, Optional, Callable
import time

logger = logging.getLogger(__name__)

class EvoTuneUI:
    """
    EvoTune用户界面类
    """
    
    def __init__(self, 
                 evotune_controller: Optional[Any] = None,
                 update_callback: Optional[Callable] = None):
        """
        初始化EvoTune UI
        
        Args:
            evotune_controller: EvoTune控制器实例
            update_callback: 参数更新回调函数
        """
        self.evotune_controller = evotune_controller
        self.update_callback = update_callback
        
        # 默认参数
        self.default_params = {
            'b1_range': (1.0, 1.3),
            'b2_range': (0.8, 1.2),
            's1_range': (0.6, 1.0),
            's2_range': (0.4, 0.8),
            'w_attn_range': (0.5, 0.95),
            'target_channels': 320,
            'num_agents': 49,
            'agent_dim': 64,
            'total_timesteps': 1000
        }
        
        # 预设配置
        self.presets = {
            'Conservative': {
                'b1_range': (1.0, 1.2),
                'b2_range': (0.9, 1.1),
                's1_range': (0.7, 0.9),
                's2_range': (0.5, 0.7),
                'w_attn_range': (0.6, 0.8)
            },
            'Balanced': {
                'b1_range': (1.0, 1.3),
                'b2_range': (0.8, 1.2),
                's1_range': (0.6, 1.0),
                's2_range': (0.4, 0.8),
                'w_attn_range': (0.5, 0.95)
            },
            'Aggressive': {
                'b1_range': (1.1, 1.5),
                'b2_range': (0.7, 1.3),
                's1_range': (0.5, 1.2),
                's2_range': (0.3, 0.9),
                'w_attn_range': (0.4, 1.0)
            }
        }
        
        logger.info("EvoTuneUI initialized")
    
    def create_main_interface(self) -> gr.Blocks:
        """
        创建主界面
        
        Returns:
            interface: Gradio界面
        """
        with gr.Blocks(title="EvoTune Control Panel") as interface:
            gr.Markdown("# EvoTune Control Panel")
            gr.Markdown("Advanced control interface for EvoTune parameters")
            
            with gr.Row():
                with gr.Column(scale=2):
                    # 主要控制面板
                    self._create_control_panel()
                
                with gr.Column(scale=1):
                    # 监控面板
                    self._create_monitoring_panel()
            
            with gr.Row():
                # 预设和导入导出
                self._create_preset_panel()
        
        return interface
    
    def _create_control_panel(self):
        """创建控制面板"""
        with gr.Group():
            gr.Markdown("## Parameter Control")
            
            # 主开关
            self.enabled_checkbox = gr.Checkbox(
                label="Enable EvoTune",
                value=False,
                info="Master switch for EvoTune functionality"
            )
            
            # 参数组
            with gr.Tabs():
                # 骨干特征参数
                with gr.TabItem("Backbone Features"):
                    self._create_backbone_controls()
                
                # 频域参数
                with gr.TabItem("Frequency Domain"):
                    self._create_frequency_controls()
                
                # 注意力参数
                with gr.TabItem("Agent Attention"):
                    self._create_attention_controls()
                
                # 高级参数
                with gr.TabItem("Advanced"):
                    self._create_advanced_controls()
    
    def _create_backbone_controls(self):
        """创建骨干特征控制"""
        gr.Markdown("### Backbone Feature Weights")
        gr.Markdown("Control the dynamic weighting of backbone features during diffusion")
        
        with gr.Row():
            self.b1_min = gr.Slider(
                minimum=0.5, maximum=2.0, step=0.05, value=1.0,
                label="b1 Minimum", 
                info="Early stage backbone weight (structure building)"
            )
            self.b1_max = gr.Slider(
                minimum=0.5, maximum=2.0, step=0.05, value=1.3,
                label="b1 Maximum",
                info="Late stage backbone weight (detail enhancement)"
            )
        
        with gr.Row():
            self.b2_min = gr.Slider(
                minimum=0.5, maximum=2.0, step=0.05, value=0.8,
                label="b2 Minimum",
                info="Early stage skip connection weight"
            )
            self.b2_max = gr.Slider(
                minimum=0.5, maximum=2.0, step=0.05, value=1.2,
                label="b2 Maximum",
                info="Late stage skip connection weight"
            )
        
        # 实时预览
        self.backbone_preview = gr.Plot(
            label="Backbone Weight Curves",
            value=self._generate_weight_plot('backbone')
        )
        
        # 绑定更新事件
        for slider in [self.b1_min, self.b1_max, self.b2_min, self.b2_max]:
            slider.change(
                fn=lambda *args: self._generate_weight_plot('backbone'),
                inputs=[self.b1_min, self.b1_max, self.b2_min, self.b2_max],
                outputs=self.backbone_preview
            )
    
    def _create_frequency_controls(self):
        """创建频域控制"""
        gr.Markdown("### Frequency Domain Processing")
        gr.Markdown("Control adaptive frequency filtering for water texture enhancement")
        
        with gr.Row():
            self.s1_min = gr.Slider(
                minimum=0.1, maximum=1.5, step=0.05, value=0.6,
                label="s1 Minimum (Low Freq)",
                info="Low frequency component control (water structure)"
            )
            self.s1_max = gr.Slider(
                minimum=0.1, maximum=1.5, step=0.05, value=1.0,
                label="s1 Maximum (Low Freq)",
                info="Enhanced low frequency processing"
            )
        
        with gr.Row():
            self.s2_min = gr.Slider(
                minimum=0.1, maximum=1.5, step=0.05, value=0.4,
                label="s2 Minimum (Mid Freq)",
                info="Mid frequency component control (water texture)"
            )
            self.s2_max = gr.Slider(
                minimum=0.1, maximum=1.5, step=0.05, value=0.8,
                label="s2 Maximum (Mid Freq)",
                info="Enhanced mid frequency processing"
            )
        
        # 频域可视化
        self.frequency_preview = gr.Plot(
            label="Frequency Response",
            value=self._generate_frequency_plot()
        )
        
        # 绑定更新事件
        for slider in [self.s1_min, self.s1_max, self.s2_min, self.s2_max]:
            slider.change(
                fn=self._generate_frequency_plot,
                inputs=[self.s1_min, self.s1_max, self.s2_min, self.s2_max],
                outputs=self.frequency_preview
            )
    
    def _create_attention_controls(self):
        """创建注意力控制"""
        gr.Markdown("### Agent Attention Parameters")
        gr.Markdown("Control the adaptive attention mechanism for water-oil region focus")
        
        with gr.Row():
            self.w_attn_min = gr.Slider(
                minimum=0.1, maximum=1.0, step=0.05, value=0.5,
                label="Attention Weight Min",
                info="Early stage attention strength"
            )
            self.w_attn_max = gr.Slider(
                minimum=0.1, maximum=1.0, step=0.05, value=0.95,
                label="Attention Weight Max",
                info="Late stage attention strength"
            )
        
        with gr.Row():
            self.num_agents = gr.Slider(
                minimum=16, maximum=100, step=1, value=49,
                label="Number of Agents",
                info="Agent tokens count (49=7x7 grid)"
            )
            self.agent_dim = gr.Slider(
                minimum=32, maximum=128, step=16, value=64,
                label="Agent Dimension",
                info="Agent token feature dimension"
            )
        
        # 注意力可视化
        self.attention_preview = gr.Plot(
            label="Attention Weight Curve",
            value=self._generate_attention_plot()
        )
    
    def _create_advanced_controls(self):
        """创建高级控制"""
        gr.Markdown("### Advanced Settings")
        
        with gr.Row():
            self.target_channels = gr.Slider(
                minimum=64, maximum=1024, step=64, value=320,
                label="Target Channels",
                info="EvoTune processing channel count"
            )
            self.total_timesteps = gr.Slider(
                minimum=100, maximum=2000, step=100, value=1000,
                label="Total Timesteps",
                info="Diffusion process timesteps"
            )
        
        # 调制点配置
        gr.Markdown("#### Modulation Points")
        self.modulation_config = gr.JSON(
            label="Modulation Points Configuration",
            value=self._get_default_modulation_config()
        )
    
    def _create_monitoring_panel(self):
        """创建监控面板"""
        with gr.Group():
            gr.Markdown("## Real-time Monitoring")
            
            # 状态显示
            self.status_display = gr.Textbox(
                label="EvoTune Status",
                value="Disabled",
                interactive=False
            )
            
            # 性能统计
            self.performance_stats = gr.JSON(
                label="Performance Statistics",
                value={}
            )
            
            # 刷新按钮
            refresh_btn = gr.Button("Refresh Stats", variant="secondary")
            refresh_btn.click(
                fn=self._refresh_stats,
                outputs=[self.status_display, self.performance_stats]
            )
            
            # 自动刷新
            auto_refresh = gr.Checkbox(
                label="Auto Refresh (5s)",
                value=False
            )
            
            # 实时图表
            self.realtime_chart = gr.Plot(
                label="Real-time Performance",
                value=None
            )
    
    def _create_preset_panel(self):
        """创建预设面板"""
        with gr.Group():
            gr.Markdown("## Presets & Configuration")
            
            with gr.Row():
                # 预设选择
                preset_dropdown = gr.Dropdown(
                    choices=list(self.presets.keys()),
                    label="Load Preset",
                    info="Load predefined parameter configurations"
                )
                
                load_preset_btn = gr.Button("Load", variant="primary")
                
                # 导入导出
                export_btn = gr.Button("Export Config", variant="secondary")
                import_btn = gr.Button("Import Config", variant="secondary")
            
            # 配置文件
            config_file = gr.File(
                label="Configuration File",
                file_types=[".json"]
            )
            
            # 绑定事件
            load_preset_btn.click(
                fn=self._load_preset,
                inputs=preset_dropdown,
                outputs=self._get_all_controls()
            )
            
            export_btn.click(
                fn=self._export_config,
                inputs=self._get_all_controls(),
                outputs=config_file
            )
    
    def _generate_weight_plot(self, plot_type='backbone'):
        """生成权重曲线图"""
        try:
            import matplotlib.pyplot as plt
            import numpy as np
            
            # 生成时间步序列
            timesteps = np.linspace(0, 1, 100)
            
            if plot_type == 'backbone':
                # 计算权重曲线
                phi_late = np.sin(np.pi * timesteps / 2)
                b1_curve = self.default_params['b1_range'][0] + \
                          (self.default_params['b1_range'][1] - self.default_params['b1_range'][0]) * phi_late
                b2_curve = self.default_params['b2_range'][0] + \
                          (self.default_params['b2_range'][1] - self.default_params['b2_range'][0]) * phi_late
                
                plt.figure(figsize=(8, 6))
                plt.plot(timesteps, b1_curve, label='b1(t) - Backbone', linewidth=2)
                plt.plot(timesteps, b2_curve, label='b2(t) - Skip Connection', linewidth=2)
                plt.xlabel('Normalized Timestep')
                plt.ylabel('Weight Value')
                plt.title('Backbone Feature Weight Curves')
                plt.legend()
                plt.grid(True, alpha=0.3)
                
                return plt
            
        except Exception as e:
            logger.error(f"Failed to generate plot: {e}")
            return None
    
    def _generate_frequency_plot(self):
        """生成频域响应图"""
        # 实现频域响应可视化
        pass
    
    def _generate_attention_plot(self):
        """生成注意力权重图"""
        # 实现注意力权重可视化
        pass
    
    def _get_default_modulation_config(self):
        """获取默认调制点配置"""
        return {
            "mid_block": {"type": "backbone", "enabled": True, "priority": 1},
            "up_blocks.0.resnets.0": {"type": "skip", "enabled": True, "priority": 2},
            "up_blocks.1.resnets.0": {"type": "skip", "enabled": True, "priority": 3},
            "up_blocks.2.resnets.0": {"type": "skip", "enabled": True, "priority": 4}
        }
    
    def _get_all_controls(self):
        """获取所有控制组件"""
        return [
            self.enabled_checkbox, self.b1_min, self.b1_max, self.b2_min, self.b2_max,
            self.s1_min, self.s1_max, self.s2_min, self.s2_max,
            self.w_attn_min, self.w_attn_max, self.num_agents, self.agent_dim,
            self.target_channels, self.total_timesteps
        ]
    
    def _load_preset(self, preset_name):
        """加载预设配置"""
        if preset_name in self.presets:
            preset = self.presets[preset_name]
            return [
                True,  # enabled
                preset['b1_range'][0], preset['b1_range'][1],
                preset['b2_range'][0], preset['b2_range'][1],
                preset['s1_range'][0], preset['s1_range'][1],
                preset['s2_range'][0], preset['s2_range'][1],
                preset['w_attn_range'][0], preset['w_attn_range'][1],
                49, 64, 320, 1000  # 默认高级参数
            ]
        return [False] + [0] * 14
    
    def _export_config(self, *args):
        """导出配置"""
        config = {
            'enabled': args[0],
            'b1_range': (args[1], args[2]),
            'b2_range': (args[3], args[4]),
            's1_range': (args[5], args[6]),
            's2_range': (args[7], args[8]),
            'w_attn_range': (args[9], args[10]),
            'num_agents': args[11],
            'agent_dim': args[12],
            'target_channels': args[13],
            'total_timesteps': args[14],
            'timestamp': time.time()
        }
        
        filename = f"evotune_config_{int(time.time())}.json"
        with open(filename, 'w') as f:
            json.dump(config, f, indent=2)
        
        return filename
    
    def _refresh_stats(self):
        """刷新统计信息"""
        if self.evotune_controller:
            try:
                stats = self.evotune_controller.get_performance_stats()
                status = "Enabled" if self.evotune_controller.enabled else "Disabled"
                return status, stats
            except Exception as e:
                return f"Error: {e}", {}
        
        return "Not Connected", {}


def create_evotune_interface(evotune_controller=None):
    """
    创建EvoTune界面
    
    Args:
        evotune_controller: EvoTune控制器实例
        
    Returns:
        interface: Gradio界面
    """
    ui = EvoTuneUI(evotune_controller)
    return ui.create_main_interface()
