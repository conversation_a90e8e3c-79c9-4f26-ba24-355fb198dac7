#### 3.2.4 时间步感知的自适应频域处理机制

针对积水环境下水纹理细节的生成需求，EvoTune设计了时间步感知的自适应频域处理机制。该机制直接作用于UNet的中间特征表示，通过分层滤波器设计对水纹理的频域成分进行精细控制。

**自适应频域滤波器设计**：
$$\tau(t) = 7 + \lfloor 5(1 - \tilde{t}) \rfloor \tag{8a}$$
$$\boldsymbol{M}_{u,v}(t) = \begin{cases}
s_1(t), & \text{if } \max(|u - u_c|, |v - v_c|) \leq \tau(t) \\
s_2(t), & \text{if } \tau(t) < \max(|u - u_c|, |v - v_c|) \leq 2\tau(t) \\
1, & \text{otherwise}
\end{cases} \tag{8b}$$

其中$\tau(t)$为时间步感知的动态阈值，$(u_c, v_c)$为频域中心坐标。

**这一步的作用**：设计时间步感知的分层滤波器，使用来自公式(3)的$s_1(t)$控制低频成分（积水区域的整体结构），$s_2(t)$控制中频成分（水纹理的细节特征），高频成分保持不变（保持图像的锐度），实现对积水区域不同频率成分的精细调控。

#### 3.2.5 EvoTune在UNet中的统一特征调制机制

EvoTune作为UNet的内部控制器，通过统一的特征调制机制在关键连接点进行干预。该机制将Agent Attention增强、频域处理和时间步权重调制整合为一个完整的处理流程。

**统一特征调制的完整流程**：

对于UNet中的任意中间特征$\boldsymbol{H}_{\mathrm{input}} \in \mathbb{R}^{B \times C \times H \times W}$（可以是骨干特征或跳跃连接特征），EvoTune执行以下统一处理：

**步骤1：Agent Attention增强**
$$\boldsymbol{H}_{\mathrm{attended}} = \text{AgentAttention}(\boldsymbol{H}_{\mathrm{input}}, w_{\mathrm{attn}}(t)) \tag{9a}$$

**步骤2：频域处理**
$$\boldsymbol{H}_{\mathrm{freq}} = \mathrm{FFT2D}(\boldsymbol{H}_{\mathrm{attended}}) \tag{9b}$$
$$\boldsymbol{H}_{\mathrm{filtered}} = \boldsymbol{H}_{\mathrm{freq}} \odot \boldsymbol{M}(t) \tag{9c}$$
$$\boldsymbol{H}_{\mathrm{fourier}} = \mathrm{IFFT2D}(\boldsymbol{H}_{\mathrm{filtered}}) \tag{9d}$$

**步骤3：自适应门控融合**
$$\boldsymbol{G} = \sigma(\text{Conv}([\boldsymbol{H}_{\mathrm{input}}, \boldsymbol{H}_{\mathrm{fourier}}])) \tag{9e}$$
$$\boldsymbol{F} = \text{Conv}([\boldsymbol{H}_{\mathrm{input}}, \boldsymbol{H}_{\mathrm{fourier}}]) \tag{9f}$$
$$\boldsymbol{H}_{\mathrm{enhanced}} = \boldsymbol{H}_{\mathrm{input}} \odot (1 - \boldsymbol{G} \odot b(t)) + \boldsymbol{F} \odot (\boldsymbol{G} \odot b(t)) \tag{9g}$$

**这一步的作用**：
- **公式(9a)**：使用Agent Attention对输入特征进行水油区域感知的增强
- **公式(9b-9d)**：在频域对增强特征进行时间步感知的滤波处理，优化水纹理的频域特性
- **公式(9e-9f)**：生成自适应门控信号和融合特征，实现智能的特征选择
- **公式(9g)**：通过门控机制将原始特征和处理后特征进行时间步感知的自适应融合

**在UNet不同位置的具体应用**：

**骨干特征调制**（编码器-解码器连接点）：
$$\boldsymbol{H}_{\mathrm{backbone}}^{\mathrm{enhanced}} = \text{EvoTuneModulation}(\boldsymbol{H}_{\mathrm{backbone}}, b_1(t), \boldsymbol{s}(t), w_{\mathrm{attn}}(t)) \tag{10}$$

**跳跃连接特征调制**（跳跃连接处）：
$$\boldsymbol{H}_{\mathrm{skip}}^{\mathrm{enhanced}} = \text{EvoTuneModulation}(\boldsymbol{H}_{\mathrm{skip}}, b_2(t), \boldsymbol{s}(t), w_{\mathrm{attn}}(t)) \tag{11}$$

其中$\text{EvoTuneModulation}(\cdot)$表示上述公式(9a-9g)定义的完整调制过程。

**这一步的作用**：
- **早期阶段**：$b_1(t)$增强，骨干特征权重提升，在编码器-解码器连接点增强特征传递，专注于建立积水区域的基础结构
- **后期阶段**：$b_2(t)$增强，跳跃连接权重提升，在跳跃连接处优化特征融合，专注于积水区域的细节特征完善

**与传统FreeU方法的根本区别**：
1. **动态vs静态**：EvoTune使用时间步感知的动态参数，而FreeU使用固定参数
2. **智能融合vs简单缩放**：EvoTune采用门控融合机制，而FreeU仅进行简单的特征缩放
3. **场景专用vs通用**：EvoTune专门针对水油场景优化，而FreeU是通用方法
4. **多模块协同vs单一处理**：EvoTune整合了注意力、频域和权重调制，而FreeU仅处理频域

#### 3.2.6 EvoTune的协同优化机制

EvoTune的核心优势在于其作为UNet中间控制器的统一调谐框架，实现了多个处理模块在UNet内部的深度协同优化：

**时间步统一调度**：所有模块（Agent Attention、频域处理、特征调制）都由统一的时间步调谐器控制，确保在扩散过程的不同阶段采用协调一致的处理策略。

**特征空间一致性**：所有处理都在UNet的中间特征空间进行，避免了多次特征空间转换带来的信息损失。

**端到端优化**：EvoTune与UNet深度集成，可以通过端到端训练实现整体优化，而不是作为独立的后处理模块。

这种设计使得EvoTune能够在保持扩散模型原有生成能力的基础上，专门针对积水干扰场景进行精确优化，实现了通用性与专用性的有机统一。
