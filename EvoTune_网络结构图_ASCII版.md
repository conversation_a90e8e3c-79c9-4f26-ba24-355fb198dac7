# EvoTune网络结构图 - ASCII版

## 1. 完整网络结构图

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                              EvoTune 网络架构                                │
└─────────────────────────────────────────────────────────────────────────────┘

输入层
┌─────────────────┐
│  噪声图像 x_t   │
│   时间步 t      │
└─────────┬───────┘
          │
          ▼
┌─────────────────────────────────────────────────────────────────────────────┐
│                    核心创新1: 动态时间步调谐                                  │
├─────────────────────────────────────────────────────────────────────────────┤
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────────────────────────┐  │
│  │时间步标准化  │ -> │  相位计算   │ -> │      参数调谐                    │  │
│  │t̃ = t/1000   │    │φ_early     │    │ b1(t), b2(t), s1(t), s2(t)     │  │
│  │             │    │φ_late      │    │ w_attn(t)                      │  │
│  └─────────────┘    └─────────────┘    └─────────────┬───────────────────┘  │
└─────────────────────────────────────────────────────┼─────────────────────┘
                                                        │
          ┌─────────────────────────────────────────────┼─────────────────────┐
          │                                             ▼                     │
          │            核心创新2: 水油专用Agent Attention                      │
          │  ┌─────────────┐    ┌─────────────┐    ┌─────────────────────┐    │
          │  │ Agent生成   │ -> │  双向注意力  │ -> │    时间步调制        │    │
          │  │ 49个tokens  │    │Agent<->空间 │    │   w_attn(t) 权重    │    │
          │  └─────────────┘    └─────────────┘    └─────────┬───────────┘    │
          └─────────────────────────────────────────────────┼─────────────────┘
                                                            │
                    ┌───────────────────────────────────────┼───────────────────┐
                    │                                       ▼                   │
                    │          核心创新3: 自适应频域处理                         │
                    │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────┐   │
                    │  │  FFT变换    │->│ 自适应滤波   │->│   IFFT逆变换    │   │
                    │  │             │  │基于s1,s2参数│  │                 │   │
                    │  └─────────────┘  └─────────────┘  └─────────┬───────┘   │
                    └─────────────────────────────────────────────┼───────────┘
                                                                  │
                                                                  ▼
                                                        ┌─────────────────┐
                                                        │   输出层        │
                                                        │  增强特征       │
                                                        │ 送入原始UNet    │
                                                        └─────────────────┘
```

## 2. 简化版网络结构图

```
输入 (x_t, t)
      │
      ▼
┌─────────────────┐
│  时间步调谐      │  ──→  参数: b1(t), b2(t), s1(t), s2(t), w_attn(t)
└─────────────────┘                │                    │
      │                            │                    │
      ▼                            ▼                    │
┌─────────────────┐         ┌─────────────────┐         │
│ Agent Attention │  ←──────│   时间步权重     │         │
└─────────────────┘         └─────────────────┘         │
      │                                                 │
      ▼                                                 │
┌─────────────────┐         ┌─────────────────┐         │
│   频域处理       │  ←──────│   滤波参数       │ ←───────┘
└─────────────────┘         └─────────────────┘
      │
      ▼
   输出特征
```

## 3. 数据流向图

```
时间步 t ──┐
           │
           ▼
       标准化 (t̃)
           │
           ▼
       相位计算 (φ_early, φ_late)
           │
           ▼
       参数生成 ──┬── b1(t), b2(t) ──→ (传递给UNet)
                 │
                 ├── s1(t), s2(t) ──→ 频域滤波
                 │
                 └── w_attn(t) ────→ Agent Attention

输入特征 x_t ──┐
               │
               ▼
           Agent生成 (49 tokens)
               │
               ▼
           双向注意力 ←── w_attn(t)
               │
               ▼
           特征增强
               │
               ▼
           FFT变换
               │
               ▼
           自适应滤波 ←── s1(t), s2(t)
               │
               ▼
           IFFT逆变换
               │
               ▼
           增强特征输出
```

## 4. 核心创新模块详细图

### 创新1: 动态时间步调谐
```
t ──→ [t̃ = t/1000] ──→ [φ_early = σ(10(t̃-0.8))] ──→ [b1(t) = 1.5 + 0.3×φ_early×0.5]
                   └──→ [φ_late = σ(10(0.2-t̃))] ──┬→ [s1(t) = 0.8 + 0.3×φ_late×0.4]
                                                   ├→ [s2(t) = 0.5 + 0.3×φ_late×0.3]
                                                   └→ [w_attn(t) = 0.5 + 0.5σ(8(0.3-t̃))]
```

### 创新2: 水油专用Agent Attention
```
输入特征 X ──→ [AdaptivePool] ──→ Agent tokens (49×C)
              │
              ▼
          [双向注意力计算] ←── w_attn(t)
              │
              ▼
          增强特征输出
```

### 创新3: 自适应频域处理
```
输入 ──→ [FFT] ──→ [滤波器设计] ──→ [频域滤波] ──→ [IFFT] ──→ 输出
                        ▲
                   s1(t), s2(t)
```

## 5. 参数演化时间线

```
时间步:  1000 ────────── 500 ────────── 0
         早期           中期           后期

b1(t):   1.610 ────────── 1.507 ────────── 1.500
         ████████       ████████       ████████
         (高)           (中)           (低)

s1(t):   0.800 ────────── 0.806 ────────── 0.888  
         ████████       ████████       ████████████
         (低)           (中)           (高)

w_attn:  0.504 ────────── 0.584 ────────── 0.916
         ████████       ████████████   ████████████████
         (低)           (中)           (高)

作用:    建立结构       平衡过渡       优化细节
```

## 6. 与传统方法对比

```
传统FreeU:
输入 ──→ [固定参数处理] ──→ 输出
         b1=1.5, s1=0.8
         (参数不变)

EvoTune:
输入 ──→ [动态调谐] ──→ [Agent注意力] ──→ [自适应频域] ──→ 输出
         参数随时间变化    水油专用机制      时间步感知滤波
```

## 7. 论文图表制作建议

### 主结构图
- 使用第1节的完整结构图作为主图
- 用不同颜色标注三大创新模块
- 添加数据维度标注

### 参数演化图
- 使用第5节的时间线图
- 制作成曲线图显示连续变化
- 标注关键时间点

### 模块细节图
- 使用第4节的详细流程图
- 每个创新模块单独成图
- 突出数学公式和计算流程

这些ASCII图可以作为制作正式论文图表的参考模板。
