<!DOCTYPE html>
<html lang="en-US">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <meta name="generator" content="VuePress 2.0.0-beta.49">
    <style>
      :root {
        --c-bg: #fff;
      }
      html.dark {
        --c-bg: #22272e;
      }
      html, body {
        background-color: var(--c-bg);
      }
    </style>
    <script>
      const userMode = localStorage.getItem('vuepress-color-scheme');
			const systemDarkMode = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;
			if (userMode === 'dark' || (userMode !== 'light' && systemDarkMode)) {
				document.documentElement.classList.toggle('dark', true);
			}
    </script>
    <title>LoRA 训练 | SD 训练 UI</title><meta name="description" content="">
    <link rel="modulepreload" href="/assets/app.9273d30a.js"><link rel="modulepreload" href="/assets/index.html.db1c0354.js"><link rel="modulepreload" href="/assets/index.html.b97ec799.js"><link rel="prefetch" href="/assets/index.html.9d7cc666.js"><link rel="prefetch" href="/assets/tageditor.html.66da263e.js"><link rel="prefetch" href="/assets/tagger.html.2eb31fcb.js"><link rel="prefetch" href="/assets/task.html.4e4c8633.js"><link rel="prefetch" href="/assets/tensorboard.html.4a2799a9.js"><link rel="prefetch" href="/assets/index.html.18cf2953.js"><link rel="prefetch" href="/assets/basic.html.48955584.js"><link rel="prefetch" href="/assets/master.html.54eb6415.js"><link rel="prefetch" href="/assets/params.html.c8cc13ef.js"><link rel="prefetch" href="/assets/tools.html.c0a4659a.js"><link rel="prefetch" href="/assets/about.html.5b0c0de9.js"><link rel="prefetch" href="/assets/settings.html.06993f96.js"><link rel="prefetch" href="/assets/404.html.686caba0.js"><link rel="prefetch" href="/assets/index.html.84bf285d.js"><link rel="prefetch" href="/assets/tageditor.html.66fa7b72.js"><link rel="prefetch" href="/assets/tagger.html.f698ca26.js"><link rel="prefetch" href="/assets/task.html.2f4311fb.js"><link rel="prefetch" href="/assets/tensorboard.html.e5ada3f5.js"><link rel="prefetch" href="/assets/index.html.4696b6e4.js"><link rel="prefetch" href="/assets/basic.html.655a3322.js"><link rel="prefetch" href="/assets/master.html.94401419.js"><link rel="prefetch" href="/assets/params.html.c90a6b4c.js"><link rel="prefetch" href="/assets/tools.html.1d9df334.js"><link rel="prefetch" href="/assets/about.html.2343ff24.js"><link rel="prefetch" href="/assets/settings.html.0626d062.js"><link rel="prefetch" href="/assets/404.html.cbf82dee.js"><link rel="prefetch" href="/assets/404.310165f5.js"><link rel="prefetch" href="/assets/layout.c140630d.js">
    <link rel="stylesheet" href="/assets/style.04eab9dc.css">
  </head>
  <body>
    <div id="app"><!--[--><div class="theme-container no-navbar"><!--[--><!----><!--]--><div class="sidebar-mask"></div><!--[--><aside class="sidebar" data-v-db8971c4><div class="el-scrollbar" data-v-db8971c4><div class="el-scrollbar__wrap el-scrollbar__wrap--hidden-default" style=""><div class="el-scrollbar__view" style=""><!--[--><div class="sidebar-container" data-v-db8971c4><!----><ul class="sidebar-items" data-v-db8971c4><!--[--><li><a href="/" class="sidebar-item sidebar-heading" aria-label="SD-Trainer"><!--[--><!--]--> SD-Trainer <!--[--><!--]--></a><!----></li><li><a href="/lora/index.md" class="sidebar-item sidebar-heading active" aria-label="LoRA训练"><!--[--><!--]--> LoRA训练 <!--[--><!--]--></a><ul style="" class="sidebar-item-children"><!--[--><li><a href="/lora/basic.md" class="sidebar-item" aria-label="新手"><!--[--><!--]--> 新手 <!--[--><!--]--></a><!----></li><li><a href="/lora/master.md" class="sidebar-item" aria-label="专家"><!--[--><!--]--> 专家 <!--[--><!--]--></a><!----></li><li><a href="/lora/tools.md" class="sidebar-item" aria-label="工具"><!--[--><!--]--> 工具 <!--[--><!--]--></a><!----></li><li><a href="/lora/params.md" class="sidebar-item" aria-label="参数详解"><!--[--><!--]--> 参数详解 <!--[--><!--]--></a><!----></li><!--]--></ul></li><li><a href="/dreambooth/index.md" class="sidebar-item sidebar-heading" aria-label="Dreambooth 训练"><!--[--><!--]--> Dreambooth 训练 <!--[--><!--]--></a><!----></li><li><a href="/tensorboard.md" class="sidebar-item sidebar-heading" aria-label="Tensorboard"><!--[--><!--]--> Tensorboard <!--[--><!--]--></a><!----></li><li><a href="/tagger.md" class="sidebar-item sidebar-heading" aria-label="WD 1.4 标签器"><!--[--><!--]--> WD 1.4 标签器 <!--[--><!--]--></a><!----></li><li><a href="/tageditor.md" class="sidebar-item sidebar-heading" aria-label="标签编辑器"><!--[--><!--]--> 标签编辑器 <!--[--><!--]--></a><!----></li><li><p tabindex="0" class="sidebar-item sidebar-heading">其他 <!----></p><ul style="display:none;" class="sidebar-item-children"><!--[--><li><a href="/other/settings.md" class="sidebar-item" aria-label="UI 设置"><!--[--><!--]--> UI 设置 <!--[--><!--]--></a><!----></li><li><a href="/other/about.md" class="sidebar-item" aria-label="关于"><!--[--><!--]--> 关于 <!--[--><!--]--></a><!----></li><!--]--></ul></li><!--]--></ul><ul class="sidebar-bottom" data-v-db8971c4><li class="sidebar-item sidebar-heading appearance" data-v-db8971c4> Github <a class="icon" href="https://github.com/Akegarasu/lora-scripts" target="_blank" aria-label="GitHub" data-v-db8971c4><svg xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="xMidYMid meet" viewBox="0 0 24 24" data-v-db8971c4><path d="M12 2C6.475 2 2 6.475 2 12a9.994 9.994 0 0 0 6.838 9.488c.5.087.687-.213.687-.476c0-.237-.013-1.024-.013-1.862c-2.512.463-3.162-.612-3.362-1.175c-.113-.288-.6-1.175-1.025-1.413c-.35-.187-.85-.65-.013-.662c.788-.013 1.35.725 1.538 1.025c.9 1.512 2.338 1.087 2.912.825c.088-.65.35-1.087.638-1.337c-2.225-.25-4.55-1.113-4.55-4.938c0-1.088.387-1.987 1.025-2.688c-.1-.25-.45-1.275.1-2.65c0 0 .837-.262 2.75 1.026a9.28 9.28 0 0 1 2.5-.338c.85 0 1.7.112 2.5.337c1.912-1.3 2.75-1.024 2.75-1.024c.55 1.375.2 2.4.1 2.65c.637.7 1.025 1.587 1.025 2.687c0 3.838-2.337 4.688-4.562 4.938c.362.312.675.912.675 1.85c0 1.337-.013 2.412-.013 2.75c0 .262.188.574.688.474A10.016 10.016 0 0 0 22 12c0-5.525-4.475-10-10-10z" fill="currentColor" data-v-db8971c4></path></svg></a></li><li class="sidebar-item sidebar-heading appearance" data-v-db8971c4> Language <button class="toggle-color-mode-button" data-v-db8971c4><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" data-v-db8971c4><path d=" M12.87 15.07l-2.54-2.51.03-.03c1.74-1.94 2.98-4.17 3.71-6.53H17V4h-7V2H8v2H1v1.99h11.17C11.5 7.92 10.44 9.75 9 11.35 8.07 10.32 7.3 9.19 6.69 8h-2c.73 1.63 1.73 3.17 2.98 4.56l-5.09 5.02L4 19l5-5 3.11 3.11.76-2.04zM18.5 10h-2L12 22h2l1.12-3h4.75L21 22h2l-4.5-12zm-2.62 7l1.62-4.33L19.12 17h-3.24z " fill="currentColor" data-v-db8971c4></path></svg></button></li><li class="sidebar-item sidebar-heading appearance" data-v-db8971c4> 灯泡 <button class="toggle-color-mode-button" title="toggle color mode" data-v-db8971c4><svg style="" class="icon" focusable="false" viewBox="0 0 32 32"><path d="M16 12.005a4 4 0 1 1-4 4a4.005 4.005 0 0 1 4-4m0-2a6 6 0 1 0 6 6a6 6 0 0 0-6-6z" fill="currentColor"></path><path d="M5.394 6.813l1.414-1.415l3.506 3.506L8.9 10.318z" fill="currentColor"></path><path d="M2 15.005h5v2H2z" fill="currentColor"></path><path d="M5.394 25.197L8.9 21.691l1.414 1.415l-3.506 3.505z" fill="currentColor"></path><path d="M15 25.005h2v5h-2z" fill="currentColor"></path><path d="M21.687 23.106l1.414-1.415l3.506 3.506l-1.414 1.414z" fill="currentColor"></path><path d="M25 15.005h5v2h-5z" fill="currentColor"></path><path d="M21.687 8.904l3.506-3.506l1.414 1.415l-3.506 3.505z" fill="currentColor"></path><path d="M15 2.005h2v5h-2z" fill="currentColor"></path></svg><svg style="display:none;" class="icon" focusable="false" viewBox="0 0 32 32"><path d="M13.502 5.414a15.075 15.075 0 0 0 11.594 18.194a11.113 11.113 0 0 1-7.975 3.39c-.138 0-.278.005-.418 0a11.094 11.094 0 0 1-3.2-21.584M14.98 3a1.002 1.002 0 0 0-.175.016a13.096 13.096 0 0 0 1.825 25.981c.164.006.328 0 .49 0a13.072 13.072 0 0 0 10.703-5.555a1.01 1.01 0 0 0-.783-1.565A13.08 13.08 0 0 1 15.89 4.38A1.015 1.015 0 0 0 14.98 3z" fill="currentColor"></path></svg></button></li></ul></div><!--]--></div></div><!--[--><div class="el-scrollbar__bar is-horizontal" style="display:none;"><div class="el-scrollbar__thumb" style="width:0;transform:translateX(0%);"></div></div><div class="el-scrollbar__bar is-vertical" style="display:none;"><div class="el-scrollbar__thumb" style="height:0;transform:translateY(0%);"></div></div><!--]--></div></aside><!--]--><!--[--><main class="page"><!--[--><!--]--><div class="theme-default-content"><!--[--><!--]--><div><h1 id="lora-训练" tabindex="-1"><a class="header-anchor" href="#lora-训练" aria-hidden="true">#</a> LoRA 训练</h1><p>本 LoRA 训练界面分为两种模式。</p><ul><li>针对新手的简易训练只有部分可调节参数</li><li>针对有一定经验的用户的专家模式，开放全部的高级参数</li></ul><div class="custom-container tip"><p class="custom-container-title">TIP</p><p>如果你是新手，建议使用新手模式，不要逞强使用专家模式，否则可能会出现意想不到的问题。</p></div></div><!--[--><!--]--></div><footer class="page-meta"><!----><!----><!----></footer><!----><!--[--><!--]--></main><!--]--></div><!----><!--]--></div>
    <script type="module" src="/assets/app.9273d30a.js" defer></script>
  </body>
</html>
