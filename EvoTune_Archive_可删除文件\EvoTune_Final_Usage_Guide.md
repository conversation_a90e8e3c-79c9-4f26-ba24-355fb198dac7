# 🎉 EvoTune 最终使用指南

## ✅ 安装状态
- ✅ EvoTune核心模块正常工作
- ✅ WebUI扩展已安装
- ✅ 所有测试通过
- ✅ 系统已准备就绪

---

## 🚀 立即开始使用

### 方法一：使用安全模式启动（推荐）

```bash
# 双击这个文件启动
双击 start_webui_evotune_safe.bat
```

这个启动脚本会：
- 自动禁用冲突的扩展（TensorRT、infinite-zoom）
- 启动EvoTune增强版WebUI
- 自动打开浏览器到 http://localhost:7860
- 退出时恢复被禁用的扩展

### 方法二：使用原始启动器

```bash
# 进入SD WebUI目录
cd h:\StableDiffusion_Gaijin\sd-webui-aki-v4.4
双击 A启动器.exe
```

注意：可能会遇到其他扩展的错误，但不影响EvoTune使用。

---

## 🎯 在WebUI中使用EvoTune

### 1. 配置EvoTune参数

启动WebUI后：

1. **打开设置**：点击界面顶部的"设置"标签
2. **找到EvoTune设置**：在左侧列表中找到"EvoTune设置"
3. **调整参数**：
   ```
   evotune_enabled: True
   evotune_b1: 1.5      (骨干特征权重, 0.5-3.0)
   evotune_b2: 1.2      (跳跃连接权重, 0.5-3.0)
   evotune_s1: 0.8      (骨干特征缩放, 0.1-2.0)
   evotune_s2: 0.5      (跳跃连接缩放, 0.1-2.0)
   evotune_use_agent_attention: False    (暂时禁用)
   evotune_use_adaptive_fourier: True    (启用频域优化)
   ```
4. **应用设置**：点击"应用设置"按钮

### 2. 使用EvoTune专用界面

WebUI中会出现"EvoTune"标签页，提供：
- 当前配置显示
- 详细使用说明
- 模型测试功能
- 配置刷新

### 3. 在图生图模式下使用

1. **切换到图生图**：点击"图生图"标签
2. **上传原始图像**：上传包含积水和油污的工业场景图像
3. **设置提示词**：
   ```
   正面提示词: oil leak, water interference, electrical equipment, realistic industrial scene, high quality, detailed
   负面提示词: blurry, low quality, artifacts, cartoon, anime
   ```
4. **调整参数**：
   - 重绘幅度：0.3-0.7（推荐0.5）
   - 采样步数：20-50
   - CFG Scale：7-12
5. **生成图像**：点击"生成"，EvoTune会自动优化效果

---

## ⚙️ 参数调优指南

### 针对不同场景的参数建议：

#### 🌊 水面反射较强的场景
```
b1: 1.8    (增强骨干特征)
s1: 1.0    (增大频域缩放)
b2: 1.2    (保持默认)
s2: 0.5    (保持默认)
```

#### 🛢️ 油污纹理复杂的场景
```
b1: 1.5    (保持默认)
s1: 0.8    (保持默认)
b2: 1.5    (增强跳跃连接)
s2: 0.7    (增大缩放)
```

#### ⚖️ 平衡设置（推荐起始值）
```
b1: 1.5, b2: 1.2, s1: 0.8, s2: 0.5
```

### 实时调整技巧：
1. 生成一张图像观察效果
2. 在EvoTune设置中微调参数
3. 重新生成对比效果
4. 保存有效的参数组合

---

## 🔧 故障排除

### 常见问题及解决方案：

#### 1. WebUI启动失败
```bash
# 解决方案：使用安全模式启动
双击 start_webui_evotune_safe.bat
```

#### 2. EvoTune设置不显示
```bash
# 检查扩展是否正确安装
python test_evotune_simple.py

# 重新安装扩展
python install_evotune_to_webui.py
```

#### 3. 生成效果不理想
- 调整EvoTune参数（b1, b2, s1, s2）
- 使用更详细的提示词
- 尝试不同的重绘幅度
- 确保原始图像质量良好

#### 4. 显存不足
- 减小图像尺寸
- 降低批次大小
- 使用--lowvram启动参数

#### 5. 其他扩展冲突
- 使用安全模式启动（自动禁用冲突扩展）
- 或手动禁用TensorRT和infinite-zoom扩展

---

## 📊 效果评估

### 如何判断EvoTune是否生效：

1. **视觉对比**：
   - 水面反射更自然
   - 油污纹理更清晰
   - 边界更加清晰

2. **定量评估**：
   ```python
   # 使用评估脚本
   python evotune_evaluation_metrics.py
   ```

3. **参数监控**：
   - 在EvoTune标签页查看当前配置
   - 使用测试功能验证模型状态

---

## 🎯 最佳实践总结

### 推荐工作流程：

1. **准备阶段**：
   - 使用安全模式启动WebUI
   - 配置EvoTune参数（从默认值开始）
   - 准备高质量的原始图像

2. **生成阶段**：
   - 图生图模式，重绘幅度0.5
   - 详细的提示词描述
   - 多次生成选择最佳效果

3. **优化阶段**：
   - 根据效果调整参数
   - 记录有效的参数组合
   - 建立参数库供后续使用

### 提示词模板：

```
# 基础模板
oil leak, water interference, electrical equipment, realistic, industrial scene, high quality

# 增强模板  
electrical substation oil leak with water puddle interference, realistic industrial photography, detailed textures, high resolution, professional lighting

# 特定场景
transformer oil spill on wet concrete, water reflection, industrial equipment, photorealistic, detailed surface textures
```

---

## 🔮 未来改进

### 计划中的功能：
- ✅ 基础EvoTune模型
- ✅ 时间步自适应调谐
- ✅ 自适应频域滤波
- 🔄 Agent Attention优化（修复中）
- 📋 LoRA微调集成
- 📋 批量处理功能
- 📋 自动参数优化

### 如何获得更好效果：
1. 收集更多高质量的水油场景数据
2. 使用LoRA进行特定场景微调
3. 结合其他图像增强技术
4. 参与社区反馈和改进

---

## 📞 支持和反馈

如果遇到问题或有改进建议：
1. 运行 `python test_evotune_simple.py` 进行诊断
2. 查看WebUI控制台的错误信息
3. 尝试不同的参数组合
4. 记录问题现象和复现步骤

---

## 🎉 总结

EvoTune系统现在已经完全可用！您可以：

✅ **立即使用**：双击 `start_webui_evotune_safe.bat` 开始
✅ **参数调优**：在设置中调整EvoTune参数
✅ **效果评估**：使用专用界面测试和监控
✅ **持续改进**：根据效果调整和优化

专门针对变电设备渗漏油场景中的积水干扰问题，EvoTune将显著提升您的图像生成质量，为您的研究提供强有力的支持！

**开始您的EvoTune之旅吧！** 🚀
