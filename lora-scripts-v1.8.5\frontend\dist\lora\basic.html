<!DOCTYPE html>
<html lang="en-US">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <meta name="generator" content="VuePress 2.0.0-beta.49">
    <style>
      :root {
        --c-bg: #fff;
      }
      html.dark {
        --c-bg: #22272e;
      }
      html, body {
        background-color: var(--c-bg);
      }
    </style>
    <script>
      const userMode = localStorage.getItem('vuepress-color-scheme');
			const systemDarkMode = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;
			if (userMode === 'dark' || (userMode !== 'light' && systemDarkMode)) {
				document.documentElement.classList.toggle('dark', true);
			}
    </script>
    <title>LoRA 训练 新手模式 | SD 训练 UI</title><meta name="description" content="">
    <link rel="modulepreload" href="/assets/app.9273d30a.js"><link rel="modulepreload" href="/assets/basic.html.655a3322.js"><link rel="modulepreload" href="/assets/basic.html.48955584.js"><link rel="prefetch" href="/assets/index.html.9d7cc666.js"><link rel="prefetch" href="/assets/tageditor.html.66da263e.js"><link rel="prefetch" href="/assets/tagger.html.2eb31fcb.js"><link rel="prefetch" href="/assets/task.html.4e4c8633.js"><link rel="prefetch" href="/assets/tensorboard.html.4a2799a9.js"><link rel="prefetch" href="/assets/index.html.18cf2953.js"><link rel="prefetch" href="/assets/index.html.b97ec799.js"><link rel="prefetch" href="/assets/master.html.54eb6415.js"><link rel="prefetch" href="/assets/params.html.c8cc13ef.js"><link rel="prefetch" href="/assets/tools.html.c0a4659a.js"><link rel="prefetch" href="/assets/about.html.5b0c0de9.js"><link rel="prefetch" href="/assets/settings.html.06993f96.js"><link rel="prefetch" href="/assets/404.html.686caba0.js"><link rel="prefetch" href="/assets/index.html.84bf285d.js"><link rel="prefetch" href="/assets/tageditor.html.66fa7b72.js"><link rel="prefetch" href="/assets/tagger.html.f698ca26.js"><link rel="prefetch" href="/assets/task.html.2f4311fb.js"><link rel="prefetch" href="/assets/tensorboard.html.e5ada3f5.js"><link rel="prefetch" href="/assets/index.html.4696b6e4.js"><link rel="prefetch" href="/assets/index.html.db1c0354.js"><link rel="prefetch" href="/assets/master.html.94401419.js"><link rel="prefetch" href="/assets/params.html.c90a6b4c.js"><link rel="prefetch" href="/assets/tools.html.1d9df334.js"><link rel="prefetch" href="/assets/about.html.2343ff24.js"><link rel="prefetch" href="/assets/settings.html.0626d062.js"><link rel="prefetch" href="/assets/404.html.cbf82dee.js"><link rel="prefetch" href="/assets/404.310165f5.js"><link rel="prefetch" href="/assets/layout.c140630d.js">
    <link rel="stylesheet" href="/assets/style.04eab9dc.css">
  </head>
  <body>
    <div id="app"><!--[--><div class="theme-container no-navbar"><!--[--><!----><!--]--><div class="sidebar-mask"></div><!--[--><aside class="sidebar" data-v-db8971c4><div class="el-scrollbar" data-v-db8971c4><div class="el-scrollbar__wrap el-scrollbar__wrap--hidden-default" style=""><div class="el-scrollbar__view" style=""><!--[--><div class="sidebar-container" data-v-db8971c4><!----><ul class="sidebar-items" data-v-db8971c4><!--[--><li><a href="/" class="sidebar-item sidebar-heading" aria-label="SD-Trainer"><!--[--><!--]--> SD-Trainer <!--[--><!--]--></a><!----></li><li><a href="/lora/index.md" class="sidebar-item sidebar-heading active" aria-label="LoRA训练"><!--[--><!--]--> LoRA训练 <!--[--><!--]--></a><ul style="" class="sidebar-item-children"><!--[--><li><a href="/lora/basic.md" class="sidebar-item active" aria-label="新手"><!--[--><!--]--> 新手 <!--[--><!--]--></a><!----></li><li><a href="/lora/master.md" class="sidebar-item" aria-label="专家"><!--[--><!--]--> 专家 <!--[--><!--]--></a><!----></li><li><a href="/lora/tools.md" class="sidebar-item" aria-label="工具"><!--[--><!--]--> 工具 <!--[--><!--]--></a><!----></li><li><a href="/lora/params.md" class="sidebar-item" aria-label="参数详解"><!--[--><!--]--> 参数详解 <!--[--><!--]--></a><!----></li><!--]--></ul></li><li><a href="/dreambooth/index.md" class="sidebar-item sidebar-heading" aria-label="Dreambooth 训练"><!--[--><!--]--> Dreambooth 训练 <!--[--><!--]--></a><!----></li><li><a href="/tensorboard.md" class="sidebar-item sidebar-heading" aria-label="Tensorboard"><!--[--><!--]--> Tensorboard <!--[--><!--]--></a><!----></li><li><a href="/tagger.md" class="sidebar-item sidebar-heading" aria-label="WD 1.4 标签器"><!--[--><!--]--> WD 1.4 标签器 <!--[--><!--]--></a><!----></li><li><a href="/tageditor.md" class="sidebar-item sidebar-heading" aria-label="标签编辑器"><!--[--><!--]--> 标签编辑器 <!--[--><!--]--></a><!----></li><li><p tabindex="0" class="sidebar-item sidebar-heading">其他 <!----></p><ul style="display:none;" class="sidebar-item-children"><!--[--><li><a href="/other/settings.md" class="sidebar-item" aria-label="UI 设置"><!--[--><!--]--> UI 设置 <!--[--><!--]--></a><!----></li><li><a href="/other/about.md" class="sidebar-item" aria-label="关于"><!--[--><!--]--> 关于 <!--[--><!--]--></a><!----></li><!--]--></ul></li><!--]--></ul><ul class="sidebar-bottom" data-v-db8971c4><li class="sidebar-item sidebar-heading appearance" data-v-db8971c4> Github <a class="icon" href="https://github.com/Akegarasu/lora-scripts" target="_blank" aria-label="GitHub" data-v-db8971c4><svg xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="xMidYMid meet" viewBox="0 0 24 24" data-v-db8971c4><path d="M12 2C6.475 2 2 6.475 2 12a9.994 9.994 0 0 0 6.838 9.488c.5.087.687-.213.687-.476c0-.237-.013-1.024-.013-1.862c-2.512.463-3.162-.612-3.362-1.175c-.113-.288-.6-1.175-1.025-1.413c-.35-.187-.85-.65-.013-.662c.788-.013 1.35.725 1.538 1.025c.9 1.512 2.338 1.087 2.912.825c.088-.65.35-1.087.638-1.337c-2.225-.25-4.55-1.113-4.55-4.938c0-1.088.387-1.987 1.025-2.688c-.1-.25-.45-1.275.1-2.65c0 0 .837-.262 2.75 1.026a9.28 9.28 0 0 1 2.5-.338c.85 0 1.7.112 2.5.337c1.912-1.3 2.75-1.024 2.75-1.024c.55 1.375.2 2.4.1 2.65c.637.7 1.025 1.587 1.025 2.687c0 3.838-2.337 4.688-4.562 4.938c.362.312.675.912.675 1.85c0 1.337-.013 2.412-.013 2.75c0 .262.188.574.688.474A10.016 10.016 0 0 0 22 12c0-5.525-4.475-10-10-10z" fill="currentColor" data-v-db8971c4></path></svg></a></li><li class="sidebar-item sidebar-heading appearance" data-v-db8971c4> Language <button class="toggle-color-mode-button" data-v-db8971c4><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" data-v-db8971c4><path d=" M12.87 15.07l-2.54-2.51.03-.03c1.74-1.94 2.98-4.17 3.71-6.53H17V4h-7V2H8v2H1v1.99h11.17C11.5 7.92 10.44 9.75 9 11.35 8.07 10.32 7.3 9.19 6.69 8h-2c.73 1.63 1.73 3.17 2.98 4.56l-5.09 5.02L4 19l5-5 3.11 3.11.76-2.04zM18.5 10h-2L12 22h2l1.12-3h4.75L21 22h2l-4.5-12zm-2.62 7l1.62-4.33L19.12 17h-3.24z " fill="currentColor" data-v-db8971c4></path></svg></button></li><li class="sidebar-item sidebar-heading appearance" data-v-db8971c4> 灯泡 <button class="toggle-color-mode-button" title="toggle color mode" data-v-db8971c4><svg style="" class="icon" focusable="false" viewBox="0 0 32 32"><path d="M16 12.005a4 4 0 1 1-4 4a4.005 4.005 0 0 1 4-4m0-2a6 6 0 1 0 6 6a6 6 0 0 0-6-6z" fill="currentColor"></path><path d="M5.394 6.813l1.414-1.415l3.506 3.506L8.9 10.318z" fill="currentColor"></path><path d="M2 15.005h5v2H2z" fill="currentColor"></path><path d="M5.394 25.197L8.9 21.691l1.414 1.415l-3.506 3.505z" fill="currentColor"></path><path d="M15 25.005h2v5h-2z" fill="currentColor"></path><path d="M21.687 23.106l1.414-1.415l3.506 3.506l-1.414 1.414z" fill="currentColor"></path><path d="M25 15.005h5v2h-5z" fill="currentColor"></path><path d="M21.687 8.904l3.506-3.506l1.414 1.415l-3.506 3.505z" fill="currentColor"></path><path d="M15 2.005h2v5h-2z" fill="currentColor"></path></svg><svg style="display:none;" class="icon" focusable="false" viewBox="0 0 32 32"><path d="M13.502 5.414a15.075 15.075 0 0 0 11.594 18.194a11.113 11.113 0 0 1-7.975 3.39c-.138 0-.278.005-.418 0a11.094 11.094 0 0 1-3.2-21.584M14.98 3a1.002 1.002 0 0 0-.175.016a13.096 13.096 0 0 0 1.825 25.981c.164.006.328 0 .49 0a13.072 13.072 0 0 0 10.703-5.555a1.01 1.01 0 0 0-.783-1.565A13.08 13.08 0 0 1 15.89 4.38A1.015 1.015 0 0 0 14.98 3z" fill="currentColor"></path></svg></button></li></ul></div><!--]--></div></div><!--[--><div class="el-scrollbar__bar is-horizontal" style="display:none;"><div class="el-scrollbar__thumb" style="width:0;transform:translateX(0%);"></div></div><div class="el-scrollbar__bar is-vertical" style="display:none;"><div class="el-scrollbar__thumb" style="height:0;transform:translateY(0%);"></div></div><!--]--></div></aside><!--]--><!--[--><div class="example-container"><!----><section class="schema-container"><div class="el-scrollbar"><div class="el-scrollbar__wrap el-scrollbar__wrap--hidden-default" style=""><div class="el-scrollbar__view" style=""><!--[--><form><!--[--><!----><!--[--><!----><!--[--><!--[--><!----><!--[--><h2 class="k-schema-header">训练用模型</h2><!--[--><!--[--><div class="k-schema-item"><div class="actions"></div><div class="k-schema-main"><div class="k-schema-left"><h3><!--[--><!--[--><!--[--><span class="prefix"></span><span>pretrained_model_name_or_path</span><!--[--><!--]--><!--]--><!--]--><!--]--></h3><!--[--><!--[--><!--[--><div class="markdown"><p>底模文件路径</p>
</div><!--]--><!--]--><!--]--></div><div class="k-schema-right"><!--[--><!--[--><!--]--><!--[--><!--]--><!--[--><!--]--><!--]--><!----></div><div class="k-schema-menu"><!--[--><button ariadisabled="false" type="button" class="el-button ellipsis el-tooltip__trigger el-tooltip__trigger" style=""><!--v-if--><span class=""><!--[--><svg class="k-icon" xmlns="http://www.w3.org/2000/svg" viewbox="0 0 448 512"><path fill="currentColor" d="M352 256C352 238.3 366.3 224 384 224C401.7 224 416 238.3 416 256C416 273.7 401.7 288 384 288C366.3 288 352 273.7 352 256zM192 256C192 238.3 206.3 224 224 224C241.7 224 256 238.3 256 256C256 273.7 241.7 288 224 288C206.3 288 192 273.7 192 256zM96 256C96 273.7 81.67 288 64 288C46.33 288 32 273.7 32 256C32 238.3 46.33 224 64 224C81.67 224 96 238.3 96 256z"></path></svg><!--]--></span></button><!--teleport start--><!--teleport end--><!--]--></div></div><!--[--><div class="bottom"><div class="el-input el-input--suffix" style=""><!-- input --><!--[--><!-- prepend slot --><!--v-if--><div class="el-input__wrapper"><!-- prefix slot --><!--v-if--><input class="el-input__inner" type autocomplete="off" tabindex="0" style=""><!-- suffix slot --><span class="el-input__suffix"><span class="el-input__suffix-inner"><!--[--><!--[--><span class="suffix-icon"><svg viewbox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" svg class="k-icon"><path fill="currentColor" d="M128 192v640h768V320H485.76L357.504 192H128zm-32-64h287.872l128.384 128H928a32 32 0 0 1 32 32v576a32 32 0 0 1-32 32H96a32 32 0 0 1-32-32V160a32 32 0 0 1 32-32z"></path></svg></span><!--]--><!--v-if--><!--]--><!--v-if--><!--v-if--><!--v-if--><!--v-if--></span></span></div><!-- append slot --><!--v-if--><!--]--></div></div><!--]--></div><!--[--><!--]--><!--]--><!--]--><!--]--><!--]--><!--[--><!----><!--[--><h2 class="k-schema-header">数据集设置</h2><!--[--><!--[--><div class="k-schema-item"><div class="actions"></div><div class="k-schema-main"><div class="k-schema-left"><h3><!--[--><!--[--><!--[--><span class="prefix"></span><span>train_data_dir</span><!--[--><!--]--><!--]--><!--]--><!--]--></h3><!--[--><!--[--><!--[--><div class="markdown"><p>训练数据集路径</p>
</div><!--]--><!--]--><!--]--></div><div class="k-schema-right"><!--[--><!--[--><!--]--><!--[--><!--]--><!--[--><!--]--><!--]--><!----></div><div class="k-schema-menu"><!--[--><button ariadisabled="false" type="button" class="el-button ellipsis el-tooltip__trigger el-tooltip__trigger" style=""><!--v-if--><span class=""><!--[--><svg class="k-icon" xmlns="http://www.w3.org/2000/svg" viewbox="0 0 448 512"><path fill="currentColor" d="M352 256C352 238.3 366.3 224 384 224C401.7 224 416 238.3 416 256C416 273.7 401.7 288 384 288C366.3 288 352 273.7 352 256zM192 256C192 238.3 206.3 224 224 224C241.7 224 256 238.3 256 256C256 273.7 241.7 288 224 288C206.3 288 192 273.7 192 256zM96 256C96 273.7 81.67 288 64 288C46.33 288 32 273.7 32 256C32 238.3 46.33 224 64 224C81.67 224 96 238.3 96 256z"></path></svg><!--]--></span></button><!--teleport start--><!--teleport end--><!--]--></div></div><!--[--><div class="bottom"><div class="el-input el-input--suffix" style=""><!-- input --><!--[--><!-- prepend slot --><!--v-if--><div class="el-input__wrapper"><!-- prefix slot --><!--v-if--><input class="el-input__inner" type autocomplete="off" tabindex="0" style=""><!-- suffix slot --><span class="el-input__suffix"><span class="el-input__suffix-inner"><!--[--><!--[--><span class="suffix-icon"><svg viewbox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" svg class="k-icon"><path fill="currentColor" d="M128 192v640h768V320H485.76L357.504 192H128zm-32-64h287.872l128.384 128H928a32 32 0 0 1 32 32v576a32 32 0 0 1-32 32H96a32 32 0 0 1-32-32V160a32 32 0 0 1 32-32z"></path></svg></span><!--]--><!--v-if--><!--]--><!--v-if--><!--v-if--><!--v-if--><!--v-if--></span></span></div><!-- append slot --><!--v-if--><!--]--></div></div><!--]--></div><!--[--><!--]--><!--]--><!--[--><div class="k-schema-item"><div class="actions"></div><div class="k-schema-main"><div class="k-schema-left"><h3><!--[--><!--[--><!--[--><span class="prefix"></span><span>reg_data_dir</span><!--[--><!--]--><!--]--><!--]--><!--]--></h3><!--[--><!--[--><!--[--><div class="markdown"><p>正则化数据集路径。默认留空，不使用正则化图像</p>
</div><!--]--><!--]--><!--]--></div><div class="k-schema-right"><!--[--><!--[--><!--]--><!--[--><!--]--><!--[--><!--]--><!--]--><!----></div><div class="k-schema-menu"><!--[--><button ariadisabled="false" type="button" class="el-button ellipsis el-tooltip__trigger el-tooltip__trigger" style=""><!--v-if--><span class=""><!--[--><svg class="k-icon" xmlns="http://www.w3.org/2000/svg" viewbox="0 0 448 512"><path fill="currentColor" d="M352 256C352 238.3 366.3 224 384 224C401.7 224 416 238.3 416 256C416 273.7 401.7 288 384 288C366.3 288 352 273.7 352 256zM192 256C192 238.3 206.3 224 224 224C241.7 224 256 238.3 256 256C256 273.7 241.7 288 224 288C206.3 288 192 273.7 192 256zM96 256C96 273.7 81.67 288 64 288C46.33 288 32 273.7 32 256C32 238.3 46.33 224 64 224C81.67 224 96 238.3 96 256z"></path></svg><!--]--></span></button><!--teleport start--><!--teleport end--><!--]--></div></div><!--[--><div class="bottom"><div class="el-input el-input--suffix" style=""><!-- input --><!--[--><!-- prepend slot --><!--v-if--><div class="el-input__wrapper"><!-- prefix slot --><!--v-if--><input class="el-input__inner" type autocomplete="off" tabindex="0" style=""><!-- suffix slot --><span class="el-input__suffix"><span class="el-input__suffix-inner"><!--[--><!--[--><span class="suffix-icon"><svg viewbox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" svg class="k-icon"><path fill="currentColor" d="M128 192v640h768V320H485.76L357.504 192H128zm-32-64h287.872l128.384 128H928a32 32 0 0 1 32 32v576a32 32 0 0 1-32 32H96a32 32 0 0 1-32-32V160a32 32 0 0 1 32-32z"></path></svg></span><!--]--><!--v-if--><!--]--><!--v-if--><!--v-if--><!--v-if--><!--v-if--></span></span></div><!-- append slot --><!--v-if--><!--]--></div></div><!--]--></div><!--[--><!--]--><!--]--><!--[--><div class="k-schema-item"><div class="actions"></div><div class="k-schema-main"><div class="k-schema-left"><h3><!--[--><!--[--><span class="prefix"></span><span>resolution</span><!--[--><!--]--><!--]--><!--]--></h3><!--[--><!--[--><div class="markdown"><p>训练图片分辨率，宽x高。支持非正方形，但必须是 64 倍数。</p>
</div><!--]--><!--]--></div><div class="k-schema-right"><!--[--><!--[--><!--]--><!--[--><!--[--><div class="el-input el-input--suffix" style="width:12rem;"><!-- input --><!--[--><!-- prepend slot --><!--v-if--><div class="el-input__wrapper"><!-- prefix slot --><!--v-if--><input class="el-input__inner" type="text" autocomplete="off" tabindex="0" style=""><!-- suffix slot --><span class="el-input__suffix"><span class="el-input__suffix-inner"><!--[--><!--[--><!--]--><!--v-if--><!--]--><!--v-if--><!--v-if--><!--v-if--><!--v-if--></span></span></div><!-- append slot --><!--v-if--><!--]--></div><!--]--><!--]--><!--[--><!--]--><!--]--><!----></div><div class="k-schema-menu"><!--[--><button ariadisabled="false" type="button" class="el-button ellipsis el-tooltip__trigger el-tooltip__trigger" style=""><!--v-if--><span class=""><!--[--><svg class="k-icon" xmlns="http://www.w3.org/2000/svg" viewbox="0 0 448 512"><path fill="currentColor" d="M352 256C352 238.3 366.3 224 384 224C401.7 224 416 238.3 416 256C416 273.7 401.7 288 384 288C366.3 288 352 273.7 352 256zM192 256C192 238.3 206.3 224 224 224C241.7 224 256 238.3 256 256C256 273.7 241.7 288 224 288C206.3 288 192 273.7 192 256zM96 256C96 273.7 81.67 288 64 288C46.33 288 32 273.7 32 256C32 238.3 46.33 224 64 224C81.67 224 96 238.3 96 256z"></path></svg><!--]--></span></button><!--teleport start--><!--teleport end--><!--]--></div></div><!--[--><!--]--></div><!--[--><!--]--><!--]--><!--]--><!--]--><!--]--><!--[--><!----><!--[--><h2 class="k-schema-header">保存设置</h2><!--[--><!--[--><div class="k-schema-item"><div class="actions"></div><div class="k-schema-main"><div class="k-schema-left"><h3><!--[--><!--[--><span class="prefix"></span><span>output_name</span><!--[--><!--]--><!--]--><!--]--></h3><!--[--><!--[--><div class="markdown"><p>模型保存名称</p>
</div><!--]--><!--]--></div><div class="k-schema-right"><!--[--><!--[--><!--]--><!--[--><!--[--><div class="el-input el-input--suffix" style="width:12rem;"><!-- input --><!--[--><!-- prepend slot --><!--v-if--><div class="el-input__wrapper"><!-- prefix slot --><!--v-if--><input class="el-input__inner" type="text" autocomplete="off" tabindex="0" style=""><!-- suffix slot --><span class="el-input__suffix"><span class="el-input__suffix-inner"><!--[--><!--[--><!--]--><!--v-if--><!--]--><!--v-if--><!--v-if--><!--v-if--><!--v-if--></span></span></div><!-- append slot --><!--v-if--><!--]--></div><!--]--><!--]--><!--[--><!--]--><!--]--><!----></div><div class="k-schema-menu"><!--[--><button ariadisabled="false" type="button" class="el-button ellipsis el-tooltip__trigger el-tooltip__trigger" style=""><!--v-if--><span class=""><!--[--><svg class="k-icon" xmlns="http://www.w3.org/2000/svg" viewbox="0 0 448 512"><path fill="currentColor" d="M352 256C352 238.3 366.3 224 384 224C401.7 224 416 238.3 416 256C416 273.7 401.7 288 384 288C366.3 288 352 273.7 352 256zM192 256C192 238.3 206.3 224 224 224C241.7 224 256 238.3 256 256C256 273.7 241.7 288 224 288C206.3 288 192 273.7 192 256zM96 256C96 273.7 81.67 288 64 288C46.33 288 32 273.7 32 256C32 238.3 46.33 224 64 224C81.67 224 96 238.3 96 256z"></path></svg><!--]--></span></button><!--teleport start--><!--teleport end--><!--]--></div></div><!--[--><!--]--></div><!--[--><!--]--><!--]--><!--[--><div class="k-schema-item"><div class="actions"></div><div class="k-schema-main"><div class="k-schema-left"><h3><!--[--><!--[--><!--[--><span class="prefix"></span><span>output_dir</span><!--[--><!--]--><!--]--><!--]--><!--]--></h3><!--[--><!--[--><!--[--><div class="markdown"><p>模型保存文件夹</p>
</div><!--]--><!--]--><!--]--></div><div class="k-schema-right"><!--[--><!--[--><!--]--><!--[--><!--]--><!--[--><!--]--><!--]--><!----></div><div class="k-schema-menu"><!--[--><button ariadisabled="false" type="button" class="el-button ellipsis el-tooltip__trigger el-tooltip__trigger" style=""><!--v-if--><span class=""><!--[--><svg class="k-icon" xmlns="http://www.w3.org/2000/svg" viewbox="0 0 448 512"><path fill="currentColor" d="M352 256C352 238.3 366.3 224 384 224C401.7 224 416 238.3 416 256C416 273.7 401.7 288 384 288C366.3 288 352 273.7 352 256zM192 256C192 238.3 206.3 224 224 224C241.7 224 256 238.3 256 256C256 273.7 241.7 288 224 288C206.3 288 192 273.7 192 256zM96 256C96 273.7 81.67 288 64 288C46.33 288 32 273.7 32 256C32 238.3 46.33 224 64 224C81.67 224 96 238.3 96 256z"></path></svg><!--]--></span></button><!--teleport start--><!--teleport end--><!--]--></div></div><!--[--><div class="bottom"><div class="el-input el-input--suffix" style=""><!-- input --><!--[--><!-- prepend slot --><!--v-if--><div class="el-input__wrapper"><!-- prefix slot --><!--v-if--><input class="el-input__inner" type autocomplete="off" tabindex="0" style=""><!-- suffix slot --><span class="el-input__suffix"><span class="el-input__suffix-inner"><!--[--><!--[--><span class="suffix-icon"><svg viewbox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" svg class="k-icon"><path fill="currentColor" d="M128 192v640h768V320H485.76L357.504 192H128zm-32-64h287.872l128.384 128H928a32 32 0 0 1 32 32v576a32 32 0 0 1-32 32H96a32 32 0 0 1-32-32V160a32 32 0 0 1 32-32z"></path></svg></span><!--]--><!--v-if--><!--]--><!--v-if--><!--v-if--><!--v-if--><!--v-if--></span></span></div><!-- append slot --><!--v-if--><!--]--></div></div><!--]--></div><!--[--><!--]--><!--]--><!--[--><div class="k-schema-item"><div class="actions"></div><div class="k-schema-main"><div class="k-schema-left"><h3><!--[--><!--[--><span class="prefix"></span><span>save_every_n_epochs</span><!--[--><!--]--><!--]--><!--]--></h3><!--[--><!--[--><div class="markdown"><p>每 N epoch（轮）自动保存一次模型</p>
</div><!--]--><!--]--></div><div class="k-schema-right"><!--[--><!--[--><!--]--><!--[--><!--[--><div class="el-input-number"><span role="button" aria-label="decrease number" class="el-input-number__decrease"><i class="el-icon" style=""><!--[--><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024"><path fill="currentColor" d="M128 544h768a32 32 0 1 0 0-64H128a32 32 0 0 0 0 64z"></path></svg><!--]--></i></span><span role="button" aria-label="increase number" class="el-input-number__increase"><i class="el-icon" style=""><!--[--><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024"><path fill="currentColor" d="M480 480V128a32 32 0 0 1 64 0v352h352a32 32 0 1 1 0 64H544v352a32 32 0 1 1-64 0V544H128a32 32 0 0 1 0-64h352z"></path></svg><!--]--></i></span><div class="el-input" style=""><!-- input --><!--[--><!-- prepend slot --><!--v-if--><div class="el-input__wrapper"><!-- prefix slot --><!--v-if--><input class="el-input__inner" step="1" max="Infinity" min="-Infinity" type="number" autocomplete="off" tabindex="0" style=""><!-- suffix slot --><!--v-if--></div><!-- append slot --><!--v-if--><!--]--></div></div><!--]--><!--]--><!--[--><!--]--><!--]--><!----></div><div class="k-schema-menu"><!--[--><button ariadisabled="false" type="button" class="el-button ellipsis el-tooltip__trigger el-tooltip__trigger" style=""><!--v-if--><span class=""><!--[--><svg class="k-icon" xmlns="http://www.w3.org/2000/svg" viewbox="0 0 448 512"><path fill="currentColor" d="M352 256C352 238.3 366.3 224 384 224C401.7 224 416 238.3 416 256C416 273.7 401.7 288 384 288C366.3 288 352 273.7 352 256zM192 256C192 238.3 206.3 224 224 224C241.7 224 256 238.3 256 256C256 273.7 241.7 288 224 288C206.3 288 192 273.7 192 256zM96 256C96 273.7 81.67 288 64 288C46.33 288 32 273.7 32 256C32 238.3 46.33 224 64 224C81.67 224 96 238.3 96 256z"></path></svg><!--]--></span></button><!--teleport start--><!--teleport end--><!--]--></div></div><!--[--><!--]--></div><!--[--><!--]--><!--]--><!--]--><!--]--><!--]--><!--[--><!----><!--[--><h2 class="k-schema-header">训练相关参数</h2><!--[--><!--[--><div class="k-schema-item"><div class="actions"></div><div class="k-schema-main"><div class="k-schema-left"><h3><!--[--><!--[--><span class="prefix"></span><span>max_train_epochs</span><!--[--><!--]--><!--]--><!--]--></h3><!--[--><!--[--><div class="markdown"><p>最大训练 epoch（轮数）</p>
</div><!--]--><!--]--></div><div class="k-schema-right"><!--[--><!--[--><!--]--><!--[--><!--[--><div class="el-input-number"><span role="button" aria-label="decrease number" class="el-input-number__decrease"><i class="el-icon" style=""><!--[--><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024"><path fill="currentColor" d="M128 544h768a32 32 0 1 0 0-64H128a32 32 0 0 0 0 64z"></path></svg><!--]--></i></span><span role="button" aria-label="increase number" class="el-input-number__increase"><i class="el-icon" style=""><!--[--><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024"><path fill="currentColor" d="M480 480V128a32 32 0 0 1 64 0v352h352a32 32 0 1 1 0 64H544v352a32 32 0 1 1-64 0V544H128a32 32 0 0 1 0-64h352z"></path></svg><!--]--></i></span><div class="el-input" style=""><!-- input --><!--[--><!-- prepend slot --><!--v-if--><div class="el-input__wrapper"><!-- prefix slot --><!--v-if--><input class="el-input__inner" step="1" max="Infinity" min="1" type="number" autocomplete="off" tabindex="0" style=""><!-- suffix slot --><!--v-if--></div><!-- append slot --><!--v-if--><!--]--></div></div><!--]--><!--]--><!--[--><!--]--><!--]--><!----></div><div class="k-schema-menu"><!--[--><button ariadisabled="false" type="button" class="el-button ellipsis el-tooltip__trigger el-tooltip__trigger" style=""><!--v-if--><span class=""><!--[--><svg class="k-icon" xmlns="http://www.w3.org/2000/svg" viewbox="0 0 448 512"><path fill="currentColor" d="M352 256C352 238.3 366.3 224 384 224C401.7 224 416 238.3 416 256C416 273.7 401.7 288 384 288C366.3 288 352 273.7 352 256zM192 256C192 238.3 206.3 224 224 224C241.7 224 256 238.3 256 256C256 273.7 241.7 288 224 288C206.3 288 192 273.7 192 256zM96 256C96 273.7 81.67 288 64 288C46.33 288 32 273.7 32 256C32 238.3 46.33 224 64 224C81.67 224 96 238.3 96 256z"></path></svg><!--]--></span></button><!--teleport start--><!--teleport end--><!--]--></div></div><!--[--><!--]--></div><!--[--><!--]--><!--]--><!--[--><div class="k-schema-item"><div class="actions"></div><div class="k-schema-main"><div class="k-schema-left"><h3><!--[--><!--[--><span class="prefix"></span><span>train_batch_size</span><!--[--><!--]--><!--]--><!--]--></h3><!--[--><!--[--><div class="markdown"><p>批量大小</p>
</div><!--]--><!--]--></div><div class="k-schema-right"><!--[--><!--[--><!--]--><!--[--><!--[--><div class="el-input-number"><span role="button" aria-label="decrease number" class="el-input-number__decrease is-disabled"><i class="el-icon" style=""><!--[--><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024"><path fill="currentColor" d="M128 544h768a32 32 0 1 0 0-64H128a32 32 0 0 0 0 64z"></path></svg><!--]--></i></span><span role="button" aria-label="increase number" class="el-input-number__increase"><i class="el-icon" style=""><!--[--><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024"><path fill="currentColor" d="M480 480V128a32 32 0 0 1 64 0v352h352a32 32 0 1 1 0 64H544v352a32 32 0 1 1-64 0V544H128a32 32 0 0 1 0-64h352z"></path></svg><!--]--></i></span><div class="el-input" style=""><!-- input --><!--[--><!-- prepend slot --><!--v-if--><div class="el-input__wrapper"><!-- prefix slot --><!--v-if--><input class="el-input__inner" step="1" max="Infinity" min="1" type="number" autocomplete="off" tabindex="0" style=""><!-- suffix slot --><!--v-if--></div><!-- append slot --><!--v-if--><!--]--></div></div><!--]--><!--]--><!--[--><!--]--><!--]--><!----></div><div class="k-schema-menu"><!--[--><button ariadisabled="false" type="button" class="el-button ellipsis el-tooltip__trigger el-tooltip__trigger" style=""><!--v-if--><span class=""><!--[--><svg class="k-icon" xmlns="http://www.w3.org/2000/svg" viewbox="0 0 448 512"><path fill="currentColor" d="M352 256C352 238.3 366.3 224 384 224C401.7 224 416 238.3 416 256C416 273.7 401.7 288 384 288C366.3 288 352 273.7 352 256zM192 256C192 238.3 206.3 224 224 224C241.7 224 256 238.3 256 256C256 273.7 241.7 288 224 288C206.3 288 192 273.7 192 256zM96 256C96 273.7 81.67 288 64 288C46.33 288 32 273.7 32 256C32 238.3 46.33 224 64 224C81.67 224 96 238.3 96 256z"></path></svg><!--]--></span></button><!--teleport start--><!--teleport end--><!--]--></div></div><!--[--><!--]--></div><!--[--><!--]--><!--]--><!--]--><!--]--><!--]--><!--[--><!----><!--[--><!----><!--[--><!--[--><!----><!--[--><h2 class="k-schema-header">学习率与优化器设置</h2><!--[--><!--[--><div class="k-schema-item"><div class="actions"></div><div class="k-schema-main"><div class="k-schema-left"><h3><!--[--><!--[--><span class="prefix"></span><span>unet_lr</span><!--[--><!--]--><!--]--><!--]--></h3><!--[--><!--[--><div class="markdown"><p>U-Net 学习率</p>
</div><!--]--><!--]--></div><div class="k-schema-right"><!--[--><!--[--><!--]--><!--[--><!--[--><div class="el-input el-input--suffix" style="width:12rem;"><!-- input --><!--[--><!-- prepend slot --><!--v-if--><div class="el-input__wrapper"><!-- prefix slot --><!--v-if--><input class="el-input__inner" type="text" autocomplete="off" tabindex="0" style=""><!-- suffix slot --><span class="el-input__suffix"><span class="el-input__suffix-inner"><!--[--><!--[--><!--]--><!--v-if--><!--]--><!--v-if--><!--v-if--><!--v-if--><!--v-if--></span></span></div><!-- append slot --><!--v-if--><!--]--></div><!--]--><!--]--><!--[--><!--]--><!--]--><!----></div><div class="k-schema-menu"><!--[--><button ariadisabled="false" type="button" class="el-button ellipsis el-tooltip__trigger el-tooltip__trigger" style=""><!--v-if--><span class=""><!--[--><svg class="k-icon" xmlns="http://www.w3.org/2000/svg" viewbox="0 0 448 512"><path fill="currentColor" d="M352 256C352 238.3 366.3 224 384 224C401.7 224 416 238.3 416 256C416 273.7 401.7 288 384 288C366.3 288 352 273.7 352 256zM192 256C192 238.3 206.3 224 224 224C241.7 224 256 238.3 256 256C256 273.7 241.7 288 224 288C206.3 288 192 273.7 192 256zM96 256C96 273.7 81.67 288 64 288C46.33 288 32 273.7 32 256C32 238.3 46.33 224 64 224C81.67 224 96 238.3 96 256z"></path></svg><!--]--></span></button><!--teleport start--><!--teleport end--><!--]--></div></div><!--[--><!--]--></div><!--[--><!--]--><!--]--><!--[--><div class="k-schema-item"><div class="actions"></div><div class="k-schema-main"><div class="k-schema-left"><h3><!--[--><!--[--><span class="prefix"></span><span>text_encoder_lr</span><!--[--><!--]--><!--]--><!--]--></h3><!--[--><!--[--><div class="markdown"><p>文本编码器学习率</p>
</div><!--]--><!--]--></div><div class="k-schema-right"><!--[--><!--[--><!--]--><!--[--><!--[--><div class="el-input el-input--suffix" style="width:12rem;"><!-- input --><!--[--><!-- prepend slot --><!--v-if--><div class="el-input__wrapper"><!-- prefix slot --><!--v-if--><input class="el-input__inner" type="text" autocomplete="off" tabindex="0" style=""><!-- suffix slot --><span class="el-input__suffix"><span class="el-input__suffix-inner"><!--[--><!--[--><!--]--><!--v-if--><!--]--><!--v-if--><!--v-if--><!--v-if--><!--v-if--></span></span></div><!-- append slot --><!--v-if--><!--]--></div><!--]--><!--]--><!--[--><!--]--><!--]--><!----></div><div class="k-schema-menu"><!--[--><button ariadisabled="false" type="button" class="el-button ellipsis el-tooltip__trigger el-tooltip__trigger" style=""><!--v-if--><span class=""><!--[--><svg class="k-icon" xmlns="http://www.w3.org/2000/svg" viewbox="0 0 448 512"><path fill="currentColor" d="M352 256C352 238.3 366.3 224 384 224C401.7 224 416 238.3 416 256C416 273.7 401.7 288 384 288C366.3 288 352 273.7 352 256zM192 256C192 238.3 206.3 224 224 224C241.7 224 256 238.3 256 256C256 273.7 241.7 288 224 288C206.3 288 192 273.7 192 256zM96 256C96 273.7 81.67 288 64 288C46.33 288 32 273.7 32 256C32 238.3 46.33 224 64 224C81.67 224 96 238.3 96 256z"></path></svg><!--]--></span></button><!--teleport start--><!--teleport end--><!--]--></div></div><!--[--><!--]--></div><!--[--><!--]--><!--]--><!--[--><div class="k-schema-item"><div class="actions"></div><div class="k-schema-main"><div class="k-schema-left"><h3><!--[--><!--[--><!--[--><!--[--><span class="prefix"></span><span>lr_scheduler</span><!--[--><!--]--><!--]--><!--]--><!--]--><!--]--></h3><!--[--><!--[--><!--[--><!--[--><div class="markdown"><p>学习率调度器设置</p>
</div><!--]--><!--]--><!--]--><!--]--></div><div class="k-schema-right"><!--[--><!--[--><!--[--><div class="el-select"><!--[--><div class="select-trigger el-tooltip__trigger el-tooltip__trigger"><!--v-if--><!-- fix: https://github.com/element-plus/element-plus/issues/11415 --><!--v-if--><div class="el-input el-input--suffix" style=""><!-- input --><!--[--><!-- prepend slot --><!--v-if--><div class="el-input__wrapper"><!-- prefix slot --><!--v-if--><input class="el-input__inner" type="text" readonly autocomplete="off" tabindex="0" placeholder="Select" style=""><!-- suffix slot --><span class="el-input__suffix"><span class="el-input__suffix-inner"><!--[--><!--[--><i class="el-icon el-select__caret el-select__icon" style=""><!--[--><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024"><path fill="currentColor" d="M831.872 340.864 512 652.672 192.128 340.864a30.592 30.592 0 0 0-42.752 0 29.12 29.12 0 0 0 0 41.6L489.664 714.24a32 32 0 0 0 44.672 0l340.288-331.712a29.12 29.12 0 0 0 0-41.728 30.592 30.592 0 0 0-42.752 0z"></path></svg><!--]--></i><!--v-if--><!--]--><!--v-if--><!--]--><!--v-if--><!--v-if--><!--v-if--><!--v-if--></span></span></div><!-- append slot --><!--v-if--><!--]--></div></div><!--teleport start--><!--teleport end--><!--]--></div><!--]--><!--]--><!--[--><!--]--><!--[--><!--]--><!--]--><!----></div><div class="k-schema-menu"><!--[--><button ariadisabled="false" type="button" class="el-button ellipsis el-tooltip__trigger el-tooltip__trigger" style=""><!--v-if--><span class=""><!--[--><svg class="k-icon" xmlns="http://www.w3.org/2000/svg" viewbox="0 0 448 512"><path fill="currentColor" d="M352 256C352 238.3 366.3 224 384 224C401.7 224 416 238.3 416 256C416 273.7 401.7 288 384 288C366.3 288 352 273.7 352 256zM192 256C192 238.3 206.3 224 224 224C241.7 224 256 238.3 256 256C256 273.7 241.7 288 224 288C206.3 288 192 273.7 192 256zM96 256C96 273.7 81.67 288 64 288C46.33 288 32 273.7 32 256C32 238.3 46.33 224 64 224C81.67 224 96 238.3 96 256z"></path></svg><!--]--></span></button><!--teleport start--><!--teleport end--><!--]--></div></div><!--[--><!--]--></div><!--[--><!--]--><!--]--><!--[--><div class="k-schema-item"><div class="actions"></div><div class="k-schema-main"><div class="k-schema-left"><h3><!--[--><!--[--><span class="prefix"></span><span>lr_warmup_steps</span><!--[--><!--]--><!--]--><!--]--></h3><!--[--><!--[--><div class="markdown"><p>学习率预热步数</p>
</div><!--]--><!--]--></div><div class="k-schema-right"><!--[--><!--[--><!--]--><!--[--><!--[--><div class="el-input-number"><span role="button" aria-label="decrease number" class="el-input-number__decrease"><i class="el-icon" style=""><!--[--><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024"><path fill="currentColor" d="M128 544h768a32 32 0 1 0 0-64H128a32 32 0 0 0 0 64z"></path></svg><!--]--></i></span><span role="button" aria-label="increase number" class="el-input-number__increase"><i class="el-icon" style=""><!--[--><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024"><path fill="currentColor" d="M480 480V128a32 32 0 0 1 64 0v352h352a32 32 0 1 1 0 64H544v352a32 32 0 1 1-64 0V544H128a32 32 0 0 1 0-64h352z"></path></svg><!--]--></i></span><div class="el-input" style=""><!-- input --><!--[--><!-- prepend slot --><!--v-if--><div class="el-input__wrapper"><!-- prefix slot --><!--v-if--><input class="el-input__inner" step="1" max="Infinity" min="-Infinity" type="number" autocomplete="off" tabindex="0" style=""><!-- suffix slot --><!--v-if--></div><!-- append slot --><!--v-if--><!--]--></div></div><!--]--><!--]--><!--[--><!--]--><!--]--><!----></div><div class="k-schema-menu"><!--[--><button ariadisabled="false" type="button" class="el-button ellipsis el-tooltip__trigger el-tooltip__trigger" style=""><!--v-if--><span class=""><!--[--><svg class="k-icon" xmlns="http://www.w3.org/2000/svg" viewbox="0 0 448 512"><path fill="currentColor" d="M352 256C352 238.3 366.3 224 384 224C401.7 224 416 238.3 416 256C416 273.7 401.7 288 384 288C366.3 288 352 273.7 352 256zM192 256C192 238.3 206.3 224 224 224C241.7 224 256 238.3 256 256C256 273.7 241.7 288 224 288C206.3 288 192 273.7 192 256zM96 256C96 273.7 81.67 288 64 288C46.33 288 32 273.7 32 256C32 238.3 46.33 224 64 224C81.67 224 96 238.3 96 256z"></path></svg><!--]--></span></button><!--teleport start--><!--teleport end--><!--]--></div></div><!--[--><!--]--></div><!--[--><!--]--><!--]--><!--]--><!--]--><!--]--><!--[--><!----><!--[--><!----><!--[--><!----><!--[--><div class="k-schema-item"><div class="actions"></div><div class="k-schema-main"><div class="k-schema-left"><h3><!--[--><!--[--><span class="prefix"></span><span>lr_scheduler_num_cycles</span><!--[--><!--]--><!--]--><!--]--></h3><!--[--><!--[--><div class="markdown"><p>重启次数</p>
</div><!--]--><!--]--></div><div class="k-schema-right"><!--[--><!--[--><!--]--><!--[--><!--[--><div class="el-input-number"><span role="button" aria-label="decrease number" class="el-input-number__decrease"><i class="el-icon" style=""><!--[--><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024"><path fill="currentColor" d="M128 544h768a32 32 0 1 0 0-64H128a32 32 0 0 0 0 64z"></path></svg><!--]--></i></span><span role="button" aria-label="increase number" class="el-input-number__increase"><i class="el-icon" style=""><!--[--><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024"><path fill="currentColor" d="M480 480V128a32 32 0 0 1 64 0v352h352a32 32 0 1 1 0 64H544v352a32 32 0 1 1-64 0V544H128a32 32 0 0 1 0-64h352z"></path></svg><!--]--></i></span><div class="el-input" style=""><!-- input --><!--[--><!-- prepend slot --><!--v-if--><div class="el-input__wrapper"><!-- prefix slot --><!--v-if--><input class="el-input__inner" step="1" max="Infinity" min="-Infinity" type="number" autocomplete="off" tabindex="0" style=""><!-- suffix slot --><!--v-if--></div><!-- append slot --><!--v-if--><!--]--></div></div><!--]--><!--]--><!--[--><!--]--><!--]--><!----></div><div class="k-schema-menu"><!--[--><button ariadisabled="false" type="button" class="el-button ellipsis el-tooltip__trigger el-tooltip__trigger" style=""><!--v-if--><span class=""><!--[--><svg class="k-icon" xmlns="http://www.w3.org/2000/svg" viewbox="0 0 448 512"><path fill="currentColor" d="M352 256C352 238.3 366.3 224 384 224C401.7 224 416 238.3 416 256C416 273.7 401.7 288 384 288C366.3 288 352 273.7 352 256zM192 256C192 238.3 206.3 224 224 224C241.7 224 256 238.3 256 256C256 273.7 241.7 288 224 288C206.3 288 192 273.7 192 256zM96 256C96 273.7 81.67 288 64 288C46.33 288 32 273.7 32 256C32 238.3 46.33 224 64 224C81.67 224 96 238.3 96 256z"></path></svg><!--]--></span></button><!--teleport start--><!--teleport end--><!--]--></div></div><!--[--><!--]--></div><!--[--><!--]--><!--]--><!--]--><!--]--><!--]--><!--[--><!----><!--[--><!----><!--[--><!--[--><div class="k-schema-item"><div class="actions"></div><div class="k-schema-main"><div class="k-schema-left"><h3><!--[--><!--[--><!--[--><!--[--><span class="prefix"></span><span>optimizer_type</span><!--[--><!--]--><!--]--><!--]--><!--]--><!--]--></h3><!--[--><!--[--><!--[--><!--[--><div class="markdown"><p>优化器设置</p>
</div><!--]--><!--]--><!--]--><!--]--></div><div class="k-schema-right"><!--[--><!--[--><!--[--><div class="el-select"><!--[--><div class="select-trigger el-tooltip__trigger el-tooltip__trigger"><!--v-if--><!-- fix: https://github.com/element-plus/element-plus/issues/11415 --><!--v-if--><div class="el-input el-input--suffix" style=""><!-- input --><!--[--><!-- prepend slot --><!--v-if--><div class="el-input__wrapper"><!-- prefix slot --><!--v-if--><input class="el-input__inner" type="text" readonly autocomplete="off" tabindex="0" placeholder="Select" style=""><!-- suffix slot --><span class="el-input__suffix"><span class="el-input__suffix-inner"><!--[--><!--[--><i class="el-icon el-select__caret el-select__icon" style=""><!--[--><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024"><path fill="currentColor" d="M831.872 340.864 512 652.672 192.128 340.864a30.592 30.592 0 0 0-42.752 0 29.12 29.12 0 0 0 0 41.6L489.664 714.24a32 32 0 0 0 44.672 0l340.288-331.712a29.12 29.12 0 0 0 0-41.728 30.592 30.592 0 0 0-42.752 0z"></path></svg><!--]--></i><!--v-if--><!--]--><!--v-if--><!--]--><!--v-if--><!--v-if--><!--v-if--><!--v-if--></span></span></div><!-- append slot --><!--v-if--><!--]--></div></div><!--teleport start--><!--teleport end--><!--]--></div><!--]--><!--]--><!--[--><!--]--><!--[--><!--]--><!--]--><!----></div><div class="k-schema-menu"><!--[--><button ariadisabled="false" type="button" class="el-button ellipsis el-tooltip__trigger el-tooltip__trigger" style=""><!--v-if--><span class=""><!--[--><svg class="k-icon" xmlns="http://www.w3.org/2000/svg" viewbox="0 0 448 512"><path fill="currentColor" d="M352 256C352 238.3 366.3 224 384 224C401.7 224 416 238.3 416 256C416 273.7 401.7 288 384 288C366.3 288 352 273.7 352 256zM192 256C192 238.3 206.3 224 224 224C241.7 224 256 238.3 256 256C256 273.7 241.7 288 224 288C206.3 288 192 273.7 192 256zM96 256C96 273.7 81.67 288 64 288C46.33 288 32 273.7 32 256C32 238.3 46.33 224 64 224C81.67 224 96 238.3 96 256z"></path></svg><!--]--></span></button><!--teleport start--><!--teleport end--><!--]--></div></div><!--[--><!--]--></div><!--[--><!--]--><!--]--><!--]--><!--]--><!--]--><!--]--><!--]--><!--]--><!--[--><!----><!--[--><!----><!--[--><!--[--><!----><!--[--><h2 class="k-schema-header">训练预览图设置</h2><!--[--><!--[--><div class="k-schema-item"><div class="actions"></div><div class="k-schema-main"><div class="k-schema-left"><h3><!--[--><!--[--><span class="prefix"></span><span>enable_preview</span><!--[--><!--]--><!--]--><!--]--></h3><!--[--><!--[--><div class="markdown"><p>启用训练预览图</p>
</div><!--]--><!--]--></div><div class="k-schema-right"><!--[--><!--[--><!--]--><!--[--><!--[--><div class="el-switch" style=""><input class="el-switch__input" type="checkbox" role="switch" aria-checked="false" aria-disabled="false" name true-value="true" false-value="false"><!--v-if--><span class="el-switch__core" style="width:;"><!--v-if--><div class="el-switch__action"><!--v-if--></div></span><!--v-if--></div><!--]--><!--]--><!--[--><!--]--><!--]--><!----></div><div class="k-schema-menu"><!--[--><button ariadisabled="false" type="button" class="el-button ellipsis el-tooltip__trigger el-tooltip__trigger" style=""><!--v-if--><span class=""><!--[--><svg class="k-icon" xmlns="http://www.w3.org/2000/svg" viewbox="0 0 448 512"><path fill="currentColor" d="M352 256C352 238.3 366.3 224 384 224C401.7 224 416 238.3 416 256C416 273.7 401.7 288 384 288C366.3 288 352 273.7 352 256zM192 256C192 238.3 206.3 224 224 224C241.7 224 256 238.3 256 256C256 273.7 241.7 288 224 288C206.3 288 192 273.7 192 256zM96 256C96 273.7 81.67 288 64 288C46.33 288 32 273.7 32 256C32 238.3 46.33 224 64 224C81.67 224 96 238.3 96 256z"></path></svg><!--]--></span></button><!--teleport start--><!--teleport end--><!--]--></div></div><!--[--><!--]--></div><!--[--><!--]--><!--]--><!--]--><!--]--><!--]--><!--[--><!----><!--[--><!----><!--[--><!--]--><!--]--><!--]--><!--]--><!--]--><!--]--><!--[--><!----><!--[--><!----><!--[--><!--[--><!----><!--[--><h2 class="k-schema-header">网络设置</h2><!--[--><!--[--><div class="k-schema-item"><div class="actions"></div><div class="k-schema-main"><div class="k-schema-left"><h3><!--[--><!--[--><!--[--><span class="prefix"></span><span>network_weights</span><!--[--><!--]--><!--]--><!--]--><!--]--></h3><!--[--><!--[--><!--[--><div class="markdown"><p>从已有的 LoRA 模型上继续训练，填写路径</p>
</div><!--]--><!--]--><!--]--></div><div class="k-schema-right"><!--[--><!--[--><!--]--><!--[--><!--]--><!--[--><!--]--><!--]--><!----></div><div class="k-schema-menu"><!--[--><button ariadisabled="false" type="button" class="el-button ellipsis el-tooltip__trigger el-tooltip__trigger" style=""><!--v-if--><span class=""><!--[--><svg class="k-icon" xmlns="http://www.w3.org/2000/svg" viewbox="0 0 448 512"><path fill="currentColor" d="M352 256C352 238.3 366.3 224 384 224C401.7 224 416 238.3 416 256C416 273.7 401.7 288 384 288C366.3 288 352 273.7 352 256zM192 256C192 238.3 206.3 224 224 224C241.7 224 256 238.3 256 256C256 273.7 241.7 288 224 288C206.3 288 192 273.7 192 256zM96 256C96 273.7 81.67 288 64 288C46.33 288 32 273.7 32 256C32 238.3 46.33 224 64 224C81.67 224 96 238.3 96 256z"></path></svg><!--]--></span></button><!--teleport start--><!--teleport end--><!--]--></div></div><!--[--><div class="bottom"><div class="el-input el-input--suffix" style=""><!-- input --><!--[--><!-- prepend slot --><!--v-if--><div class="el-input__wrapper"><!-- prefix slot --><!--v-if--><input class="el-input__inner" type autocomplete="off" tabindex="0" style=""><!-- suffix slot --><span class="el-input__suffix"><span class="el-input__suffix-inner"><!--[--><!--[--><span class="suffix-icon"><svg viewbox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" svg class="k-icon"><path fill="currentColor" d="M128 192v640h768V320H485.76L357.504 192H128zm-32-64h287.872l128.384 128H928a32 32 0 0 1 32 32v576a32 32 0 0 1-32 32H96a32 32 0 0 1-32-32V160a32 32 0 0 1 32-32z"></path></svg></span><!--]--><!--v-if--><!--]--><!--v-if--><!--v-if--><!--v-if--><!--v-if--></span></span></div><!-- append slot --><!--v-if--><!--]--></div></div><!--]--></div><!--[--><!--]--><!--]--><!--[--><div class="k-schema-item"><div class="actions"></div><div class="k-schema-main"><div class="k-schema-left"><h3><!--[--><!--[--><span class="prefix"></span><span>network_dim</span><!--[--><!--]--><!--]--><!--]--></h3><!--[--><!--[--><div class="markdown"><p>网络维度，常用 4~128，不是越大越好</p>
</div><!--]--><!--]--></div><div class="k-schema-right"><!--[--><!--[--><!--]--><!--[--><!--[--><div class="el-input-number"><span role="button" aria-label="decrease number" class="el-input-number__decrease"><i class="el-icon" style=""><!--[--><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024"><path fill="currentColor" d="M128 544h768a32 32 0 1 0 0-64H128a32 32 0 0 0 0 64z"></path></svg><!--]--></i></span><span role="button" aria-label="increase number" class="el-input-number__increase"><i class="el-icon" style=""><!--[--><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024"><path fill="currentColor" d="M480 480V128a32 32 0 0 1 64 0v352h352a32 32 0 1 1 0 64H544v352a32 32 0 1 1-64 0V544H128a32 32 0 0 1 0-64h352z"></path></svg><!--]--></i></span><div class="el-input" style=""><!-- input --><!--[--><!-- prepend slot --><!--v-if--><div class="el-input__wrapper"><!-- prefix slot --><!--v-if--><input class="el-input__inner" step="8" max="256" min="8" type="number" autocomplete="off" tabindex="0" style=""><!-- suffix slot --><!--v-if--></div><!-- append slot --><!--v-if--><!--]--></div></div><!--]--><!--]--><!--[--><!--]--><!--]--><!----></div><div class="k-schema-menu"><!--[--><button ariadisabled="false" type="button" class="el-button ellipsis el-tooltip__trigger el-tooltip__trigger" style=""><!--v-if--><span class=""><!--[--><svg class="k-icon" xmlns="http://www.w3.org/2000/svg" viewbox="0 0 448 512"><path fill="currentColor" d="M352 256C352 238.3 366.3 224 384 224C401.7 224 416 238.3 416 256C416 273.7 401.7 288 384 288C366.3 288 352 273.7 352 256zM192 256C192 238.3 206.3 224 224 224C241.7 224 256 238.3 256 256C256 273.7 241.7 288 224 288C206.3 288 192 273.7 192 256zM96 256C96 273.7 81.67 288 64 288C46.33 288 32 273.7 32 256C32 238.3 46.33 224 64 224C81.67 224 96 238.3 96 256z"></path></svg><!--]--></span></button><!--teleport start--><!--teleport end--><!--]--></div></div><!--[--><!--]--></div><!--[--><!--]--><!--]--><!--[--><div class="k-schema-item"><div class="actions"></div><div class="k-schema-main"><div class="k-schema-left"><h3><!--[--><!--[--><span class="prefix"></span><span>network_alpha</span><!--[--><!--]--><!--]--><!--]--></h3><!--[--><!--[--><div class="markdown"><p>常用值：等于 network_dim 或 network_dim*1/2 或 1。使用较小的 alpha 需要提升学习率。</p>
</div><!--]--><!--]--></div><div class="k-schema-right"><!--[--><!--[--><!--]--><!--[--><!--[--><div class="el-input-number"><span role="button" aria-label="decrease number" class="el-input-number__decrease"><i class="el-icon" style=""><!--[--><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024"><path fill="currentColor" d="M128 544h768a32 32 0 1 0 0-64H128a32 32 0 0 0 0 64z"></path></svg><!--]--></i></span><span role="button" aria-label="increase number" class="el-input-number__increase"><i class="el-icon" style=""><!--[--><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024"><path fill="currentColor" d="M480 480V128a32 32 0 0 1 64 0v352h352a32 32 0 1 1 0 64H544v352a32 32 0 1 1-64 0V544H128a32 32 0 0 1 0-64h352z"></path></svg><!--]--></i></span><div class="el-input" style=""><!-- input --><!--[--><!-- prepend slot --><!--v-if--><div class="el-input__wrapper"><!-- prefix slot --><!--v-if--><input class="el-input__inner" step="1" max="Infinity" min="1" type="number" autocomplete="off" tabindex="0" style=""><!-- suffix slot --><!--v-if--></div><!-- append slot --><!--v-if--><!--]--></div></div><!--]--><!--]--><!--[--><!--]--><!--]--><!----></div><div class="k-schema-menu"><!--[--><button ariadisabled="false" type="button" class="el-button ellipsis el-tooltip__trigger el-tooltip__trigger" style=""><!--v-if--><span class=""><!--[--><svg class="k-icon" xmlns="http://www.w3.org/2000/svg" viewbox="0 0 448 512"><path fill="currentColor" d="M352 256C352 238.3 366.3 224 384 224C401.7 224 416 238.3 416 256C416 273.7 401.7 288 384 288C366.3 288 352 273.7 352 256zM192 256C192 238.3 206.3 224 224 224C241.7 224 256 238.3 256 256C256 273.7 241.7 288 224 288C206.3 288 192 273.7 192 256zM96 256C96 273.7 81.67 288 64 288C46.33 288 32 273.7 32 256C32 238.3 46.33 224 64 224C81.67 224 96 238.3 96 256z"></path></svg><!--]--></span></button><!--teleport start--><!--teleport end--><!--]--></div></div><!--[--><!--]--></div><!--[--><!--]--><!--]--><!--]--><!--]--><!--]--><!--]--><!--]--><!--]--><!--[--><!----><!--[--><h2 class="k-schema-header">caption 选项</h2><!--[--><!--[--><div class="k-schema-item"><div class="actions"></div><div class="k-schema-main"><div class="k-schema-left"><h3><!--[--><!--[--><span class="prefix"></span><span>shuffle_caption</span><!--[--><!--]--><!--]--><!--]--></h3><!--[--><!--[--><div class="markdown"><p>训练时随机打乱 tokens</p>
</div><!--]--><!--]--></div><div class="k-schema-right"><!--[--><!--[--><!--]--><!--[--><!--[--><div class="el-switch is-checked" style=""><input class="el-switch__input" type="checkbox" role="switch" aria-checked="true" aria-disabled="false" name true-value="true" false-value="false"><!--v-if--><span class="el-switch__core" style="width:;"><!--v-if--><div class="el-switch__action"><!--v-if--></div></span><!--v-if--></div><!--]--><!--]--><!--[--><!--]--><!--]--><!----></div><div class="k-schema-menu"><!--[--><button ariadisabled="false" type="button" class="el-button ellipsis el-tooltip__trigger el-tooltip__trigger" style=""><!--v-if--><span class=""><!--[--><svg class="k-icon" xmlns="http://www.w3.org/2000/svg" viewbox="0 0 448 512"><path fill="currentColor" d="M352 256C352 238.3 366.3 224 384 224C401.7 224 416 238.3 416 256C416 273.7 401.7 288 384 288C366.3 288 352 273.7 352 256zM192 256C192 238.3 206.3 224 224 224C241.7 224 256 238.3 256 256C256 273.7 241.7 288 224 288C206.3 288 192 273.7 192 256zM96 256C96 273.7 81.67 288 64 288C46.33 288 32 273.7 32 256C32 238.3 46.33 224 64 224C81.67 224 96 238.3 96 256z"></path></svg><!--]--></span></button><!--teleport start--><!--teleport end--><!--]--></div></div><!--[--><!--]--></div><!--[--><!--]--><!--]--><!--[--><div class="k-schema-item"><div class="actions"></div><div class="k-schema-main"><div class="k-schema-left"><h3><!--[--><!--[--><span class="prefix"></span><span>keep_tokens</span><!--[--><!--]--><!--]--><!--]--></h3><!--[--><!--[--><div class="markdown"><p>在随机打乱 tokens 时，保留前 N 个不变</p>
</div><!--]--><!--]--></div><div class="k-schema-right"><!--[--><!--[--><!--]--><!--[--><!--[--><div class="el-input-number"><span role="button" aria-label="decrease number" class="el-input-number__decrease is-disabled"><i class="el-icon" style=""><!--[--><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024"><path fill="currentColor" d="M128 544h768a32 32 0 1 0 0-64H128a32 32 0 0 0 0 64z"></path></svg><!--]--></i></span><span role="button" aria-label="increase number" class="el-input-number__increase"><i class="el-icon" style=""><!--[--><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024"><path fill="currentColor" d="M480 480V128a32 32 0 0 1 64 0v352h352a32 32 0 1 1 0 64H544v352a32 32 0 1 1-64 0V544H128a32 32 0 0 1 0-64h352z"></path></svg><!--]--></i></span><div class="el-input" style=""><!-- input --><!--[--><!-- prepend slot --><!--v-if--><div class="el-input__wrapper"><!-- prefix slot --><!--v-if--><input class="el-input__inner" step="1" max="255" min="0" type="number" autocomplete="off" tabindex="0" style=""><!-- suffix slot --><!--v-if--></div><!-- append slot --><!--v-if--><!--]--></div></div><!--]--><!--]--><!--[--><!--]--><!--]--><!----></div><div class="k-schema-menu"><!--[--><button ariadisabled="false" type="button" class="el-button ellipsis el-tooltip__trigger el-tooltip__trigger" style=""><!--v-if--><span class=""><!--[--><svg class="k-icon" xmlns="http://www.w3.org/2000/svg" viewbox="0 0 448 512"><path fill="currentColor" d="M352 256C352 238.3 366.3 224 384 224C401.7 224 416 238.3 416 256C416 273.7 401.7 288 384 288C366.3 288 352 273.7 352 256zM192 256C192 238.3 206.3 224 224 224C241.7 224 256 238.3 256 256C256 273.7 241.7 288 224 288C206.3 288 192 273.7 192 256zM96 256C96 273.7 81.67 288 64 288C46.33 288 32 273.7 32 256C32 238.3 46.33 224 64 224C81.67 224 96 238.3 96 256z"></path></svg><!--]--></span></button><!--teleport start--><!--teleport end--><!--]--></div></div><!--[--><!--]--></div><!--[--><!--]--><!--]--><!--]--><!--]--><!--]--><!--[--><!----><!--[--><h2 class="k-schema-header">速度优化选项</h2><!--[--><!--[--><div class="k-schema-item"><div class="actions"></div><div class="k-schema-main"><div class="k-schema-left"><h3><!--[--><!--[--><!--[--><!--[--><span class="prefix"></span><span>mixed_precision</span><!--[--><!--]--><!--]--><!--]--><!--]--><!--]--></h3><!--[--><!--[--><!--[--><!--[--><div class="markdown"><p>混合精度</p>
</div><!--]--><!--]--><!--]--><!--]--></div><div class="k-schema-right"><!--[--><!--[--><!--[--><div class="el-select"><!--[--><div class="select-trigger el-tooltip__trigger el-tooltip__trigger"><!--v-if--><!-- fix: https://github.com/element-plus/element-plus/issues/11415 --><!--v-if--><div class="el-input el-input--suffix" style=""><!-- input --><!--[--><!-- prepend slot --><!--v-if--><div class="el-input__wrapper"><!-- prefix slot --><!--v-if--><input class="el-input__inner" type="text" readonly autocomplete="off" tabindex="0" placeholder="Select" style=""><!-- suffix slot --><span class="el-input__suffix"><span class="el-input__suffix-inner"><!--[--><!--[--><i class="el-icon el-select__caret el-select__icon" style=""><!--[--><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024"><path fill="currentColor" d="M831.872 340.864 512 652.672 192.128 340.864a30.592 30.592 0 0 0-42.752 0 29.12 29.12 0 0 0 0 41.6L489.664 714.24a32 32 0 0 0 44.672 0l340.288-331.712a29.12 29.12 0 0 0 0-41.728 30.592 30.592 0 0 0-42.752 0z"></path></svg><!--]--></i><!--v-if--><!--]--><!--v-if--><!--]--><!--v-if--><!--v-if--><!--v-if--><!--v-if--></span></span></div><!-- append slot --><!--v-if--><!--]--></div></div><!--teleport start--><!--teleport end--><!--]--></div><!--]--><!--]--><!--[--><!--]--><!--[--><!--]--><!--]--><!----></div><div class="k-schema-menu"><!--[--><button ariadisabled="false" type="button" class="el-button ellipsis el-tooltip__trigger el-tooltip__trigger" style=""><!--v-if--><span class=""><!--[--><svg class="k-icon" xmlns="http://www.w3.org/2000/svg" viewbox="0 0 448 512"><path fill="currentColor" d="M352 256C352 238.3 366.3 224 384 224C401.7 224 416 238.3 416 256C416 273.7 401.7 288 384 288C366.3 288 352 273.7 352 256zM192 256C192 238.3 206.3 224 224 224C241.7 224 256 238.3 256 256C256 273.7 241.7 288 224 288C206.3 288 192 273.7 192 256zM96 256C96 273.7 81.67 288 64 288C46.33 288 32 273.7 32 256C32 238.3 46.33 224 64 224C81.67 224 96 238.3 96 256z"></path></svg><!--]--></span></button><!--teleport start--><!--teleport end--><!--]--></div></div><!--[--><!--]--></div><!--[--><!--]--><!--]--><!--[--><div class="k-schema-item"><div class="actions"></div><div class="k-schema-main"><div class="k-schema-left"><h3><!--[--><!--[--><span class="prefix"></span><span>no_half_vae</span><!--[--><!--]--><!--]--><!--]--></h3><!--[--><!--[--><div class="markdown"><p>不使用半精度 VAE，当出现 NaN detected in latents 报错时使用</p>
</div><!--]--><!--]--></div><div class="k-schema-right"><!--[--><!--[--><!--]--><!--[--><!--[--><div class="el-switch nullable" style=""><input class="el-switch__input" type="checkbox" role="switch" aria-checked="false" aria-disabled="false" name true-value="true" false-value="false"><!--v-if--><span class="el-switch__core" style="width:;"><!--v-if--><div class="el-switch__action"><!--v-if--></div></span><!--v-if--></div><!--]--><!--]--><!--[--><!--]--><!--]--><!----></div><div class="k-schema-menu"><!--[--><button ariadisabled="false" type="button" class="el-button ellipsis el-tooltip__trigger el-tooltip__trigger" style=""><!--v-if--><span class=""><!--[--><svg class="k-icon" xmlns="http://www.w3.org/2000/svg" viewbox="0 0 448 512"><path fill="currentColor" d="M352 256C352 238.3 366.3 224 384 224C401.7 224 416 238.3 416 256C416 273.7 401.7 288 384 288C366.3 288 352 273.7 352 256zM192 256C192 238.3 206.3 224 224 224C241.7 224 256 238.3 256 256C256 273.7 241.7 288 224 288C206.3 288 192 273.7 192 256zM96 256C96 273.7 81.67 288 64 288C46.33 288 32 273.7 32 256C32 238.3 46.33 224 64 224C81.67 224 96 238.3 96 256z"></path></svg><!--]--></span></button><!--teleport start--><!--teleport end--><!--]--></div></div><!--[--><!--]--></div><!--[--><!--]--><!--]--><!--[--><div class="k-schema-item"><div class="actions"></div><div class="k-schema-main"><div class="k-schema-left"><h3><!--[--><!--[--><span class="prefix"></span><span>xformers</span><!--[--><!--]--><!--]--><!--]--></h3><!--[--><!--[--><div class="markdown"><p>启用 xformers</p>
</div><!--]--><!--]--></div><div class="k-schema-right"><!--[--><!--[--><!--]--><!--[--><!--[--><div class="el-switch is-checked" style=""><input class="el-switch__input" type="checkbox" role="switch" aria-checked="true" aria-disabled="false" name true-value="true" false-value="false"><!--v-if--><span class="el-switch__core" style="width:;"><!--v-if--><div class="el-switch__action"><!--v-if--></div></span><!--v-if--></div><!--]--><!--]--><!--[--><!--]--><!--]--><!----></div><div class="k-schema-menu"><!--[--><button ariadisabled="false" type="button" class="el-button ellipsis el-tooltip__trigger el-tooltip__trigger" style=""><!--v-if--><span class=""><!--[--><svg class="k-icon" xmlns="http://www.w3.org/2000/svg" viewbox="0 0 448 512"><path fill="currentColor" d="M352 256C352 238.3 366.3 224 384 224C401.7 224 416 238.3 416 256C416 273.7 401.7 288 384 288C366.3 288 352 273.7 352 256zM192 256C192 238.3 206.3 224 224 224C241.7 224 256 238.3 256 256C256 273.7 241.7 288 224 288C206.3 288 192 273.7 192 256zM96 256C96 273.7 81.67 288 64 288C46.33 288 32 273.7 32 256C32 238.3 46.33 224 64 224C81.67 224 96 238.3 96 256z"></path></svg><!--]--></span></button><!--teleport start--><!--teleport end--><!--]--></div></div><!--[--><!--]--></div><!--[--><!--]--><!--]--><!--[--><div class="k-schema-item"><div class="actions"></div><div class="k-schema-main"><div class="k-schema-left"><h3><!--[--><!--[--><span class="prefix"></span><span>cache_latents</span><!--[--><!--]--><!--]--><!--]--></h3><!--[--><!--[--><div class="markdown"><p>缓存图像 latent</p>
</div><!--]--><!--]--></div><div class="k-schema-right"><!--[--><!--[--><!--]--><!--[--><!--[--><div class="el-switch is-checked" style=""><input class="el-switch__input" type="checkbox" role="switch" aria-checked="true" aria-disabled="false" name true-value="true" false-value="false"><!--v-if--><span class="el-switch__core" style="width:;"><!--v-if--><div class="el-switch__action"><!--v-if--></div></span><!--v-if--></div><!--]--><!--]--><!--[--><!--]--><!--]--><!----></div><div class="k-schema-menu"><!--[--><button ariadisabled="false" type="button" class="el-button ellipsis el-tooltip__trigger el-tooltip__trigger" style=""><!--v-if--><span class=""><!--[--><svg class="k-icon" xmlns="http://www.w3.org/2000/svg" viewbox="0 0 448 512"><path fill="currentColor" d="M352 256C352 238.3 366.3 224 384 224C401.7 224 416 238.3 416 256C416 273.7 401.7 288 384 288C366.3 288 352 273.7 352 256zM192 256C192 238.3 206.3 224 224 224C241.7 224 256 238.3 256 256C256 273.7 241.7 288 224 288C206.3 288 192 273.7 192 256zM96 256C96 273.7 81.67 288 64 288C46.33 288 32 273.7 32 256C32 238.3 46.33 224 64 224C81.67 224 96 238.3 96 256z"></path></svg><!--]--></span></button><!--teleport start--><!--teleport end--><!--]--></div></div><!--[--><!--]--></div><!--[--><!--]--><!--]--><!--]--><!--]--><!--]--><!--]--><!--]--><!--]--></form><!--]--></div></div><!--[--><div class="el-scrollbar__bar is-horizontal" style="display:none;"><div class="el-scrollbar__thumb" style="width:0;transform:translateX(0%);"></div></div><div class="el-scrollbar__bar is-vertical" style="display:none;"><div class="el-scrollbar__thumb" style="height:0;transform:translateY(0%);"></div></div><!--]--></div></section><div class="right-container"><section class="theme-default-content"><div class="el-scrollbar"><div class="el-scrollbar__wrap el-scrollbar__wrap--hidden-default" style=""><div class="el-scrollbar__view" style=""><!--[--><main><div><h1 id="lora-训练-新手模式" tabindex="-1"><a class="header-anchor" href="#lora-训练-新手模式" aria-hidden="true">#</a> LoRA 训练 新手模式</h1><p>默认设置为你准备好了所有需要的参数，只需要你修改底模路径、训练集路径、训练轮数即可一键训练模型。</p></div></main><!--]--></div></div><!--[--><div class="el-scrollbar__bar is-horizontal" style="display:none;"><div class="el-scrollbar__thumb" style="width:0;transform:translateX(0%);"></div></div><div class="el-scrollbar__bar is-vertical" style="display:none;"><div class="el-scrollbar__thumb" style="height:0;transform:translateY(0%);"></div></div><!--]--></div></section><section><header>参数预览</header><main class="params-section"><code><div class="el-scrollbar"><div class="el-scrollbar__wrap el-scrollbar__wrap--hidden-default" style="max-height:60vh;"><div class="el-scrollbar__view" style=""><!--[--><!----><!---->pretrained_model_name_or_path = &quot;./sd-models/model.safetensors&quot;
train_data_dir = &quot;./train/aki&quot;
resolution = &quot;512,512&quot;
output_name = &quot;aki&quot;
output_dir = &quot;./output&quot;
save_every_n_epochs = 2
max_train_epochs = 10
train_batch_size = 1
unet_lr = 0.0001
text_encoder_lr = 0.00001
lr_scheduler = &quot;cosine_with_restarts&quot;
lr_warmup_steps = 0
lr_scheduler_num_cycles = 1
optimizer_type = &quot;AdamW8bit&quot;
network_dim = 32
network_alpha = 32
shuffle_caption = true
keep_tokens = 0
mixed_precision = &quot;fp16&quot;
xformers = true
cache_latents = true
<!--]--></div></div><!--[--><div class="el-scrollbar__bar is-horizontal" style="display:none;"><div class="el-scrollbar__thumb" style="width:0;transform:translateX(0%);"></div></div><div class="el-scrollbar__bar is-vertical" style="display:none;"><div class="el-scrollbar__thumb" style="height:0;transform:translateY(0%);"></div></div><!--]--></div></code></main></section><div class="el-row" style="margin-left:-5px;margin-right:-5px;margin:0px 20px 10px 20px;"><!--[--><div class="el-col el-col-8 is-guttered" style="padding-right:5px;padding-left:5px;padding-left:0;"><!--[--><button ariadisabled="false" type="button" class="el-button max-btn" style=""><!--v-if--><span class=""><!--[-->全部重置<!--]--></span></button><!--]--></div><div class="el-col el-col-8 is-guttered" style="padding-right:5px;padding-left:5px;"><!--[--><button ariadisabled="false" type="button" class="el-button max-btn" style=""><!--v-if--><span class=""><!--[-->保存参数<!--]--></span></button><!--]--></div><div class="el-col el-col-8 is-guttered" style="padding-right:5px;padding-left:5px;"><!--[--><button ariadisabled="false" type="button" class="el-button max-btn" style=""><!--v-if--><span class=""><!--[-->读取参数<!--]--></span></button><!--]--></div><!--]--></div><div class="el-row" style="margin-left:-5px;margin-right:-5px;margin:0px 20px 10px 20px;"><!--[--><div class="el-col el-col-12 is-guttered" style="padding-right:5px;padding-left:5px;padding-left:0;"><!--[--><button ariadisabled="false" type="button" class="el-button max-btn" style=""><!--v-if--><span class=""><!--[-->下载配置文件<!--]--></span></button><!--]--></div><div class="el-col el-col-12 is-guttered" style="padding-right:5px;padding-left:5px;"><!--[--><button ariadisabled="false" type="button" class="el-button max-btn" style=""><!--v-if--><span class=""><!--[-->导入配置文件<!--]--></span></button><!--]--></div><!--]--></div><div class="el-row" style="margin-left:-5px;margin-right:-5px;margin:0px 20px 10px 20px;"><!--[--><div class="el-col el-col-12 is-guttered" style="padding-right:5px;padding-left:5px;padding-left:0;"><!--[--><button ariadisabled="false" type="button" class="el-button el-button--primary is-plain max-btn color-btn" style=""><!--v-if--><span class=""><!--[-->开始训练<!--]--></span></button><!--]--></div><div class="el-col el-col-12 is-guttered" style="padding-right:5px;padding-left:5px;"><!--[--><button ariadisabled="false" type="button" class="el-button el-button--warning is-plain max-btn color-btn" style=""><!--v-if--><span class=""><!--[-->终止训练<!--]--></span></button><!--]--></div><!--]--></div><section id="test-output"><header>Output</header></section></div></div><!--]--></div><!----><!--]--></div>
    <script type="module" src="/assets/app.9273d30a.js" defer></script>
  </body>
</html>
