# EvoTune 使用指南

## 🚀 快速开始

### 方式一：SD WebUI集成使用（图像生成）

#### 1. 集成EvoTune到SD WebUI

首先需要将EvoTune集成到您的SD WebUI中：

```bash
# 1. 进入SD WebUI目录
cd h:\StableDiffusion_Gaijin\sd-webui-aki-v4.4

# 2. 将EvoTune文件复制到extensions目录
mkdir extensions\evotune
copy ..\EvoTune_UNetModel.py extensions\evotune\
copy ..\sd_webui_evotune_integration.py extensions\evotune\
copy ..\evotune_evaluation_metrics.py extensions\evotune\
```

#### 2. 修改SD WebUI启动脚本

创建一个新的启动脚本 `webui-evotune.bat`：

```batch
@echo off
set PYTHON=
set GIT=
set VENV_DIR=
set COMMANDLINE_ARGS=--xformers --api --listen

echo 启动EvoTune增强版SD WebUI...
python launch.py %COMMANDLINE_ARGS%
pause
```

#### 3. 启动SD WebUI

```bash
# 方法1：使用新的启动脚本
双击 webui-evotune.bat

# 方法2：使用原始启动器
双击 A启动器.exe
```

#### 4. 在WebUI中使用EvoTune

1. 打开浏览器访问 `http://localhost:7860`
2. 在设置中找到"UNet模型"选项
3. 选择"EvoTune (积水干扰优化)"
4. 在图生图模式下使用，效果最佳

### 方式二：LoRA训练使用（模型微调）

#### 1. 准备LoRA训练环境

```bash
# 进入LoRA训练目录
cd h:\StableDiffusion_Gaijin\lora-scripts-v1.8.5

# 启动LoRA训练界面
双击 A启动脚本.bat
```

#### 2. 配置EvoTune LoRA训练

创建EvoTune专用的训练配置文件 `evotune_config.toml`：

```toml
[model]
pretrained_model_name_or_path = "runwayml/stable-diffusion-v1-5"
v2 = false
v_parameterization = false

[dataset]
train_data_dir = "./train_data/water_oil_scenes"
resolution = 512
batch_size = 4
max_train_epochs = 100

[network]
network_module = "networks.lora"
network_dim = 4
network_alpha = 1.0
network_dropout = 0.1

[optimizer]
optimizer_type = "AdamW8bit"
learning_rate = 1e-4
lr_scheduler = "cosine_with_restarts"

[evotune]
use_evotune = true
b1 = 1.5
b2 = 1.2
s1 = 0.8
s2 = 0.5
use_agent_attention = false  # 暂时禁用直到修复
use_adaptive_fourier = true
```

#### 3. 准备训练数据

创建训练数据目录结构：
```
train_data/
└── water_oil_scenes/
    ├── images/
    │   ├── 001_water_oil.jpg
    │   ├── 002_water_oil.jpg
    │   └── ...
    └── captions/
        ├── 001_water_oil.txt
        ├── 002_water_oil.txt
        └── ...
```

标注文件示例（001_water_oil.txt）：
```
electrical equipment oil leak with water interference, realistic industrial scene, high quality
```

#### 4. 启动EvoTune LoRA训练

```bash
# 在LoRA训练界面中：
# 1. 选择"EvoTune专用训练"模式
# 2. 加载配置文件 evotune_config.toml
# 3. 设置输出目录
# 4. 点击"开始训练"
```

## 🛠️ 详细配置说明

### SD WebUI集成配置

#### 1. 创建EvoTune扩展

创建文件 `sd-webui-aki-v4.4\extensions\evotune\scripts\evotune_extension.py`：

```python
import gradio as gr
from modules import script_callbacks, shared
from modules.ui_components import FormRow
import sys
import os

# 添加EvoTune路径
evotune_path = os.path.join(os.path.dirname(__file__), "..", "..", "..", "..")
sys.path.append(evotune_path)

try:
    from sd_webui_evotune_integration import register_evotune_unet, EvoTuneSDUnet
    evotune_available = True
except ImportError:
    evotune_available = False

def on_ui_settings():
    if evotune_available:
        section = ('evotune', "EvoTune设置")
        
        shared.opts.add_option("evotune_enabled", shared.OptionInfo(
            True, "启用EvoTune模式", section=section))
        shared.opts.add_option("evotune_b1", shared.OptionInfo(
            1.5, "EvoTune参数b1", section=section))
        shared.opts.add_option("evotune_b2", shared.OptionInfo(
            1.2, "EvoTune参数b2", section=section))
        shared.opts.add_option("evotune_s1", shared.OptionInfo(
            0.8, "EvoTune参数s1", section=section))
        shared.opts.add_option("evotune_s2", shared.OptionInfo(
            0.5, "EvoTune参数s2", section=section))

def on_app_started(demo, app):
    if evotune_available:
        register_evotune_unet()
        print("EvoTune扩展已加载")

script_callbacks.on_ui_settings(on_ui_settings)
script_callbacks.on_app_started(on_app_started)
```

#### 2. 在WebUI中的使用步骤

1. **启动SD WebUI**
   ```bash
   cd h:\StableDiffusion_Gaijin\sd-webui-aki-v4.4
   双击 A启动器.exe
   ```

2. **配置EvoTune**
   - 打开浏览器访问 `http://localhost:7860`
   - 进入"设置" → "EvoTune设置"
   - 调整参数：b1=1.5, b2=1.2, s1=0.8, s2=0.5
   - 点击"应用设置"

3. **使用图生图模式**
   - 切换到"图生图"标签
   - 上传包含积水和油污的原始图像
   - 在提示词中添加：`oil leak, water interference, realistic, high quality`
   - 设置重绘幅度：0.3-0.7
   - 点击"生成"

### LoRA训练配置

#### 1. 创建EvoTune训练脚本

创建文件 `lora-scripts-v1.8.5\train_evotune.py`：

```python
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from evotune_lora_adapter import EvoTuneLoRAAdapter, create_evotune_lora_config
from EvoTune_UNetModel import EvoTune_UNetModel
import torch

def main():
    # 创建EvoTune模型
    model = EvoTune_UNetModel(
        model_channels=320,  # SD标准通道数
        resolution=64,       # SD标准分辨率
        use_agent_attention=False,  # 暂时禁用
        use_adaptive_fourier=True
    )
    
    # 应用LoRA适配器
    lora_config = create_evotune_lora_config(
        rank=4,
        alpha=1.0,
        dropout=0.1,
        learning_rate=1e-4
    )
    
    lora_adapter = EvoTuneLoRAAdapter(
        model=model,
        **lora_config['lora_config']
    )
    
    print("EvoTune LoRA训练环境已准备就绪")
    print(f"可训练参数: {sum(p.numel() for p in model.parameters() if p.requires_grad):,}")
    
    return model, lora_adapter

if __name__ == "__main__":
    main()
```

#### 2. 在LoRA界面中的使用步骤

1. **启动LoRA训练界面**
   ```bash
   cd h:\StableDiffusion_Gaijin\lora-scripts-v1.8.5
   双击 A启动脚本.bat
   ```

2. **配置训练参数**
   - 模型路径：选择SD 1.5基础模型
   - 训练数据：指向您的水油场景数据集
   - 网络类型：选择"LoRA"
   - 网络维度：4
   - 学习率：1e-4
   - 批次大小：4

3. **启动训练**
   - 检查所有配置
   - 点击"开始训练"
   - 监控训练进度和损失

## 📊 使用建议

### 最佳实践

1. **图像生成**
   - 使用图生图模式效果最佳
   - 重绘幅度建议0.3-0.7
   - 添加相关提示词增强效果

2. **LoRA训练**
   - 准备高质量的水油场景数据
   - 使用详细的文本描述
   - 训练50-100个epoch

3. **参数调优**
   - 水面反射强：增大b1和s1
   - 油污纹理复杂：增大b2和s2
   - 根据效果动态调整

### 常见问题

1. **Q: EvoTune选项没有出现在WebUI中？**
   A: 检查扩展是否正确安装，重启WebUI

2. **Q: LoRA训练时显存不足？**
   A: 减小batch_size，使用梯度累积

3. **Q: 生成效果不理想？**
   A: 调整EvoTune参数，使用更好的提示词

## 🔧 故障排除

### 常见错误及解决方案

1. **模块导入错误**
   ```bash
   # 确保所有依赖已安装
   pip install -i https://pypi.tuna.tsinghua.edu.cn/simple einops timm scipy opencv-python
   ```

2. **CUDA内存错误**
   ```bash
   # 在启动参数中添加
   --lowvram --medvram
   ```

3. **Agent Attention尺寸错误**
   ```python
   # 暂时禁用Agent Attention
   use_agent_attention=False
   ```

通过以上步骤，您就可以完整使用EvoTune系统进行积水干扰场景下的油污图像生成和模型训练了！
