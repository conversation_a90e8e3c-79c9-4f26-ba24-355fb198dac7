[model_arguments]
v2 = false
v_parameterization = false
pretrained_model_name_or_path = "./sd-models/model.ckpt"

[dataset_arguments]
train_data_dir = "./train/aki"
reg_data_dir = ""
resolution = "512,512"
prior_loss_weight = 1

[additional_network_arguments]
network_dim = 32
network_alpha = 16
network_train_unet_only = false
network_train_text_encoder_only = false
network_module = "networks.lora"
network_args = []

[optimizer_arguments]
unet_lr = 1e-4
text_encoder_lr = 1e-5

optimizer_type = "AdamW8bit"
lr_scheduler = "cosine_with_restarts"
lr_warmup_steps = 0
lr_restart_cycles = 1

[training_arguments]
train_batch_size = 1
noise_offset = 0.0
keep_tokens = 0
min_bucket_reso = 256
max_bucket_reso = 1024
caption_extension = ".txt"
max_token_length = 225
seed = 1337
xformers = true
lowram = false
max_train_epochs = 10
resolution = "512,512"
clip_skip = 2
mixed_precision = "fp16"

[sample_prompt_arguments]
sample_sampler = "euler_a"
sample_every_n_epochs = 5

[saving_arguments]
output_name = "output_name"
save_every_n_epochs = 1
save_state = false
save_model_as = "safetensors"
output_dir = "./output"
logging_dir = "./logs"
log_prefix = ""
save_precision = "fp16"

[others]
cache_latents = true
shuffle_caption = true
enable_bucket = true