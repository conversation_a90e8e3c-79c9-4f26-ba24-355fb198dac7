"""
EvoTune U-Net模型与Stable Diffusion WebUI集成接口
专门针对积水干扰场景下的油污生成优化

作者: AI Assistant
日期: 2025-01-11
"""

import torch
import torch.nn as nn
from typing import Optional, Dict, Any, Tuple
import os
import sys

# 添加当前目录到Python路径，以便导入EvoTune模型
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

from EvoTune_UNetModel import EvoTune_UNetModel


class EvoTuneSDUnet(nn.Module):
    """
    EvoTune U-Net的SD WebUI适配器
    实现与Stable Diffusion WebUI的无缝集成
    """
    def __init__(self, 
                 original_unet,
                 evotune_config: Optional[Dict[str, Any]] = None):
        super().__init__()
        
        self.original_unet = original_unet
        self.evotune_config = evotune_config or self.get_default_config()
        
        # 创建EvoTune模型
        self.evotune_model = EvoTune_UNetModel(
            **self.evotune_config
        )
        
        # 模型状态
        self.is_evotune_enabled = True
        self.water_oil_enhancement = True
        self.adaptive_timestep_tuning = True
        
        # 性能监控
        self.generation_stats = {
            'total_calls': 0,
            'evotune_calls': 0,
            'avg_processing_time': 0.0
        }

    @staticmethod
    def get_default_config():
        """
        获取默认的EvoTune配置
        """
        return {
            'b1': 1.5,
            'b2': 1.2,
            's1': 0.8,
            's2': 0.5,
            'model_channels': 320,  # 匹配SD默认通道数
            'num_classes': None,
            'resolution': 64,       # 匹配SD默认分辨率
            'use_agent_attention': True,
            'use_adaptive_fourier': True,
            'max_timesteps': 1000
        }

    def forward(self, x, timesteps=None, context=None, **kwargs):
        """
        前向传播，智能选择使用EvoTune或原始UNet
        """
        import time
        start_time = time.time()
        
        self.generation_stats['total_calls'] += 1
        
        # 检查是否应该使用EvoTune
        if self.should_use_evotune(x, timesteps, context, **kwargs):
            self.generation_stats['evotune_calls'] += 1
            result = self.evotune_forward(x, timesteps, context, **kwargs)
        else:
            result = self.original_unet(x, timesteps, context, **kwargs)
        
        # 更新性能统计
        processing_time = time.time() - start_time
        self.generation_stats['avg_processing_time'] = (
            self.generation_stats['avg_processing_time'] * 0.9 + 
            processing_time * 0.1
        )
        
        return result

    def should_use_evotune(self, x, timesteps=None, context=None, **kwargs):
        """
        判断是否应该使用EvoTune模型
        基于输入特征和上下文信息进行智能判断
        """
        if not self.is_evotune_enabled:
            return False
        
        # 检查是否为图生图模式（通常有更高的初始噪声）
        if x.std() > 0.8:  # 高噪声输入，可能是图生图
            return True
        
        # 检查上下文中是否包含水或油相关的提示词
        if context is not None and hasattr(context, 'shape'):
            # 这里可以添加更复杂的上下文分析逻辑
            return True
        
        # 检查时间步范围（EvoTune在特定时间步范围内效果更好）
        if timesteps is not None:
            timestep_mean = timesteps.float().mean()
            if 200 <= timestep_mean <= 800:  # 中等噪声范围
                return True
        
        return False

    def evotune_forward(self, x, timesteps=None, context=None, **kwargs):
        """
        使用EvoTune模型进行前向传播
        """
        try:
            # 适配输入格式
            adapted_input = self.adapt_input_format(x, timesteps, context, **kwargs)
            
            # EvoTune前向传播
            result = self.evotune_model(**adapted_input)
            
            # 适配输出格式
            result = self.adapt_output_format(result, x.shape)
            
            return result
            
        except Exception as e:
            print(f"EvoTune前向传播失败，回退到原始UNet: {e}")
            return self.original_unet(x, timesteps, context, **kwargs)

    def adapt_input_format(self, x, timesteps=None, context=None, **kwargs):
        """
        适配输入格式以匹配EvoTune模型
        """
        adapted = {
            'x': x,
            'timesteps': timesteps,
            'context': context
        }
        
        # 处理其他参数
        for key, value in kwargs.items():
            if key in ['y', 'class_labels']:
                adapted['y'] = value
            else:
                adapted[key] = value
        
        return adapted

    def adapt_output_format(self, result, target_shape):
        """
        适配输出格式以匹配SD WebUI期望的格式
        """
        if result.shape != target_shape:
            # 如果尺寸不匹配，进行插值调整
            result = torch.nn.functional.interpolate(
                result, 
                size=target_shape[-2:], 
                mode='bilinear', 
                align_corners=False
            )
        
        return result

    def enable_evotune(self, enable: bool = True):
        """
        启用或禁用EvoTune模式
        """
        self.is_evotune_enabled = enable
        print(f"EvoTune模式: {'启用' if enable else '禁用'}")

    def set_water_oil_enhancement(self, enable: bool = True):
        """
        设置水油区域增强
        """
        self.water_oil_enhancement = enable
        if hasattr(self.evotune_model, 'use_agent_attention'):
            self.evotune_model.use_agent_attention = enable

    def set_adaptive_timestep_tuning(self, enable: bool = True):
        """
        设置自适应时间步调谐
        """
        self.adaptive_timestep_tuning = enable
        if hasattr(self.evotune_model, 'use_adaptive_fourier'):
            self.evotune_model.use_adaptive_fourier = enable

    def update_evotune_params(self, **params):
        """
        更新EvoTune参数
        """
        if hasattr(self.evotune_model, 'set_adaptive_params'):
            self.evotune_model.set_adaptive_params(**params)

    def get_generation_stats(self):
        """
        获取生成统计信息
        """
        stats = self.generation_stats.copy()
        if stats['total_calls'] > 0:
            stats['evotune_usage_rate'] = stats['evotune_calls'] / stats['total_calls']
        else:
            stats['evotune_usage_rate'] = 0.0
        
        return stats

    def reset_stats(self):
        """
        重置统计信息
        """
        self.generation_stats = {
            'total_calls': 0,
            'evotune_calls': 0,
            'avg_processing_time': 0.0
        }


class EvoTuneSDUnetOption:
    """
    EvoTune U-Net选项类，用于SD WebUI集成
    """
    model_name = "evotune"
    label = "EvoTune (积水干扰优化)"

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or EvoTuneSDUnet.get_default_config()

    def create_unet(self):
        """
        创建EvoTune UNet实例
        """
        # 这里需要获取原始的SD UNet模型
        # 在实际集成时，需要从SD WebUI的模型管理器中获取
        try:
            from modules import shared
            original_unet = shared.sd_model.model.diffusion_model
        except ImportError:
            # 如果无法导入SD WebUI模块，创建一个占位符
            print("警告: 无法导入SD WebUI模块，使用占位符UNet")
            original_unet = None
        
        return EvoTuneSDUnet(original_unet, self.config)


# SD WebUI集成函数
def register_evotune_unet():
    """
    注册EvoTune UNet到SD WebUI
    """
    try:
        from modules import sd_unet
        
        # 创建EvoTune选项
        evotune_option = EvoTuneSDUnetOption()
        
        # 注册到SD WebUI
        if hasattr(sd_unet, 'unet_options'):
            sd_unet.unet_options.append(evotune_option)
        
        print("EvoTune UNet已成功注册到SD WebUI")
        return True
        
    except ImportError as e:
        print(f"SD WebUI模块导入失败: {e}")
        return False
    except Exception as e:
        print(f"EvoTune UNet注册失败: {e}")
        return False


# 测试函数
def test_evotune_integration():
    """
    测试EvoTune集成
    """
    print("=== EvoTune SD WebUI集成测试 ===")
    
    # 创建测试用的EvoTune UNet
    config = EvoTuneSDUnet.get_default_config()
    evotune_unet = EvoTuneSDUnet(None, config)
    
    # 测试输入
    test_input = torch.randn(1, 4, 64, 64)  # SD潜在空间格式
    test_timesteps = torch.tensor([500])
    test_context = torch.randn(1, 77, 768)  # CLIP文本嵌入格式
    
    print(f"测试输入尺寸: {test_input.shape}")
    print(f"测试时间步: {test_timesteps}")
    print(f"测试上下文尺寸: {test_context.shape}")
    
    # 测试前向传播
    try:
        with torch.no_grad():
            output = evotune_unet(test_input, test_timesteps, test_context)
        print(f"输出尺寸: {output.shape}")
        print("前向传播测试成功")
    except Exception as e:
        print(f"前向传播测试失败: {e}")
    
    # 测试统计信息
    stats = evotune_unet.get_generation_stats()
    print(f"生成统计: {stats}")
    
    print("=== 集成测试完成 ===")


if __name__ == "__main__":
    test_evotune_integration()
