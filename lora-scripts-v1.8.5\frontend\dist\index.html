<!DOCTYPE html>
<html lang="en-US">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <meta name="generator" content="VuePress 2.0.0-beta.49">
    <style>
      :root {
        --c-bg: #fff;
      }
      html.dark {
        --c-bg: #22272e;
      }
      html, body {
        background-color: var(--c-bg);
      }
    </style>
    <script>
      const userMode = localStorage.getItem('vuepress-color-scheme');
			const systemDarkMode = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;
			if (userMode === 'dark' || (userMode !== 'light' && systemDarkMode)) {
				document.documentElement.classList.toggle('dark', true);
			}
    </script>
    <title>SD-Trainer | SD 训练 UI</title><meta name="description" content="">
    <link rel="modulepreload" href="/assets/app.9273d30a.js"><link rel="modulepreload" href="/assets/index.html.84bf285d.js"><link rel="modulepreload" href="/assets/index.html.9d7cc666.js"><link rel="prefetch" href="/assets/tageditor.html.66da263e.js"><link rel="prefetch" href="/assets/tagger.html.2eb31fcb.js"><link rel="prefetch" href="/assets/task.html.4e4c8633.js"><link rel="prefetch" href="/assets/tensorboard.html.4a2799a9.js"><link rel="prefetch" href="/assets/index.html.18cf2953.js"><link rel="prefetch" href="/assets/basic.html.48955584.js"><link rel="prefetch" href="/assets/index.html.b97ec799.js"><link rel="prefetch" href="/assets/master.html.54eb6415.js"><link rel="prefetch" href="/assets/params.html.c8cc13ef.js"><link rel="prefetch" href="/assets/tools.html.c0a4659a.js"><link rel="prefetch" href="/assets/about.html.5b0c0de9.js"><link rel="prefetch" href="/assets/settings.html.06993f96.js"><link rel="prefetch" href="/assets/404.html.686caba0.js"><link rel="prefetch" href="/assets/tageditor.html.66fa7b72.js"><link rel="prefetch" href="/assets/tagger.html.f698ca26.js"><link rel="prefetch" href="/assets/task.html.2f4311fb.js"><link rel="prefetch" href="/assets/tensorboard.html.e5ada3f5.js"><link rel="prefetch" href="/assets/index.html.4696b6e4.js"><link rel="prefetch" href="/assets/basic.html.655a3322.js"><link rel="prefetch" href="/assets/index.html.db1c0354.js"><link rel="prefetch" href="/assets/master.html.94401419.js"><link rel="prefetch" href="/assets/params.html.c90a6b4c.js"><link rel="prefetch" href="/assets/tools.html.1d9df334.js"><link rel="prefetch" href="/assets/about.html.2343ff24.js"><link rel="prefetch" href="/assets/settings.html.0626d062.js"><link rel="prefetch" href="/assets/404.html.cbf82dee.js"><link rel="prefetch" href="/assets/404.310165f5.js"><link rel="prefetch" href="/assets/layout.c140630d.js">
    <link rel="stylesheet" href="/assets/style.04eab9dc.css">
  </head>
  <body>
    <div id="app"><!--[--><div class="theme-container no-navbar"><!--[--><!----><!--]--><div class="sidebar-mask"></div><!--[--><aside class="sidebar" data-v-db8971c4><div class="el-scrollbar" data-v-db8971c4><div class="el-scrollbar__wrap el-scrollbar__wrap--hidden-default" style=""><div class="el-scrollbar__view" style=""><!--[--><div class="sidebar-container" data-v-db8971c4><!----><ul class="sidebar-items" data-v-db8971c4><!--[--><li><a aria-current="page" href="/" class="router-link-active router-link-exact-active sidebar-item sidebar-heading active" aria-label="SD-Trainer"><!--[--><!--]--> SD-Trainer <!--[--><!--]--></a><!----></li><li><a href="/lora/index.md" class="sidebar-item sidebar-heading" aria-label="LoRA训练"><!--[--><!--]--> LoRA训练 <!--[--><!--]--></a><ul style="display:none;" class="sidebar-item-children"><!--[--><li><a href="/lora/basic.md" class="sidebar-item" aria-label="新手"><!--[--><!--]--> 新手 <!--[--><!--]--></a><!----></li><li><a href="/lora/master.md" class="sidebar-item" aria-label="专家"><!--[--><!--]--> 专家 <!--[--><!--]--></a><!----></li><li><a href="/lora/tools.md" class="sidebar-item" aria-label="工具"><!--[--><!--]--> 工具 <!--[--><!--]--></a><!----></li><li><a href="/lora/params.md" class="sidebar-item" aria-label="参数详解"><!--[--><!--]--> 参数详解 <!--[--><!--]--></a><!----></li><!--]--></ul></li><li><a href="/dreambooth/index.md" class="sidebar-item sidebar-heading" aria-label="Dreambooth 训练"><!--[--><!--]--> Dreambooth 训练 <!--[--><!--]--></a><!----></li><li><a href="/tensorboard.md" class="sidebar-item sidebar-heading" aria-label="Tensorboard"><!--[--><!--]--> Tensorboard <!--[--><!--]--></a><!----></li><li><a href="/tagger.md" class="sidebar-item sidebar-heading" aria-label="WD 1.4 标签器"><!--[--><!--]--> WD 1.4 标签器 <!--[--><!--]--></a><!----></li><li><a href="/tageditor.md" class="sidebar-item sidebar-heading" aria-label="标签编辑器"><!--[--><!--]--> 标签编辑器 <!--[--><!--]--></a><!----></li><li><p tabindex="0" class="sidebar-item sidebar-heading">其他 <!----></p><ul style="display:none;" class="sidebar-item-children"><!--[--><li><a href="/other/settings.md" class="sidebar-item" aria-label="UI 设置"><!--[--><!--]--> UI 设置 <!--[--><!--]--></a><!----></li><li><a href="/other/about.md" class="sidebar-item" aria-label="关于"><!--[--><!--]--> 关于 <!--[--><!--]--></a><!----></li><!--]--></ul></li><!--]--></ul><ul class="sidebar-bottom" data-v-db8971c4><li class="sidebar-item sidebar-heading appearance" data-v-db8971c4> Github <a class="icon" href="https://github.com/Akegarasu/lora-scripts" target="_blank" aria-label="GitHub" data-v-db8971c4><svg xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="xMidYMid meet" viewBox="0 0 24 24" data-v-db8971c4><path d="M12 2C6.475 2 2 6.475 2 12a9.994 9.994 0 0 0 6.838 9.488c.5.087.687-.213.687-.476c0-.237-.013-1.024-.013-1.862c-2.512.463-3.162-.612-3.362-1.175c-.113-.288-.6-1.175-1.025-1.413c-.35-.187-.85-.65-.013-.662c.788-.013 1.35.725 1.538 1.025c.9 1.512 2.338 1.087 2.912.825c.088-.65.35-1.087.638-1.337c-2.225-.25-4.55-1.113-4.55-4.938c0-1.088.387-1.987 1.025-2.688c-.1-.25-.45-1.275.1-2.65c0 0 .837-.262 2.75 1.026a9.28 9.28 0 0 1 2.5-.338c.85 0 1.7.112 2.5.337c1.912-1.3 2.75-1.024 2.75-1.024c.55 1.375.2 2.4.1 2.65c.637.7 1.025 1.587 1.025 2.687c0 3.838-2.337 4.688-4.562 4.938c.362.312.675.912.675 1.85c0 1.337-.013 2.412-.013 2.75c0 .262.188.574.688.474A10.016 10.016 0 0 0 22 12c0-5.525-4.475-10-10-10z" fill="currentColor" data-v-db8971c4></path></svg></a></li><li class="sidebar-item sidebar-heading appearance" data-v-db8971c4> Language <button class="toggle-color-mode-button" data-v-db8971c4><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" data-v-db8971c4><path d=" M12.87 15.07l-2.54-2.51.03-.03c1.74-1.94 2.98-4.17 3.71-6.53H17V4h-7V2H8v2H1v1.99h11.17C11.5 7.92 10.44 9.75 9 11.35 8.07 10.32 7.3 9.19 6.69 8h-2c.73 1.63 1.73 3.17 2.98 4.56l-5.09 5.02L4 19l5-5 3.11 3.11.76-2.04zM18.5 10h-2L12 22h2l1.12-3h4.75L21 22h2l-4.5-12zm-2.62 7l1.62-4.33L19.12 17h-3.24z " fill="currentColor" data-v-db8971c4></path></svg></button></li><li class="sidebar-item sidebar-heading appearance" data-v-db8971c4> 灯泡 <button class="toggle-color-mode-button" title="toggle color mode" data-v-db8971c4><svg style="" class="icon" focusable="false" viewBox="0 0 32 32"><path d="M16 12.005a4 4 0 1 1-4 4a4.005 4.005 0 0 1 4-4m0-2a6 6 0 1 0 6 6a6 6 0 0 0-6-6z" fill="currentColor"></path><path d="M5.394 6.813l1.414-1.415l3.506 3.506L8.9 10.318z" fill="currentColor"></path><path d="M2 15.005h5v2H2z" fill="currentColor"></path><path d="M5.394 25.197L8.9 21.691l1.414 1.415l-3.506 3.505z" fill="currentColor"></path><path d="M15 25.005h2v5h-2z" fill="currentColor"></path><path d="M21.687 23.106l1.414-1.415l3.506 3.506l-1.414 1.414z" fill="currentColor"></path><path d="M25 15.005h5v2h-5z" fill="currentColor"></path><path d="M21.687 8.904l3.506-3.506l1.414 1.415l-3.506 3.505z" fill="currentColor"></path><path d="M15 2.005h2v5h-2z" fill="currentColor"></path></svg><svg style="display:none;" class="icon" focusable="false" viewBox="0 0 32 32"><path d="M13.502 5.414a15.075 15.075 0 0 0 11.594 18.194a11.113 11.113 0 0 1-7.975 3.39c-.138 0-.278.005-.418 0a11.094 11.094 0 0 1-3.2-21.584M14.98 3a1.002 1.002 0 0 0-.175.016a13.096 13.096 0 0 0 1.825 25.981c.164.006.328 0 .49 0a13.072 13.072 0 0 0 10.703-5.555a1.01 1.01 0 0 0-.783-1.565A13.08 13.08 0 0 1 15.89 4.38A1.015 1.015 0 0 0 14.98 3z" fill="currentColor"></path></svg></button></li></ul></div><!--]--></div></div><!--[--><div class="el-scrollbar__bar is-horizontal" style="display:none;"><div class="el-scrollbar__thumb" style="width:0;transform:translateX(0%);"></div></div><div class="el-scrollbar__bar is-vertical" style="display:none;"><div class="el-scrollbar__thumb" style="height:0;transform:translateY(0%);"></div></div><!--]--></div></aside><!--]--><!--[--><main class="page"><!--[--><!--]--><div class="theme-default-content"><!--[--><!--]--><div><div align="center"><h1 id="sd-trainer" tabindex="-1"><a class="header-anchor" href="#sd-trainer" aria-hidden="true">#</a> SD-Trainer</h1><img src="/assets/icon.65fd68ba.webp" width="200" height="200" alt="SD-Trainer" style="margin:20px;border-radius:25px;"><p>Stable Diffusion 训练 UI v1.8.5.fix1</p><p>Author <a href="https://space.bilibili.com/12566101" target="_blank" rel="noopener noreferrer">秋葉 aaaki<span><svg class="external-link-icon" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path><polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg><span class="external-link-icon-sr-only">open in new window</span></span></a> Github <a href="https://github.com/Akegarasu/lora-scripts" target="_blank" rel="noopener noreferrer">lora-scripts<span><svg class="external-link-icon" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path><polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg><span class="external-link-icon-sr-only">open in new window</span></span></a></p></div><p align="center"><a href="https://github.com/Akegarasu/lora-scripts" style="margin:2px;"><img alt="GitHub Repo stars" src="https://img.shields.io/github/stars/Akegarasu/lora-scripts"></a><a href="https://github.com/Akegarasu/lora-scripts" style="margin:2px;"><img alt="GitHub forks" src="https://img.shields.io/github/forks/Akegarasu/lora-scripts"></a><a href="https://raw.githubusercontent.com/Akegarasu/lora-scripts/master/LICENSE" style="margin:2px;"><img src="https://img.shields.io/github/license/Akegarasu/lora-scripts" alt="license"></a><a href="https://github.com/Akegarasu/lora-scripts/releases" style="margin:2px;"><img src="https://img.shields.io/github/v/release/Akegarasu/lora-scripts?color=blueviolet&amp;include_prereleases" alt="release"></a></p><h3 id="更新日志" tabindex="-1"><a class="header-anchor" href="#更新日志" aria-hidden="true">#</a> 更新日志</h3><h4 id="v1-8-5-fix1" tabindex="-1"><a class="header-anchor" href="#v1-8-5-fix1" aria-hidden="true">#</a> v1.8.5.fix1</h4><ul><li>修复了一些小问题</li><li>添加更多训练参数</li><li>重构部分代码</li><li>更新 Tagger 模型列表</li><li>专家模式中自定义参数改为 Toml 格式</li><li>更新多卡相关参数</li><li>修复多卡使用时无法超过 2 张显卡的问题</li></ul><h4 id="v1-8-4" tabindex="-1"><a class="header-anchor" href="#v1-8-4" aria-hidden="true">#</a> v1.8.4</h4><ul><li>添加一些参数</li><li>重构部分代码</li></ul><h4 id="v1-8-3-fix3" tabindex="-1"><a class="header-anchor" href="#v1-8-3-fix3" aria-hidden="true">#</a> v1.8.3.fix3</h4><ul><li>重构部分代码</li><li>修复一系列小问题</li><li>只有一张显卡时，隐藏选择显卡的选项</li><li>WD 14 Tagger 添加了更多模型选项</li><li>解除部分参数限制（希望你在调整参数时最好真的知道你在干什么）</li><li>修复保存 toml 时，科学计数法的问题</li><li>修复 标签编辑器 无法加载过多图片的问题（约 1000 张以上）</li><li>修复 SDXL finetune 训练时，text encoder 无法正常训练的问题</li><li>修复 <code>gpu_ids</code> 参数无法防呆的问题。现在如使用默认显卡则不会调整 <code>gpu_ids</code> 参数</li></ul><h4 id="v1-8-2-fix1" tabindex="-1"><a class="header-anchor" href="#v1-8-2-fix1" aria-hidden="true">#</a> v1.8.2.fix1</h4><ul><li>添加更多参数</li><li>移除 <code>multi_gpu</code> 参数，更改为 <code>gpu_ids</code> 可以自定义使用的 GPU</li><li>修复无法获取 GPU 时，参数无法正常显示的问题</li></ul><h4 id="v1-8-1" tabindex="-1"><a class="header-anchor" href="#v1-8-1" aria-hidden="true">#</a> v1.8.1</h4><ul><li>添加更多参数</li><li>解除部分不必要的参数限制</li><li>WD 14 Tagger 增加更多参数</li><li>国际化初步支持：Localization support</li></ul><h4 id="v1-8-0-fix1" tabindex="-1"><a class="header-anchor" href="#v1-8-0-fix1" aria-hidden="true">#</a> v1.8.0.fix1</h4><ul><li>新增：支持 tag 编辑器</li><li>修复部分二级开关参数禁用后有时仍然残留的问题</li></ul><h3 id="更多历史更新日志" tabindex="-1"><a class="header-anchor" href="#更多历史更新日志" aria-hidden="true">#</a> 更多历史更新日志</h3><div class="language-markdown ext-md"><pre class="shiki" style="background-color:#282c34;"><code><span class="line"><span style="color:#E06C75;">#### v1.7.6</span></span>
<span class="line"></span>
<span class="line"><span style="color:#E5C07B;">-</span><span style="color:#ABB2BF;"> 添加更多参数</span></span>
<span class="line"><span style="color:#E5C07B;">-</span><span style="color:#ABB2BF;"> 重构 </span><span style="color:#E5C07B;">`</span><span style="color:#98C379;">tensorboard</span><span style="color:#E5C07B;">`</span><span style="color:#ABB2BF;"> 加载方式，解决云端加载问题</span></span>
<span class="line"></span>
<span class="line"><span style="color:#E06C75;">#### v1.7.5</span></span>
<span class="line"></span>
<span class="line"><span style="color:#E5C07B;">-</span><span style="color:#ABB2BF;"> 更新依赖</span></span>
<span class="line"><span style="color:#E5C07B;">-</span><span style="color:#ABB2BF;"> 调整部分参数限制</span></span>
<span class="line"><span style="color:#E5C07B;">-</span><span style="color:#ABB2BF;"> Dreambooth 训练支持单独设置文本编码器学习率</span></span>
<span class="line"></span>
<span class="line"><span style="color:#E06C75;">#### v1.7.4</span></span>
<span class="line"></span>
<span class="line"><span style="color:#E5C07B;">-</span><span style="color:#ABB2BF;"> 修复潜在的 bug</span></span>
<span class="line"><span style="color:#E5C07B;">-</span><span style="color:#ABB2BF;"> 调整报错信息，减少不相关的报错输出</span></span>
<span class="line"></span>
<span class="line"><span style="color:#E06C75;">#### v1.7.3</span></span>
<span class="line"></span>
<span class="line"><span style="color:#E5C07B;">-</span><span style="color:#ABB2BF;"> 优化了参数提醒</span></span>
<span class="line"><span style="color:#E5C07B;">-</span><span style="color:#ABB2BF;"> 专家模式新增自定义参数功能，将会直接强制覆盖界面生成的参数（危险）</span></span>
<span class="line"><span style="color:#E5C07B;">-</span><span style="color:#ABB2BF;"> 更新 </span><span style="color:#E5C07B;">`</span><span style="color:#98C379;">sd-scripts</span><span style="color:#E5C07B;">`</span><span style="color:#ABB2BF;">，支持 OFT 训练（仅 SDXL）。</span><span style="color:#E5C07B;">`</span><span style="color:#98C379;">network_module</span><span style="color:#E5C07B;">`</span><span style="color:#ABB2BF;"> 内选择 </span><span style="color:#E5C07B;">`</span><span style="color:#98C379;">networks.oft</span><span style="color:#E5C07B;">`</span></span>
<span class="line"></span>
<span class="line"><span style="color:#E06C75;">#### v1.7.2</span></span>
<span class="line"></span>
<span class="line"><span style="color:#E5C07B;">-</span><span style="color:#ABB2BF;"> 修复已知问题</span></span>
<span class="line"><span style="color:#E5C07B;">-</span><span style="color:#ABB2BF;"> 添加一些数据增强系列的参数</span></span>
<span class="line"></span>
<span class="line"><span style="color:#E06C75;">#### v1.7.1</span></span>
<span class="line"></span>
<span class="line"><span style="color:#E5C07B;">-</span><span style="color:#ABB2BF;"> 添加一些参数</span></span>
<span class="line"><span style="color:#E5C07B;">-</span><span style="color:#ABB2BF;"> 防呆：添加错误使用参数时候的提醒</span></span>
<span class="line"><span style="color:#E5C07B;">-</span><span style="color:#ABB2BF;"> 解除在使用 </span><span style="color:#E5C07B;">`</span><span style="color:#98C379;">DAdaptation</span><span style="color:#E5C07B;">`</span><span style="color:#ABB2BF;"> 系列优化器时，</span><span style="color:#E5C07B;">`</span><span style="color:#98C379;">lr_scheduler</span><span style="color:#E5C07B;">`</span><span style="color:#ABB2BF;"> 被自动锁定为常量的限制，更改为提醒。</span></span>
<span class="line"></span>
<span class="line"><span style="color:#E06C75;">#### v1.7.0</span></span>
<span class="line"></span>
<span class="line"><span style="color:#E5C07B;">-</span><span style="color:#ABB2BF;"> 添加文件选择窗口，现在可以不需要手动填写路径了</span></span>
<span class="line"><span style="color:#E5C07B;">-</span><span style="color:#ABB2BF;"> （实验性）Dreambooth 训练支持</span></span>
<span class="line"><span style="color:#E5C07B;">-</span><span style="color:#ABB2BF;"> 默认模型名修改为 </span><span style="color:#E5C07B;">`</span><span style="color:#98C379;">model.safetensors</span><span style="color:#E5C07B;">`</span></span>
<span class="line"><span style="color:#E5C07B;">-</span><span style="color:#ABB2BF;"> 优化了按钮样式</span></span>
<span class="line"><span style="color:#E5C07B;">-</span><span style="color:#ABB2BF;"> 修复差异炼丹无法使用的 Bug</span></span>
<span class="line"></span>
<span class="line"><span style="color:#E06C75;">#### v1.6.2</span></span>
<span class="line"></span>
<span class="line"><span style="color:#E5C07B;">-</span><span style="color:#ABB2BF;"> 支持直接导入 </span><span style="color:#E5C07B;">`</span><span style="color:#98C379;">toml</span><span style="color:#E5C07B;">`</span><span style="color:#ABB2BF;"> 文件</span></span>
<span class="line"><span style="color:#E5C07B;">-</span><span style="color:#ABB2BF;"> 支持停止当前训练任务</span></span>
<span class="line"></span>
<span class="line"><span style="color:#E06C75;">#### v1.6.1</span></span>
<span class="line"></span>
<span class="line"><span style="color:#E5C07B;">-</span><span style="color:#ABB2BF;"> 优化界面样式</span></span>
<span class="line"><span style="color:#E5C07B;">-</span><span style="color:#ABB2BF;"> 现在保存的参数支持导入导出了！</span></span>
<span class="line"></span>
<span class="line"><span style="color:#E06C75;">#### v1.6.0</span></span>
<span class="line"></span>
<span class="line"><span style="color:#E5C07B;">-</span><span style="color:#ABB2BF;"> </span><span style="color:#E5C07B;">`</span><span style="color:#98C379;">toml</span><span style="color:#E5C07B;">`</span><span style="color:#ABB2BF;"> 文件夹更名为 </span><span style="color:#E5C07B;">`</span><span style="color:#98C379;">config</span><span style="color:#E5C07B;">`</span><span style="color:#ABB2BF;"> 文件夹</span></span>
<span class="line"><span style="color:#E5C07B;">-</span><span style="color:#ABB2BF;"> 防呆：启动时自动校验 Torch 是否可用 CUDA</span></span>
<span class="line"><span style="color:#E5C07B;">-</span><span style="color:#ABB2BF;"> 防呆：自动校验数据集目录是否存在，并且可自动尝试修复数据集路径。防不看教程的傻子又跑过来说你这个怎么用不了啊</span></span>
<span class="line"></span>
<span class="line"><span style="color:#E06C75;">#### v1.5.3</span></span>
<span class="line"></span>
<span class="line"><span style="color:#E5C07B;">-</span><span style="color:#ABB2BF;"> 修复部分值为空或 0 的情况下，仍然归类为启用的问题</span></span>
<span class="line"><span style="color:#E5C07B;">-</span><span style="color:#ABB2BF;"> 更改 tensorboard 的 url 设置，目前可以设置为任意 url</span></span>
<span class="line"></span>
<span class="line"><span style="color:#E06C75;">#### v1.5.2</span></span>
<span class="line"></span>
<span class="line"><span style="color:#E5C07B;">-</span><span style="color:#ABB2BF;"> 自动检测依赖版本，修复 bitsandbytes 的 windows 依赖</span></span>
<span class="line"></span>
<span class="line"><span style="color:#E06C75;">#### v1.5.0</span></span>
<span class="line"></span>
<span class="line"><span style="color:#E5C07B;">-</span><span style="color:#ABB2BF;"> 支持 SDXL 训练</span></span>
<span class="line"><span style="color:#E5C07B;">-</span><span style="color:#ABB2BF;"> 新增：外挂 VAE 文件覆盖</span></span>
<span class="line"><span style="color:#E5C07B;">-</span><span style="color:#ABB2BF;"> 新增：</span><span style="color:#E5C07B;">`</span><span style="color:#98C379;">full_bf16</span><span style="color:#E5C07B;">`</span><span style="color:#ABB2BF;">、</span><span style="color:#E5C07B;">`</span><span style="color:#98C379;">full_fp16</span><span style="color:#E5C07B;">`</span><span style="color:#ABB2BF;">、</span><span style="color:#E5C07B;">`</span><span style="color:#98C379;">cache_text_encoder_outputs</span><span style="color:#E5C07B;">`</span><span style="color:#ABB2BF;">、</span><span style="color:#E5C07B;">`</span><span style="color:#98C379;">bucket_reso_steps</span><span style="color:#E5C07B;">`</span><span style="color:#ABB2BF;"> 参数</span></span>
<span class="line"></span>
<span class="line"><span style="color:#E06C75;">#### v1.4.2</span></span>
<span class="line"></span>
<span class="line"><span style="color:#E5C07B;">-</span><span style="color:#ABB2BF;"> 新增：优化器 </span><span style="color:#E5C07B;">`</span><span style="color:#98C379;">Lion8bit</span><span style="color:#E5C07B;">`</span><span style="color:#ABB2BF;">、</span><span style="color:#E5C07B;">`</span><span style="color:#98C379;">PagedLion8bit</span><span style="color:#E5C07B;">`</span><span style="color:#ABB2BF;">、</span><span style="color:#E5C07B;">`</span><span style="color:#98C379;">PagedAdamW8bit</span><span style="color:#E5C07B;">`</span><span style="color:#ABB2BF;"> 参数，使用时需自行安装 </span><span style="color:#E5C07B;">`</span><span style="color:#98C379;">bitsandbytes</span><span style="color:#E5C07B;">`</span><span style="color:#ABB2BF;"> 0.39.0 以上的版本</span></span>
<span class="line"><span style="color:#E5C07B;">-</span><span style="color:#ABB2BF;"> 新增：</span><span style="color:#E5C07B;">`</span><span style="color:#98C379;">scale_weight_norms</span><span style="color:#E5C07B;">`</span><span style="color:#ABB2BF;"> 参数</span></span>
<span class="line"><span style="color:#E5C07B;">-</span><span style="color:#ABB2BF;"> 更改了 </span><span style="color:#E5C07B;">`</span><span style="color:#98C379;">lr_warmup</span><span style="color:#E5C07B;">`</span><span style="color:#ABB2BF;"> 参数的可设置时机</span></span>
<span class="line"><span style="color:#E5C07B;">-</span><span style="color:#ABB2BF;"> 修改部分参数的描述</span></span>
<span class="line"></span>
<span class="line"><span style="color:#E06C75;">#### v1.4.1</span></span>
<span class="line"></span>
<span class="line"><span style="color:#E5C07B;">-</span><span style="color:#ABB2BF;"> 新增：神童优化器相关参数</span></span>
<span class="line"></span>
<span class="line"><span style="color:#E06C75;">#### v1.4.0</span></span>
<span class="line"></span>
<span class="line"><span style="color:#E5C07B;">-</span><span style="color:#ABB2BF;"> 新增：自动保存 &amp; 读取历史参数</span></span>
<span class="line"><span style="color:#E5C07B;">-</span><span style="color:#ABB2BF;"> 新增：LoRA 相关工具 SVD 脚本 &amp; dyLoRA 脚本</span></span>
<span class="line"><span style="color:#E5C07B;">-</span><span style="color:#ABB2BF;"> 保存参数时将会自动用输出的模型名称作为保存名称</span></span>
<span class="line"><span style="color:#E5C07B;">-</span><span style="color:#ABB2BF;"> 优化参数</span></span>
<span class="line"></span>
<span class="line"><span style="color:#E06C75;">#### v1.3.2</span></span>
<span class="line"></span>
<span class="line"><span style="color:#E5C07B;">-</span><span style="color:#ABB2BF;"> 样式优化</span></span>
<span class="line"><span style="color:#E5C07B;">-</span><span style="color:#ABB2BF;"> 添加忘记的 lycoris.kohya 的 dylora 选项</span></span>
<span class="line"></span>
<span class="line"><span style="color:#E06C75;">#### v1.3.1</span></span>
<span class="line"></span>
<span class="line"><span style="color:#E5C07B;">-</span><span style="color:#ABB2BF;"> 修复了 由于 “修复了 </span><span style="color:#E5C07B;">`</span><span style="color:#98C379;">dropout</span><span style="color:#E5C07B;">`</span><span style="color:#ABB2BF;"> 参数的 bug” 产生的 bug</span></span>
<span class="line"><span style="color:#E5C07B;">-</span><span style="color:#ABB2BF;"> 其他细微调整</span></span>
<span class="line"></span>
<span class="line"><span style="color:#E06C75;">#### v1.3.0</span></span>
<span class="line"></span>
<span class="line"><span style="color:#E5C07B;">-</span><span style="color:#ABB2BF;"> 更新并修复了 </span><span style="color:#E5C07B;">`</span><span style="color:#98C379;">dropout</span><span style="color:#E5C07B;">`</span><span style="color:#ABB2BF;"> 参数的 bug</span></span>
<span class="line"><span style="color:#E5C07B;">-</span><span style="color:#ABB2BF;"> 新增功能：专家模式可以自定义 </span><span style="color:#E5C07B;">`</span><span style="color:#98C379;">network_args</span><span style="color:#E5C07B;">`</span><span style="color:#ABB2BF;"> 与 </span><span style="color:#E5C07B;">`</span><span style="color:#98C379;">optimizer_args</span><span style="color:#E5C07B;">`</span><span style="color:#ABB2BF;"> 参数。无需等待 UI 加入新参数，自定义的权限是你的！</span></span>
<span class="line"></span>
<span class="line"><span style="color:#E06C75;">#### v1.2.1</span></span>
<span class="line"></span>
<span class="line"><span style="color:#E5C07B;">-</span><span style="color:#ABB2BF;"> 更改并且修复了 DAdaptation 的一些参数</span></span>
<span class="line"></span>
<span class="line"><span style="color:#E06C75;">#### v1.2.0</span></span>
<span class="line"></span>
<span class="line"><span style="color:#E5C07B;">-</span><span style="color:#ABB2BF;"> 添加了 UI 设置。现在打开 Tensorboard 的 IP 地址和端口号可以自定义了</span></span>
<span class="line"><span style="color:#E5C07B;">-</span><span style="color:#ABB2BF;"> 修改一些新手模式中无用的参数显示</span></span>
<span class="line"><span style="color:#E5C07B;">-</span><span style="color:#ABB2BF;"> 优化了一些专家设置中参数的摆放</span></span>
<span class="line"></span>
<span class="line"><span style="color:#E06C75;">#### v1.1.0</span></span>
<span class="line"></span>
<span class="line"><span style="color:#E5C07B;">-</span><span style="color:#ABB2BF;"> 新手模式支持训练预览图</span></span>
<span class="line"><span style="color:#E5C07B;">-</span><span style="color:#ABB2BF;"> 添加一坨 DAdaptation 系列的优化器</span></span>
<span class="line"><span style="color:#E5C07B;">-</span><span style="color:#ABB2BF;"> 为 Tagger 添加了更多模型选项</span></span>
<span class="line"></span></code></pre></div></div><!--[--><!--]--></div><footer class="page-meta"><!----><!----><!----></footer><nav class="page-nav"><p class="inner"><!----><span class="next"><a href="/lora/index.md" class="" aria-label="LoRA训练"><!--[--><!--]--> LoRA训练 <!--[--><!--]--></a></span></p></nav><!--[--><!--]--></main><!--]--></div><!----><!--]--></div>
    <script type="module" src="/assets/app.9273d30a.js" defer></script>
  </body>
</html>
