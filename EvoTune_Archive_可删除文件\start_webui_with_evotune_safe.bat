@echo off
echo ========================================
echo    启动SD WebUI (EvoTune安全模式)
echo ========================================
echo.

cd /d "h:\StableDiffusion_Gaijin\sd-webui-aki-v4.4"

echo 检查EvoTune扩展状态...
if exist "extensions\evotune\scripts\evotune_extension.py" (
    echo ✓ EvoTune扩展已安装
    echo ℹ️ EvoTune默认处于禁用状态，不会影响模型加载
    echo ℹ️ 可在WebUI的"EvoTune"标签页中测试功能
) else (
    echo ⚠️ EvoTune扩展未找到
)

echo.
echo 启动WebUI...
echo 浏览器将自动打开 http://localhost:7860
echo.

set COMMANDLINE_ARGS=--theme dark --opt-sdp-attention --api --autolaunch --skip-python-version-check
python launch.py %COMMANDLINE_ARGS%

pause
