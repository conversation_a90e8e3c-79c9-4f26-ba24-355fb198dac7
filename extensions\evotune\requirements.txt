# EvoTune依赖包列表

# 核心深度学习框架
torch>=1.12.0
torchvision>=0.13.0
torchaudio>=0.12.0

# 数值计算
numpy>=1.21.0
scipy>=1.7.0

# 用户界面
gradio>=3.0.0

# 图像处理
Pillow>=8.3.0
opencv-python>=4.5.0

# 可视化
matplotlib>=3.5.0
seaborn>=0.11.0

# 系统监控
psutil>=5.8.0

# 日志和配置
pyyaml>=6.0
tqdm>=4.62.0

# 测试框架
pytest>=6.2.0
pytest-cov>=3.0.0

# 代码质量
black>=22.0.0
flake8>=4.0.0
mypy>=0.910

# 文档生成
sphinx>=4.0.0
sphinx-rtd-theme>=1.0.0

# 开发工具
jupyter>=1.0.0
ipython>=7.0.0

# 可选依赖（用于高级功能）
# transformers>=4.20.0  # 如需要更多预训练模型
# diffusers>=0.10.0     # 如需要更多扩散模型功能
# accelerate>=0.12.0    # 如需要分布式训练
# xformers>=0.0.14      # 如需要内存优化的注意力机制
