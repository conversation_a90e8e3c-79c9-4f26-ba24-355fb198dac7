"""
EvoTune自动安装脚本
将EvoTune集成到SD WebUI中

使用方法：
python install_evotune_to_webui.py
"""

import os
import shutil
import sys
from pathlib import Path

def create_evotune_extension():
    """创建EvoTune扩展"""
    
    # 定义路径
    current_dir = Path(__file__).parent
    webui_dir = current_dir / "sd-webui-aki-v4.4"
    extensions_dir = webui_dir / "extensions" / "evotune"
    scripts_dir = extensions_dir / "scripts"
    
    # 创建目录
    extensions_dir.mkdir(parents=True, exist_ok=True)
    scripts_dir.mkdir(parents=True, exist_ok=True)
    
    print(f"创建EvoTune扩展目录: {extensions_dir}")
    
    # 复制核心文件
    core_files = [
        "EvoTune_UNetModel.py",
        "sd_webui_evotune_integration.py", 
        "evotune_evaluation_metrics.py",
        "evotune_lora_adapter.py"
    ]
    
    for file in core_files:
        src = current_dir / file
        dst = extensions_dir / file
        if src.exists():
            shutil.copy2(src, dst)
            print(f"复制文件: {file}")
        else:
            print(f"警告: 文件不存在 {file}")
    
    # 创建扩展主文件
    extension_script = scripts_dir / "evotune_extension.py"
    with open(extension_script, 'w', encoding='utf-8') as f:
        f.write('''"""
EvoTune SD WebUI扩展
"""

import gradio as gr
from modules import script_callbacks, shared, sd_unet
from modules.ui_components import FormRow
import sys
import os

# 添加EvoTune路径
extension_dir = os.path.dirname(os.path.dirname(__file__))
sys.path.append(extension_dir)

try:
    from sd_webui_evotune_integration import EvoTuneSDUnetOption, register_evotune_unet
    from EvoTune_UNetModel import EvoTune_UNetModel
    evotune_available = True
    print("EvoTune模块加载成功")
except ImportError as e:
    evotune_available = False
    print(f"EvoTune模块加载失败: {e}")

def on_ui_settings():
    """添加EvoTune设置到WebUI"""
    if evotune_available:
        section = ('evotune', "EvoTune设置")
        
        shared.opts.add_option("evotune_enabled", shared.OptionInfo(
            True, "启用EvoTune模式", section=section))
        shared.opts.add_option("evotune_b1", shared.OptionInfo(
            1.5, "EvoTune参数b1 (骨干特征权重)", section=section))
        shared.opts.add_option("evotune_b2", shared.OptionInfo(
            1.2, "EvoTune参数b2 (跳跃连接权重)", section=section))
        shared.opts.add_option("evotune_s1", shared.OptionInfo(
            0.8, "EvoTune参数s1 (骨干特征缩放)", section=section))
        shared.opts.add_option("evotune_s2", shared.OptionInfo(
            0.5, "EvoTune参数s2 (跳跃连接缩放)", section=section))
        shared.opts.add_option("evotune_use_agent_attention", shared.OptionInfo(
            False, "启用Agent Attention (实验性)", section=section))
        shared.opts.add_option("evotune_use_adaptive_fourier", shared.OptionInfo(
            True, "启用自适应频域滤波", section=section))

def on_app_started(demo, app):
    """应用启动时注册EvoTune"""
    if evotune_available:
        try:
            # 创建EvoTune选项
            evotune_option = EvoTuneSDUnetOption({
                'b1': shared.opts.evotune_b1,
                'b2': shared.opts.evotune_b2,
                's1': shared.opts.evotune_s1,
                's2': shared.opts.evotune_s2,
                'use_agent_attention': shared.opts.evotune_use_agent_attention,
                'use_adaptive_fourier': shared.opts.evotune_use_adaptive_fourier
            })
            
            # 注册到UNet选项
            if hasattr(sd_unet, 'unet_options'):
                sd_unet.unet_options.append(evotune_option)
                print("EvoTune已注册到UNet选项")
            else:
                print("警告: 无法找到UNet选项列表")
                
        except Exception as e:
            print(f"EvoTune注册失败: {e}")

def on_ui_tabs():
    """添加EvoTune专用标签页"""
    if not evotune_available:
        return []
    
    with gr.Blocks(analytics_enabled=False) as evotune_interface:
        gr.Markdown("# EvoTune 积水干扰优化")
        
        with gr.Row():
            with gr.Column():
                gr.Markdown("## 参数设置")
                b1_slider = gr.Slider(0.5, 3.0, value=1.5, step=0.1, label="b1 (骨干特征权重)")
                b2_slider = gr.Slider(0.5, 3.0, value=1.2, step=0.1, label="b2 (跳跃连接权重)")
                s1_slider = gr.Slider(0.1, 2.0, value=0.8, step=0.1, label="s1 (骨干特征缩放)")
                s2_slider = gr.Slider(0.1, 2.0, value=0.5, step=0.1, label="s2 (跳跃连接缩放)")
                
                agent_attention_cb = gr.Checkbox(False, label="启用Agent Attention")
                adaptive_fourier_cb = gr.Checkbox(True, label="启用自适应频域滤波")
                
                apply_btn = gr.Button("应用设置", variant="primary")
            
            with gr.Column():
                gr.Markdown("## 使用说明")
                gr.Markdown("""
                ### EvoTune参数说明：
                - **b1, b2**: 控制特征权重，值越大增强效果越强
                - **s1, s2**: 控制频域缩放，影响纹理细节
                - **Agent Attention**: 增强水油区域注意力（实验性）
                - **自适应频域滤波**: 优化水面反射和油污纹理
                
                ### 最佳使用场景：
                1. 图生图模式，重绘幅度0.3-0.7
                2. 包含积水和油污的工业场景
                3. 提示词包含: oil leak, water interference
                
                ### 参数建议：
                - 水面反射强：增大b1和s1
                - 油污纹理复杂：增大b2和s2
                """)
        
        def apply_settings(b1, b2, s1, s2, agent_attn, adaptive_fourier):
            # 更新全局设置
            shared.opts.evotune_b1 = b1
            shared.opts.evotune_b2 = b2
            shared.opts.evotune_s1 = s1
            shared.opts.evotune_s2 = s2
            shared.opts.evotune_use_agent_attention = agent_attn
            shared.opts.evotune_use_adaptive_fourier = adaptive_fourier
            
            return "设置已应用！请在图生图模式下使用EvoTune。"
        
        apply_btn.click(
            apply_settings,
            inputs=[b1_slider, b2_slider, s1_slider, s2_slider, agent_attention_cb, adaptive_fourier_cb],
            outputs=[gr.Textbox(label="状态")]
        )
    
    return [(evotune_interface, "EvoTune", "evotune")]

# 注册回调
script_callbacks.on_ui_settings(on_ui_settings)
script_callbacks.on_app_started(on_app_started)
script_callbacks.on_ui_tabs(on_ui_tabs)
''')
    
    print(f"创建扩展脚本: {extension_script}")
    
    # 创建__init__.py文件
    init_file = extensions_dir / "__init__.py"
    with open(init_file, 'w', encoding='utf-8') as f:
        f.write('# EvoTune Extension\\n')
    
    return extensions_dir

def create_lora_integration():
    """创建LoRA训练集成"""
    
    current_dir = Path(__file__).parent
    lora_dir = current_dir / "lora-scripts-v1.8.5"
    
    if not lora_dir.exists():
        print(f"警告: LoRA目录不存在 {lora_dir}")
        return
    
    # 创建EvoTune训练脚本
    train_script = lora_dir / "train_evotune_lora.py"
    with open(train_script, 'w', encoding='utf-8') as f:
        f.write('''"""
EvoTune LoRA训练脚本
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

try:
    from evotune_lora_adapter import EvoTuneLoRAAdapter, create_evotune_lora_config
    from EvoTune_UNetModel import EvoTune_UNetModel
    import torch
    
    def create_evotune_model():
        """创建EvoTune模型用于LoRA训练"""
        model = EvoTune_UNetModel(
            b1=1.5, b2=1.2, s1=0.8, s2=0.5,
            model_channels=320,  # SD标准通道数
            resolution=64,       # SD标准分辨率
            use_agent_attention=False,  # 暂时禁用
            use_adaptive_fourier=True,
            max_timesteps=1000
        )
        
        print(f"EvoTune模型创建成功，参数量: {sum(p.numel() for p in model.parameters()):,}")
        return model
    
    def setup_lora_training(model, rank=4, alpha=1.0, dropout=0.1):
        """设置LoRA训练"""
        lora_adapter = EvoTuneLoRAAdapter(
            model=model,
            rank=rank,
            alpha=alpha,
            dropout=dropout
        )
        
        # 冻结基础模型
        lora_adapter.freeze_base_model()
        
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        total_params = sum(p.numel() for p in model.parameters())
        
        print(f"LoRA设置完成:")
        print(f"  可训练参数: {trainable_params:,}")
        print(f"  总参数: {total_params:,}")
        print(f"  参数效率: {trainable_params/total_params*100:.2f}%")
        
        return lora_adapter
    
    if __name__ == "__main__":
        print("=== EvoTune LoRA训练环境 ===")
        
        # 创建模型
        model = create_evotune_model()
        
        # 设置LoRA
        lora_adapter = setup_lora_training(model)
        
        print("\\n训练环境准备完成！")
        print("请在LoRA训练界面中配置以下参数:")
        print("- 网络类型: LoRA")
        print("- 网络维度: 4")
        print("- 学习率: 1e-4")
        print("- 批次大小: 4")

except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保EvoTune模块在正确的路径中")
''')
    
    print(f"创建LoRA训练脚本: {train_script}")
    
    # 创建配置文件模板
    config_template = lora_dir / "evotune_config_template.toml"
    with open(config_template, 'w', encoding='utf-8') as f:
        f.write('''# EvoTune LoRA训练配置模板

[model]
pretrained_model_name_or_path = "runwayml/stable-diffusion-v1-5"
v2 = false
v_parameterization = false

[dataset]
train_data_dir = "./train_data/water_oil_scenes"
resolution = 512
batch_size = 4
max_train_epochs = 100
caption_extension = ".txt"

[network]
network_module = "networks.lora"
network_dim = 4
network_alpha = 1.0
network_dropout = 0.1

[optimizer]
optimizer_type = "AdamW8bit"
learning_rate = 1e-4
lr_scheduler = "cosine_with_restarts"
lr_warmup_steps = 100

[evotune]
use_evotune = true
b1 = 1.5
b2 = 1.2
s1 = 0.8
s2 = 0.5
use_agent_attention = false
use_adaptive_fourier = true

[output]
output_dir = "./output/evotune_lora"
output_name = "evotune_water_oil"
save_every_n_epochs = 10
''')
    
    print(f"创建配置模板: {config_template}")

def create_startup_scripts():
    """创建启动脚本"""
    
    current_dir = Path(__file__).parent
    
    # SD WebUI启动脚本
    webui_script = current_dir / "start_webui_with_evotune.bat"
    with open(webui_script, 'w', encoding='utf-8') as f:
        f.write('''@echo off
echo ========================================
echo    启动EvoTune增强版SD WebUI
echo ========================================
echo.

cd /d "h:\\StableDiffusion_Gaijin\\sd-webui-aki-v4.4"

echo 检查EvoTune扩展...
if exist "extensions\\evotune\\EvoTune_UNetModel.py" (
    echo EvoTune扩展已安装 ✓
) else (
    echo 警告: EvoTune扩展未找到！
    echo 请先运行 install_evotune_to_webui.py
    pause
    exit
)

echo.
echo 启动WebUI...
echo 浏览器将自动打开 http://localhost:7860
echo.

set COMMANDLINE_ARGS=--xformers --api --listen
python launch.py %COMMANDLINE_ARGS%

pause
''')
    
    # LoRA训练启动脚本
    lora_script = current_dir / "start_lora_with_evotune.bat"
    with open(lora_script, 'w', encoding='utf-8') as f:
        f.write('''@echo off
echo ========================================
echo    启动EvoTune LoRA训练环境
echo ========================================
echo.

cd /d "h:\\StableDiffusion_Gaijin\\lora-scripts-v1.8.5"

echo 检查EvoTune训练脚本...
if exist "train_evotune_lora.py" (
    echo EvoTune训练脚本已安装 ✓
) else (
    echo 警告: EvoTune训练脚本未找到！
    echo 请先运行 install_evotune_to_webui.py
    pause
    exit
)

echo.
echo 初始化EvoTune训练环境...
python train_evotune_lora.py

echo.
echo 启动LoRA训练界面...
call A启动脚本.bat

pause
''')
    
    print(f"创建WebUI启动脚本: {webui_script}")
    print(f"创建LoRA启动脚本: {lora_script}")

def main():
    """主安装函数"""
    print("=== EvoTune自动安装程序 ===")
    print()
    
    try:
        # 1. 创建WebUI扩展
        print("1. 创建SD WebUI扩展...")
        extensions_dir = create_evotune_extension()
        
        # 2. 创建LoRA集成
        print("\\n2. 创建LoRA训练集成...")
        create_lora_integration()
        
        # 3. 创建启动脚本
        print("\\n3. 创建启动脚本...")
        create_startup_scripts()
        
        print("\\n" + "="*50)
        print("🎉 EvoTune安装完成！")
        print("="*50)
        print()
        print("📋 使用方法:")
        print("1. SD WebUI使用:")
        print("   双击 start_webui_with_evotune.bat")
        print("   在浏览器中访问 http://localhost:7860")
        print("   在设置中找到'EvoTune设置'进行配置")
        print()
        print("2. LoRA训练使用:")
        print("   双击 start_lora_with_evotune.bat")
        print("   按照提示配置训练参数")
        print()
        print("3. 详细使用说明请参考:")
        print("   EvoTune_Usage_Guide.md")
        print()
        print("⚠️  注意事项:")
        print("- Agent Attention功能暂时禁用（需要进一步调试）")
        print("- 建议在图生图模式下使用EvoTune")
        print("- 首次使用建议从默认参数开始")
        
    except Exception as e:
        print(f"❌ 安装失败: {e}")
        print("请检查文件路径和权限设置")

if __name__ == "__main__":
    main()
