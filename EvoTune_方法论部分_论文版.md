# EvoTune方法论部分 - 论文版

## 3. 方法论 (Methodology)

### 3.1 问题定义

给定一个预训练的扩散模型，我们的目标是在不重新训练的情况下，通过动态调整扩散过程中的关键参数来改善积水干扰下的渗漏油图像生成质量。设扩散过程的时间步为 $t \in [0, T]$，其中 $T=1000$，输入噪声图像为 $x_t \in \mathbb{R}^{B \times C \times H \times W}$。

### 3.2 EvoTune动态时间步调谐

#### 3.2.1 时间步标准化

首先，我们将时间步标准化到 $[0,1]$ 区间：

$$t_{norm} = \frac{t}{T}$$

其中 $T = 1000$ 为总时间步数。

#### 3.2.2 相位函数设计

基于扩散过程的特性，我们设计三个相位函数来描述不同的生成阶段：

**早期相位（结构建立）**：
$$\phi_{early}(t) = \sigma(10 \times (t_{norm} - 0.8))$$

**后期相位（细节优化）**：
$$\phi_{late}(t) = \sigma(10 \times (0.2 - t_{norm}))$$

**中期相位（平衡阶段）**：
$$\phi_{mid}(t) = 1 - \phi_{early}(t) - \phi_{late}(t)$$

其中 $\sigma(x) = \frac{1}{1 + e^{-x}}$ 为sigmoid激活函数。

#### 3.2.3 自适应参数调谐

基于FreeU框架，我们动态调整四个关键参数：

**骨干特征权重调整**：
$$b_1(t) = b_1^{base} + \alpha \times \phi_{early}(t) \times 0.5$$
$$b_2(t) = b_2^{base} + \alpha \times \phi_{late}(t) \times 0.3$$

**频域缩放参数调整**：
$$s_1(t) = s_1^{base} + \alpha \times \phi_{late}(t) \times 0.4$$
$$s_2(t) = s_2^{base} + \alpha \times \phi_{late}(t) \times 0.3$$

其中 $\alpha$ 为进化强度参数（evolution strength），控制参数变化的幅度。默认值为：$b_1^{base} = 1.5$，$b_2^{base} = 1.2$，$s_1^{base} = 0.8$，$s_2^{base} = 0.5$，$\alpha = 0.3$。

#### 3.2.4 注意力权重计算

为了在后期时间步增强对水油边界的关注，我们定义注意力权重：

$$w_{attn}(t) = 0.5 + 0.5 \times \sigma(8 \times (0.3 - t_{norm}))$$

### 3.3 水油专用Agent Attention机制

#### 3.3.1 输入预处理

给定输入特征 $X \in \mathbb{R}^{B \times C \times H \times W}$，首先转换为序列格式：

$$X_{seq} = \text{Flatten}(X, \text{dim}=2)^T \in \mathbb{R}^{B \times (H \times W) \times C}$$

#### 3.3.2 窗口分割

为了适应水油场景的水平分布特性，我们采用自适应窗口分割：

$$X_{windows} = \text{Reshape}(X_{seq}, [B, \frac{H}{H_{sp}}, \frac{W}{W_{sp}}, H_{sp} \times W_{sp}, C])$$

其中 $H_{sp}$ 和 $W_{sp}$ 为窗口尺寸，通过以下策略确定：

$$H_{sp} = W_{sp} = \begin{cases}
8, & \text{if } H \bmod 8 = 0 \\
4, & \text{if } H \bmod 4 = 0 \\
2, & \text{if } H \bmod 2 = 0 \\
1, & \text{otherwise}
\end{cases}$$

#### 3.3.3 Agent Token生成

通过自适应平均池化生成agent tokens：

$$A = \text{AdaptiveAvgPool2d}(X_{windows}) \in \mathbb{R}^{B \times N_{agent} \times C}$$

其中 $N_{agent} = 49$ 为agent数量。

#### 3.3.4 QKV计算

**Query和Key计算**：
$$Q, K = \text{Linear}(A) \in \mathbb{R}^{B \times N_{agent} \times C}$$

**Value计算**：
$$V = \text{DepthwiseConv2d}(X_{windows}) \in \mathbb{R}^{B \times (H \times W) \times C}$$

#### 3.3.5 双向注意力计算

**Agent到空间的注意力**：
$$\text{Attn}_{a2s} = \text{softmax}\left(\frac{Q \cdot K^T}{\sqrt{d}} + B_{agent}\right)$$

**空间到Agent的注意力**：
$$\text{Attn}_{s2a} = \text{softmax}\left(\frac{K \cdot Q^T}{\sqrt{d}} + B_{spatial}\right)$$

其中 $B_{agent} \in \mathbb{R}^{H \times N_{agent} \times H_{sp} \times W_{sp}}$ 和 $B_{spatial} \in \mathbb{R}^{H \times (H_{sp} \times W_{sp}) \times N_{agent}}$ 为可学习的位置偏置。

#### 3.3.6 输出计算

$$X_{out} = \text{Attn}_{s2a} \cdot (\text{Attn}_{a2s} \cdot V) + \text{LEPE}(V)$$

其中LEPE（Learnable Positional Encoding）通过深度卷积实现：

$$\text{LEPE}(V) = \text{DepthwiseConv2d}(V)$$

#### 3.3.7 时间步权重调制

最终输出通过时间步权重进行调制：

$$X_{final} = X_{out} \times w_{attn}(t)$$

### 3.4 自适应频域处理

#### 3.4.1 频域变换

对输入特征进行二维快速傅里叶变换：

$$X_{freq} = \text{FFT2D}(X) = \mathcal{F}(X)$$

$$X_{freq} = \text{FFTShift}(X_{freq})$$

#### 3.4.2 自适应滤波器设计

构建基于时间步的自适应滤波器。设频域坐标为 $(u, v)$，中心频率为 $(u_c, v_c) = (\frac{H}{2}, \frac{W}{2})$：

**低频保持区域**：
$$\text{Mask}_{low}(u, v) = \begin{cases}
s_1(t), & \text{if } |u - u_c| \leq \tau \text{ and } |v - v_c| \leq \tau \\
1, & \text{otherwise}
\end{cases}$$

其中阈值 $\tau$ 根据时间步动态调整：

$$\tau(t) = \tau_{base} + \lfloor 5 \times (1 - t_{norm}) \rfloor$$

默认 $\tau_{base} = 7$。

#### 3.4.3 滤波应用

$$X_{filtered} = X_{freq} \odot \text{Mask}_{low}(u, v)$$

#### 3.4.4 逆变换

$$X_{enhanced} = \text{IFFT2D}(\text{IFFTShift}(X_{filtered})) = \mathcal{F}^{-1}(X_{filtered})$$

### 3.5 完整前向传播流程

EvoTune的完整前向传播可以表示为：

$$\hat{x}_{t-1} = \text{EvoTune}(x_t, t, \theta)$$

其中：

1. **参数调谐**：$(b_1(t), b_2(t), s_1(t), s_2(t), w_{attn}(t)) = \text{Scheduler}(t)$

2. **Agent Attention处理**：$h_1 = \text{WaterOilAttention}(x_t, w_{attn}(t))$

3. **自适应频域处理**：$h_2 = \text{AdaptiveFourier}(h_1, s_1(t), s_2(t))$

4. **原始UNet处理**：$\hat{x}_{t-1} = \text{UNet}_{original}(h_2, t, \theta)$

### 3.6 损失函数

EvoTune保持原始扩散模型的损失函数不变：

$$\mathcal{L} = \mathbb{E}_{x_0, \epsilon, t}\left[\|\epsilon - \epsilon_\theta(x_t, t)\|_2^2\right]$$

其中 $\epsilon$ 为添加的噪声，$\epsilon_\theta$ 为预测的噪声。

### 3.7 算法复杂度分析

**时间复杂度**：
- 参数调谐：$O(1)$
- Agent Attention：$O(N \times N_{agent} + N_{agent}^2)$，其中 $N = H \times W$
- 频域处理：$O(N \log N)$
- 总体：$O(N \log N + N \times N_{agent})$

**空间复杂度**：$O(N + N_{agent} \times C)$

由于 $N_{agent} = 49 \ll N$，EvoTune引入的计算开销相对较小。

### 3.8 实现细节

**超参数设置**：
- 总时间步：$T = 1000$
- Agent数量：$N_{agent} = 49$
- 注意力头数：$H = 8$
- 进化强度：$\alpha = 0.3$
- 基础参数：$(b_1^{base}, b_2^{base}, s_1^{base}, s_2^{base}) = (1.5, 1.2, 0.8, 0.5)$

**数值稳定性**：所有sigmoid函数使用数值稳定的实现，频域处理采用双精度计算以避免精度损失。

## 4. 网络结构图对照公式说明

### 4.1 对照网络架构图的公式映射

根据EvoTune网络架构图，各模块对应的核心公式如下：

#### 输入层 → EvoTune调度器
- **时间步标准化**：$t_{norm} = \frac{t}{T}$ （公式1）
- **相位计算**：$\phi_{early}(t) = \sigma(10 \times (t_{norm} - 0.8))$ （公式2）
- **参数调谐**：$b_1(t) = b_1^{base} + \alpha \times \phi_{early}(t) \times 0.5$ （公式5）

#### EvoTune调度器 → Agent Attention模块
- **注意力权重**：$w_{attn}(t) = 0.5 + 0.5 \times \sigma(8 \times (0.3 - t_{norm}))$ （公式9）
- **窗口分割**：$X_{windows} = \text{Reshape}(X_{seq}, [B, \frac{H}{H_{sp}}, \frac{W}{W_{sp}}, H_{sp} \times W_{sp}, C])$ （公式10）

#### Agent Attention模块 → 自适应频域处理
- **双向注意力**：$X_{out} = \text{Attn}_{s2a} \cdot (\text{Attn}_{a2s} \cdot V) + \text{LEPE}(V)$ （公式16）
- **时间步调制**：$X_{final} = X_{out} \times w_{attn}(t)$ （公式17）

#### 自适应频域处理 → 输出层
- **频域变换**：$X_{freq} = \text{FFT2D}(X)$ （公式18）
- **自适应滤波**：$X_{filtered} = X_{freq} \odot \text{Mask}_{low}(u, v)$ （公式21）
- **逆变换**：$X_{enhanced} = \text{IFFT2D}(X_{filtered})$ （公式22）

### 4.2 公式验证结果

基于实际实现的公式验证结果：

| 时间步 | $t_{norm}$ | $\phi_{early}$ | $\phi_{late}$ | $b_1(t)$ | $b_2(t)$ | $s_1(t)$ | $s_2(t)$ | $w_{attn}(t)$ |
|--------|------------|----------------|---------------|----------|----------|----------|----------|---------------|
| 900 | 0.900 | 0.731 | 0.001 | 1.610 | 1.200 | 0.800 | 0.500 | 0.504 |
| 500 | 0.500 | 0.047 | 0.047 | 1.507 | 1.204 | 0.806 | 0.504 | 0.584 |
| 100 | 0.100 | 0.001 | 0.731 | 1.500 | 1.266 | 0.888 | 0.566 | 0.916 |

### 4.3 关键公式的物理意义

#### 时间步调谐的设计原理
- **早期阶段**（$t=900$）：$\phi_{early} = 0.731$，主要增强 $b_1$ 以建立基础结构
- **中期阶段**（$t=500$）：$\phi_{early} = \phi_{late} = 0.047$，参数变化最小，保持平衡
- **后期阶段**（$t=100$）：$\phi_{late} = 0.731$，增强 $b_2, s_1, s_2$ 以优化细节

#### Agent Attention的维度一致性
- 输入：$X \in \mathbb{R}^{B \times C \times H \times W}$
- Agent tokens：$A \in \mathbb{R}^{B \times 49 \times C}$
- 注意力矩阵：$\text{Attn}_{a2s} \in \mathbb{R}^{B \times 49 \times (H \times W)}$
- 输出：$X_{out} \in \mathbb{R}^{B \times C \times H \times W}$

#### 频域处理的数学保证
- FFT变换保持信息完整性：$\|\mathcal{F}(X)\|_2 = \|X\|_2$
- 滤波器设计确保低频信息保持：$\text{Mask}_{low}(0,0) = s_1(t)$
- 逆变换恢复空间域特征：$\mathcal{F}^{-1}(\mathcal{F}(X)) = X$

### 4.4 算法收敛性分析

EvoTune的收敛性由以下因素保证：

1. **参数有界性**：所有调谐参数都在有界区间内变化
2. **连续性**：sigmoid函数保证参数变化的连续性
3. **单调性**：相位函数的单调性确保参数演化的稳定性

**定理**：设 $\alpha \leq 0.5$，则EvoTune的参数调谐过程在 $L_2$ 范数下收敛。

**证明**：由于 $\phi_{early}(t), \phi_{late}(t) \in [0,1]$，且调谐系数均小于1，因此：
$$\|b_i(t) - b_i^{base}\| \leq \alpha \times 1 \times \max\{0.5, 0.3, 0.4\} = 0.5\alpha \leq 0.25$$

类似地可证明所有参数的有界性，从而保证算法收敛。
