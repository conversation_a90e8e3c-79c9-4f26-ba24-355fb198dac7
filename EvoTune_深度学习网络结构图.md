# EvoTune深度学习网络结构图

## 1. 主网络结构图（深度学习风格）

```
输入层
┌─────────────────────────────────────────────────────────────────┐
│                    Input: x_t ∈ R^(B×C×H×W), t ∈ [0,1000]      │
└─────────────────────────────┬───────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────────┐
│                    EvoTune时间步调谐器 (核心控制模块)              │
├─────────────────────────────────────────────────────────────────┤
│  Normalization: t̃ = t/1000                                     │
│  Phase Computation: φ_early = σ(10(t̃-0.8)), φ_late = σ(10(0.2-t̃)) │
│  Parameter Generation:                                          │
│    • b1(t) = 1.5 + 0.3×φ_early×0.5                            │
│    • s1(t) = 0.8 + 0.3×φ_late×0.4                             │
│    • w_attn(t) = 0.5 + 0.5σ(8(0.3-t̃))                        │
└─────────────────────────────┬───────────────────────────────────┘
                              │
                    ┌─────────┼─────────┐
                    │         │         │
               x_t  │    w_attn(t)  s1(t),s2(t)
                    │         │         │
                    ▼         ▼         │
┌─────────────────────────────────────────────────────────────────┐│
│              时间步感知Agent Attention模块 (执行器1)              ││
├─────────────────────────────────────────────────────────────────┤│
│  Reshape: x_seq ∈ R^(B×(H×W)×C)                                ││
│  Agent Generation: A = AdaptivePool(x) ∈ R^(B×49×C)            ││
│  QKV: Q,K = Linear(A), V = DepthwiseConv(x)                    ││
│  Bi-directional Attention: Attn1, Attn2                       ││
│  Feature Fusion: Y = Attn2·(Attn1·V) + V                      ││
│  Timestep Modulation: Y_out = Y × w_attn(t) ← 关键调制         ││
└─────────────────────────────┬───────────────────────────────────┘│
                              │                                   │
                              ▼                                   │
┌─────────────────────────────────────────────────────────────────┐│
│              时间步感知频域处理模块 (执行器2)                     ││
├─────────────────────────────────────────────────────────────────┤│
│  FFT Transform: Y_freq = FFT(Y_out)                            ││
│  Adaptive Filter: Mask(u,v) = {s1(t) if |u|,|v|≤τ(t), 1 else} ││
│  Dynamic Threshold: τ(t) = 7 + ⌊5(1-t̃)⌋                      ││
│  Frequency Filtering: Y_filtered = Y_freq ⊙ Mask              ││
│  IFFT Transform: Y_enhanced = IFFT(Y_filtered)                 ││
└─────────────────────────────┬───────────────────────────────────┘│
                              │                                   │
                              ▼                                   │
┌─────────────────────────────────────────────────────────────────┐│
│                        输出层                                   ││
│              Enhanced Features → Original UNet                 ││
└─────────────────────────────────────────────────────────────────┘│
                                                                  │
参数传递路径: ────────────────────────────────────────────────────┘
```

## 2. 模块详细结构图

### 2.1 EvoTune时间步调谐器
```
Input: t
  │
  ▼
┌─────────────────┐
│  Normalization  │  t̃ = t/1000
│     Layer       │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ Phase Computation│  φ_early = σ(10(t̃-0.8))
│     Layer       │  φ_late = σ(10(0.2-t̃))
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ Parameter Gen   │  b1(t) = 1.5 + 0.3×φ_early×0.5
│     Layer       │  s1(t) = 0.8 + 0.3×φ_late×0.4
│                 │  w_attn(t) = 0.5 + 0.5σ(8(0.3-t̃))
└─────────┬───────┘
          │
          ▼
Output: {b1(t), s1(t), s2(t), w_attn(t)}
```

### 2.2 时间步感知Agent Attention
```
Input: x ∈ R^(B×C×H×W), w_attn(t)
  │
  ▼
┌─────────────────┐
│   Reshape       │  x_seq ∈ R^(B×(H×W)×C)
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ Agent Generator │  A = AdaptivePool(x) ∈ R^(B×49×C)
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│  QKV Computer   │  Q,K = Linear(A)
│                 │  V = DepthwiseConv(x)
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ Bi-directional  │  Attn1 = softmax(Q·K^T/√d)
│   Attention     │  Attn2 = softmax(K·Q^T/√d)
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ Feature Fusion  │  Y = Attn2·(Attn1·V) + V
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ Timestep        │  Y_out = Y × w_attn(t)
│ Modulation      │  ↑ 关键的时间步感知机制
└─────────┬───────┘
          │
          ▼
Output: 时间步感知的注意力特征
```

### 2.3 时间步感知频域处理
```
Input: Y_out, s1(t), s2(t)
  │
  ▼
┌─────────────────┐
│  FFT Transform  │  Y_freq = FFT(Y_out)
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ Adaptive Filter │  Mask(u,v) = {s1(t) if |u|,|v|≤τ(t)
│    Designer     │                1     otherwise
│                 │  τ(t) = 7 + ⌊5(1-t̃)⌋
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ Frequency       │  Y_filtered = Y_freq ⊙ Mask
│  Filtering      │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ IFFT Transform  │  Y_enhanced = IFFT(Y_filtered)
└─────────┬───────┘
          │
          ▼
Output: 时间步感知的频域增强特征
```

## 3. 数据流向图（深度学习风格）

```
时间步 t ──────────────────────────────────────────────────┐
                                                          │
输入特征 x_t ─────────────────────────────┐                │
                                         │                │
                                         ▼                ▼
                                   ┌─────────────────────────────┐
                                   │    EvoTune时间步调谐器       │
                                   │                             │
                                   │ t → t̃ → φ → {params}        │
                                   └─────────────┬───────────────┘
                                                 │
                                    ┌────────────┼────────────┐
                                    │            │            │
                                    ▼            ▼            ▼
                                w_attn(t)    s1(t),s2(t)   b1(t),b2(t)
                                    │            │            │
                                    │            │            ▼
                                    │            │      (传递给UNet)
                                    │            │
                                    ▼            │
┌─────────────────────────────────────────────────────────────────┐│
│                Agent Attention模块                              ││
│                                                                 ││
│ x_t → Reshape → Agent → QKV → Attention → Fusion → ×w_attn(t)  ││
└─────────────────────────────┬───────────────────────────────────┘│
                              │                                   │
                              ▼                                   │
┌─────────────────────────────────────────────────────────────────┐│
│                频域处理模块                                      ││
│                                                                 ││
│ Y_out → FFT → Filter(s1,s2) → IFFT → Y_enhanced                ││
└─────────────────────────────┬───────────────────────────────────┘│
                              │                                   │
                              ▼                                   │
                        增强特征输出 ──────────────────────────────┘
```

## 4. 关键创新点标注

### 🎯 核心创新：统一的时间步感知框架
```
┌─────────────────────────────────────────────────────────────────┐
│                        EvoTune创新                              │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  传统方法: 固定参数 → 单一处理 → 输出                            │
│                                                                 │
│  EvoTune:  时间步 → 调谐器 → 多参数 → 协同执行 → 增强输出        │
│                      ↓        ↓        ↓                       │
│                   统一控制  自适应   时间步感知                   │
│                                                                 │
└─────────────────────────────────────────────────────────────────┘
```

### 🔧 技术创新点
1. **统一调谐器**: 一个时间步输入，多个自适应参数输出
2. **时间步感知**: 所有模块都接收时间步相关的控制参数
3. **协同优化**: 注意力和频域处理协同工作，而非独立执行

## 5. 参数演化可视化

```
时间步:  1000 ────────── 500 ────────── 0
         早期           中期           后期

w_attn:  0.504 ────────── 0.584 ────────── 0.916
         ████████       ████████████   ████████████████
         注意力弱        注意力中等      注意力强

s1(t):   0.800 ────────── 0.806 ────────── 0.888  
         ████████       ████████████   ████████████████
         滤波弱          滤波中等        滤波强

作用:    建立结构       平衡过渡       优化细节
         ↓              ↓              ↓
         Agent轻度作用   Agent中度作用   Agent强度作用
         频域保持结构    频域适度滤波    频域强化细节
```

## 6. 论文图表建议

### 6.1 主网络结构图
- 使用第1节的完整结构图
- 突出时间步调谐器作为核心控制模块
- 用箭头清晰标注参数传递路径

### 6.2 模块详细图
- 每个模块的内部结构（第2节）
- 突出时间步感知的关键机制
- 标注关键公式和计算步骤

### 6.3 对比图
- EvoTune vs 传统方法的架构对比
- 突出统一调谐 vs 独立处理的区别

这个结构图清晰地展示了EvoTune的真实架构：**时间步调谐器作为核心控制模块，Agent Attention和频域处理作为时间步感知的执行模块，形成统一的协同优化框架**。
