"""
测试EvoTune模块导入和功能
"""

import sys
import os

# 添加路径
sys.path.append('.')

def test_import():
    """测试导入"""
    print("=" * 50)
    print("测试EvoTune模块导入")
    print("=" * 50)
    
    try:
        from EvoTune_Complete import EvoTuneScheduler
        print("✅ EvoTuneScheduler导入成功")
        
        # 测试调度器
        scheduler = EvoTuneScheduler(1.5, 1.2, 0.8, 0.5, 0.3)
        print("✅ EvoTuneScheduler创建成功")
        
        import torch
        b1, b2, s1, s2 = scheduler.get_adaptive_params(torch.tensor([500]))
        attention_weight = scheduler.get_water_oil_attention_weights(torch.tensor([500]))
        
        print(f"✅ 调度器工作正常:")
        print(f"   时间步500: b1={b1:.3f}, b2={b2:.3f}, s1={s1:.3f}, s2={s2:.3f}")
        print(f"   注意力权重: {attention_weight:.3f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_webui_script():
    """测试WebUI脚本"""
    print("\n" + "=" * 50)
    print("测试WebUI脚本")
    print("=" * 50)
    
    try:
        # 检查脚本文件
        script_path = "sd-webui-aki-v4.4/extensions/evotune/scripts/evotune_script.py"
        if os.path.exists(script_path):
            print(f"✅ 脚本文件存在: {script_path}")
            
            # 读取脚本内容检查关键函数
            with open(script_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            if 'setup_unet_interception' in content:
                print("✅ UNet拦截函数存在")
            else:
                print("❌ UNet拦截函数不存在")
                
            if 'evotune_unet_forward' in content:
                print("✅ EvoTune前向传播函数存在")
            else:
                print("❌ EvoTune前向传播函数不存在")
                
            if 'apply_adaptive_fourier' in content:
                print("✅ 自适应频域滤波函数存在")
            else:
                print("❌ 自适应频域滤波函数不存在")
                
            return True
        else:
            print(f"❌ 脚本文件不存在: {script_path}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    import_ok = test_import()
    script_ok = test_webui_script()
    
    print("\n" + "=" * 50)
    print("测试结果")
    print("=" * 50)
    
    if import_ok and script_ok:
        print("🎉 所有测试通过！")
        print("\n📋 EvoTune真正生效的关键文件:")
        print("1. EvoTune_Complete.py - 核心调度器和算法")
        print("2. sd-webui-aki-v4.4/extensions/evotune/scripts/evotune_script.py - WebUI集成")
        print("\n🔍 EvoTune如何影响生成:")
        print("- setup_unet_interception() 拦截UNet的forward方法")
        print("- evotune_unet_forward() 在每个时间步应用动态调谐")
        print("- apply_adaptive_fourier() 应用频域滤波")
        print("- 根据时间步动态调整b1,b2,s1,s2参数")
    else:
        print("⚠️ 部分测试失败")
