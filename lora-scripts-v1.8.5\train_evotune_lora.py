"""
EvoTune LoRA训练脚本
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

try:
    from evotune_lora_adapter import EvoTuneLoRAAdapter, create_evotune_lora_config
    from EvoTune_UNetModel import EvoTune_UNetModel
    import torch
    
    def create_evotune_model():
        """创建EvoTune模型用于LoRA训练"""
        model = EvoTune_UNetModel(
            b1=1.5, b2=1.2, s1=0.8, s2=0.5,
            model_channels=320,  # SD标准通道数
            resolution=64,       # SD标准分辨率
            use_agent_attention=False,  # 暂时禁用
            use_adaptive_fourier=True,
            max_timesteps=1000
        )
        
        print(f"EvoTune模型创建成功，参数量: {sum(p.numel() for p in model.parameters()):,}")
        return model
    
    def setup_lora_training(model, rank=4, alpha=1.0, dropout=0.1):
        """设置LoRA训练"""
        lora_adapter = EvoTuneLoRAAdapter(
            model=model,
            rank=rank,
            alpha=alpha,
            dropout=dropout
        )
        
        # 冻结基础模型
        lora_adapter.freeze_base_model()
        
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        total_params = sum(p.numel() for p in model.parameters())
        
        print(f"LoRA设置完成:")
        print(f"  可训练参数: {trainable_params:,}")
        print(f"  总参数: {total_params:,}")
        print(f"  参数效率: {trainable_params/total_params*100:.2f}%")
        
        return lora_adapter
    
    if __name__ == "__main__":
        print("=== EvoTune LoRA训练环境 ===")
        
        # 创建模型
        model = create_evotune_model()
        
        # 设置LoRA
        lora_adapter = setup_lora_training(model)
        
        print("\n训练环境准备完成！")
        print("请在LoRA训练界面中配置以下参数:")
        print("- 网络类型: LoRA")
        print("- 网络维度: 4")
        print("- 学习率: 1e-4")
        print("- 批次大小: 4")

except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保EvoTune模块在正确的路径中")
