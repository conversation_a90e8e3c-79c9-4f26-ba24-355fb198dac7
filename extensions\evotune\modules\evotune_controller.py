"""
EvoTune核心控制器
实现公式(9a-9i)的统一特征调制机制
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, Tuple, Optional
import logging

from .timestep_scheduler import TimestepScheduler, timestep_context
from .agent_attention import TimestepAwareAgentAttention
from .fourier_processor import AdaptiveFourierProcessor

logger = logging.getLogger(__name__)

class FeatureFusionModule(nn.Module):
    """
    特征融合模块
    实现门控融合机制
    """
    
    def __init__(self, channels: int):
        """
        初始化特征融合模块
        
        Args:
            channels: 特征通道数
        """
        super().__init__()
        
        self.channels = channels
        
        # 门控信号生成
        self.gate_conv = nn.Sequential(
            nn.Conv2d(channels * 2, channels, 1),
            nn.BatchNorm2d(channels),
            nn.ReLU(inplace=True),
            nn.Conv2d(channels, channels, 1),
            nn.Sigmoid()
        )
        
        # 融合特征生成
        self.fusion_conv = nn.Sequential(
            nn.Conv2d(channels * 2, channels, 1),
            nn.BatchNorm2d(channels),
            nn.ReLU(inplace=True),
            nn.Conv2d(channels, channels, 1)
        )
        
        logger.debug(f"FeatureFusionModule initialized for {channels} channels")
    
    def forward(self, 
                h_input: torch.Tensor, 
                h_fourier: torch.Tensor, 
                b_weight: torch.Tensor) -> torch.Tensor:
        """
        特征融合前向传播
        
        公式(9e): G = σ(Conv([H_input, H_fourier]))
        公式(9f): F = Conv([H_input, H_fourier])
        公式(9g): H_enhanced = H_input ⊙ (1 - G ⊙ b(t)) + F ⊙ (G ⊙ b(t))
        
        Args:
            h_input: 原始输入特征 [B, C, H, W]
            h_fourier: 频域处理特征 [B, C, H, W]
            b_weight: 时间步权重 [B] or scalar
            
        Returns:
            h_enhanced: 融合后的特征 [B, C, H, W]
        """
        # 特征拼接
        combined = torch.cat([h_input, h_fourier], dim=1)  # [B, 2C, H, W]
        
        # 公式(9e): 生成门控信号
        gate = self.gate_conv(combined)  # [B, C, H, W]
        
        # 公式(9f): 生成融合特征
        fusion = self.fusion_conv(combined)  # [B, C, H, W]
        
        # 处理时间步权重的维度
        if isinstance(b_weight, torch.Tensor):
            if b_weight.dim() == 0:  # scalar
                b_weight_expanded = b_weight
            else:  # [B]
                b_weight_expanded = b_weight.view(-1, 1, 1, 1)  # [B, 1, 1, 1]
        else:  # float
            b_weight_expanded = b_weight
        
        # 公式(9g): 自适应融合
        gate_weighted = gate * b_weight_expanded  # [B, C, H, W]
        w_orig = 1 - gate_weighted  # 原始特征权重
        w_proc = gate_weighted      # 处理特征权重
        
        h_enhanced = h_input * w_orig + fusion * w_proc
        
        return h_enhanced


class EvoTuneController(nn.Module):
    """
    EvoTune核心控制器
    实现统一的特征调制机制
    """
    
    def __init__(self, 
                 channels: int,
                 total_timesteps: int = 1000,
                 num_agents: int = 49,
                 agent_dim: int = 64):
        """
        初始化EvoTune控制器
        
        Args:
            channels: 特征通道数
            total_timesteps: 总时间步数
            num_agents: Agent token数量
            agent_dim: Agent token维度
        """
        super().__init__()
        
        self.channels = channels
        self.total_timesteps = total_timesteps
        
        # 时间步调谐器
        self.timestep_scheduler = TimestepScheduler(total_timesteps=total_timesteps)
        
        # Agent Attention机制
        self.agent_attention = TimestepAwareAgentAttention(
            channels=channels,
            num_agents=num_agents,
            agent_dim=agent_dim
        )
        
        # 自适应频域处理器
        self.fourier_processor = AdaptiveFourierProcessor(channels=channels)
        
        # 特征融合模块
        self.feature_fusion = FeatureFusionModule(channels=channels)
        
        # 维度适配器（处理不同通道数的特征）
        self.dimension_adapters = nn.ModuleDict()
        
        logger.info(f"EvoTuneController initialized for {channels} channels, {total_timesteps} timesteps")
    
    def get_dimension_adapter(self, input_channels: int) -> nn.Module:
        """
        获取或创建维度适配器
        
        Args:
            input_channels: 输入特征通道数
            
        Returns:
            adapter: 维度适配器
        """
        key = str(input_channels)
        
        if key not in self.dimension_adapters:
            if input_channels == self.channels:
                # 通道数匹配，使用恒等映射
                self.dimension_adapters[key] = nn.Identity()
            else:
                # 通道数不匹配，创建适配器
                self.dimension_adapters[key] = nn.Sequential(
                    nn.Conv2d(input_channels, self.channels, 1),
                    nn.BatchNorm2d(self.channels),
                    nn.ReLU(inplace=True)
                )
                logger.info(f"Created dimension adapter: {input_channels} → {self.channels}")
        
        return self.dimension_adapters[key]
    
    def modulate_features(self, 
                         h_input: torch.Tensor, 
                         timesteps: torch.Tensor,
                         b_weight_idx: int = 0) -> torch.Tensor:
        """
        统一特征调制机制
        
        实现公式(9a-9i)的完整调制流程
        
        Args:
            h_input: 输入特征 [B, C_in, H, W]
            timesteps: 时间步 [B] or scalar
            b_weight_idx: 权重索引 (0=b1, 1=b2)
            
        Returns:
            h_enhanced: 增强后的特征 [B, C, H, W]
        """
        # 维度适配
        input_channels = h_input.shape[1]
        adapter = self.get_dimension_adapter(input_channels)
        h_adapted = adapter(h_input)  # [B, C, H, W]
        
        # 获取时间步感知参数
        params = self.timestep_scheduler.get_adaptive_params(timesteps)
        
        # 设置全局上下文（解决时间步信息传递问题）
        timestep_context.set_context(timesteps, params)
        
        try:
            # 公式(9a): Agent Attention增强
            y_out = self.agent_attention(h_adapted, params['w_attn'])
            
            # 公式(9b-9d): 频域处理
            s_weights = params['s_weights']  # [s1(t), s2(t)]
            if s_weights.dim() > 1:  # [B, 2]
                s1, s2 = s_weights[:, 0], s_weights[:, 1]
            else:  # [2]
                s1, s2 = s_weights[0], s_weights[1]
            
            h_fourier = self.fourier_processor(
                y_out, s1, s2, params['t_normalized']
            )
            
            # 公式(9e-9i): 门控融合
            b_weights = params['b_weights']  # [b1(t), b2(t)]
            if b_weights.dim() > 1:  # [B, 2]
                b_weight = b_weights[:, b_weight_idx]
            else:  # [2]
                b_weight = b_weights[b_weight_idx]
            
            h_enhanced = self.feature_fusion(h_adapted, h_fourier, b_weight)
            
            # 如果需要，恢复原始通道数
            if input_channels != self.channels:
                restore_adapter = nn.Conv2d(self.channels, input_channels, 1, 
                                          device=h_enhanced.device, dtype=h_enhanced.dtype)
                h_enhanced = restore_adapter(h_enhanced)
            
            return h_enhanced
            
        except Exception as e:
            logger.error(f"EvoTune modulation failed: {e}")
            # 降级策略：返回原始特征
            return h_input
        
        finally:
            # 清理上下文
            timestep_context.clear_context()
    
    def modulate_backbone_features(self, 
                                  h_backbone: torch.Tensor, 
                                  timesteps: torch.Tensor) -> torch.Tensor:
        """
        公式(10): 骨干特征调制
        H_backbone^enhanced = EvoTuneModulation(H_backbone, b1(t), s(t), w_attn(t))
        
        Args:
            h_backbone: 骨干特征 [B, C, H, W]
            timesteps: 时间步 [B] or scalar
            
        Returns:
            h_enhanced: 增强的骨干特征 [B, C, H, W]
        """
        return self.modulate_features(h_backbone, timesteps, b_weight_idx=0)
    
    def modulate_skip_features(self, 
                              h_skip: torch.Tensor, 
                              timesteps: torch.Tensor) -> torch.Tensor:
        """
        公式(11): 跳跃连接特征调制
        H_skip^enhanced = EvoTuneModulation(H_skip, b2(t), s(t), w_attn(t))
        
        Args:
            h_skip: 跳跃连接特征 [B, C, H, W]
            timesteps: 时间步 [B] or scalar
            
        Returns:
            h_enhanced: 增强的跳跃连接特征 [B, C, H, W]
        """
        return self.modulate_features(h_skip, timesteps, b_weight_idx=1)
    
    def forward(self, 
                h_input: torch.Tensor, 
                timesteps: torch.Tensor,
                modulation_type: str = 'backbone') -> torch.Tensor:
        """
        EvoTune控制器前向传播
        
        Args:
            h_input: 输入特征 [B, C, H, W]
            timesteps: 时间步 [B] or scalar
            modulation_type: 调制类型 ('backbone' or 'skip')
            
        Returns:
            h_enhanced: 增强后的特征 [B, C, H, W]
        """
        if modulation_type == 'backbone':
            return self.modulate_backbone_features(h_input, timesteps)
        elif modulation_type == 'skip':
            return self.modulate_skip_features(h_input, timesteps)
        else:
            raise ValueError(f"Unknown modulation type: {modulation_type}")


class MemoryLayoutOptimizer:
    """
    内存布局优化器
    解决HOOK机制中的内存布局问题
    """
    
    def __init__(self):
        pass
    
    def ensure_contiguous(self, x: torch.Tensor) -> torch.Tensor:
        """确保张量内存连续"""
        if not x.is_contiguous():
            return x.contiguous()
        return x
    
    def optimize_for_fft(self, x: torch.Tensor) -> torch.Tensor:
        """为FFT操作优化内存布局"""
        x = self.ensure_contiguous(x)
        
        # 确保数据类型适合FFT
        if x.dtype not in [torch.float32, torch.float64, torch.complex64, torch.complex128]:
            x = x.float()
        
        return x
