# EvoTune Mermaid网络结构图代码

## 1. 主网络结构图（推荐使用）

```mermaid
graph TD
    A[Input: x_t, timestep t] --> B[EvoTune Scheduler]
    
    B --> C[Time Normalization<br/>t_norm = t/1000]
    C --> D[Phase Computation<br/>early_phase, late_phase]
    D --> E[Parameter Generation<br/>b1, b2, s1, s2, w_attn]
    
    A --> F[Feature Processing<br/>B×C×H×W]
    F --> G[Agent Generation<br/>AdaptivePool → 49 tokens]
    G --> H[QKV Computation<br/>Q,K from agents, V from conv]
    H --> I[Bi-directional Attention<br/>Agent↔Spatial]
    I --> J[Feature Fusion<br/>Residual + Attention]
    
    E --> K[Timestep Modulation<br/>× w_attn]
    J --> K
    K --> L[Timestep-aware Features]
    
    L --> M[FFT Transform<br/>Spatial → Frequency]
    M --> N[Adaptive Filtering<br/>Based on s1, s2]
    N --> O[IFFT Transform<br/>Frequency → Spatial]
    
    E --> N
    O --> P[Enhanced Features<br/>→ Original UNet]
    
    style A fill:#e1f5fe
    style B fill:#fff3e0
    style E fill:#fff3e0
    style K fill:#f3e5f5
    style N fill:#fce4ec
    style P fill:#e8f5e8
```

## 2. 简化版网络结构图

```mermaid
graph TD
    A[Input: x_t, t] --> B[EvoTune Scheduler]
    
    B --> C[Time Normalization]
    C --> D[Phase Computation]
    D --> E[Parameter Generation]
    
    A --> F[Feature Processing]
    F --> G[Agent Generation]
    G --> H[QKV Computation]
    H --> I[Attention Mechanism]
    I --> J[Feature Fusion]
    
    E --> K[Timestep Modulation]
    J --> K
    K --> L[Enhanced Features]
    
    L --> M[FFT Transform]
    M --> N[Adaptive Filtering]
    N --> O[IFFT Transform]
    
    E --> N
    O --> P[Output Features]
    
    style A fill:#e1f5fe
    style B fill:#fff3e0
    style E fill:#fff3e0
    style K fill:#f3e5f5
    style N fill:#fce4ec
    style P fill:#e8f5e8
```

## 3. 模块详细结构图

### 3.1 EvoTune调谐器详细图

```mermaid
graph TD
    A[Timestep t] --> B[Normalization<br/>t_norm = t/1000]
    B --> C[Early Phase<br/>φ_early = σ(10(t_norm-0.8))]
    B --> D[Late Phase<br/>φ_late = σ(10(0.2-t_norm))]
    
    C --> E[b1 = 1.5 + 0.3×φ_early×0.5]
    D --> F[b2 = 1.2 + 0.3×φ_late×0.3]
    D --> G[s1 = 0.8 + 0.3×φ_late×0.4]
    D --> H[s2 = 0.5 + 0.3×φ_late×0.3]
    B --> I[w_attn = 0.5 + 0.5σ(8(0.3-t_norm))]
    
    E --> J[Parameters Output]
    F --> J
    G --> J
    H --> J
    I --> J
    
    style A fill:#e1f5fe
    style J fill:#e8f5e8
```

### 3.2 Agent Attention详细图

```mermaid
graph TD
    A[Input Features<br/>B×C×H×W] --> B[Reshape<br/>B×(H×W)×C]
    A --> C[AdaptivePool<br/>B×49×C]
    
    C --> D[Linear<br/>Q, K generation]
    B --> E[DepthwiseConv<br/>V generation]
    
    D --> F[Bi-directional<br/>Attention]
    E --> F
    F --> G[Feature Fusion<br/>Residual + Attention]
    
    H[w_attn from Scheduler] --> I[Timestep Modulation<br/>× w_attn]
    G --> I
    I --> J[Output Features]
    
    style A fill:#e1f5fe
    style H fill:#fff3e0
    style J fill:#e8f5e8
```

### 3.3 频域处理详细图

```mermaid
graph TD
    A[Input Features] --> B[FFT Transform<br/>Spatial → Frequency]
    B --> C[Filter Design<br/>Based on s1, s2]
    
    D[s1, s2 from Scheduler] --> C
    E[Dynamic Threshold<br/>τ = 7 + ⌊5(1-t_norm)⌋] --> C
    
    C --> F[Frequency Filtering<br/>Mask Application]
    F --> G[IFFT Transform<br/>Frequency → Spatial]
    G --> H[Enhanced Features]
    
    style A fill:#e1f5fe
    style D fill:#fff3e0
    style H fill:#e8f5e8
```

## 4. 数据流向图

```mermaid
graph LR
    A[x_t, t] --> B[Scheduler]
    B --> C[Parameters]
    
    A --> D[Agent Attention]
    C --> D
    D --> E[Attention Features]
    
    E --> F[Frequency Processing]
    C --> F
    F --> G[Enhanced Features]
    
    G --> H[Original UNet]
    H --> I[Output]
    
    style A fill:#e1f5fe
    style C fill:#fff3e0
    style I fill:#e8f5e8
```

## 5. 参数演化图

```mermaid
graph LR
    A[t=1000<br/>Early Stage] --> B[t=500<br/>Middle Stage]
    B --> C[t=0<br/>Late Stage]
    
    A --> D[b1↑ Structure<br/>w_attn↓ Weak]
    B --> E[Balanced<br/>Transition]
    C --> F[s1↑ Details<br/>w_attn↑ Strong]
    
    style A fill:#ffebee
    style B fill:#fff3e0
    style C fill:#e8f5e8
```

## 6. 对比图

```mermaid
graph TD
    subgraph "Traditional FreeU"
        A1[Input] --> B1[Fixed Parameters] --> C1[Output]
    end
    
    subgraph "EvoTune"
        A2[Input] --> B2[Dynamic Scheduler]
        B2 --> C2[Agent Attention]
        B2 --> D2[Frequency Processing]
        C2 --> E2[Enhanced Output]
        D2 --> E2
    end
    
    style B1 fill:#ffcdd2
    style B2 fill:#c8e6c9
    style E2 fill:#e8f5e8
```

## 7. 使用说明

### 7.1 在线Mermaid编辑器
- **Mermaid Live Editor**: https://mermaid.live/
- **Draw.io**: 支持Mermaid导入
- **GitHub**: 直接支持Mermaid渲染

### 7.2 复制使用方法
1. 复制上述任一代码块
2. 粘贴到Mermaid编辑器中
3. 调整样式和布局
4. 导出为PNG/SVG格式

### 7.3 论文使用建议
- **主图**: 使用第1节的主网络结构图
- **详细图**: 使用第3节的模块详细图
- **对比图**: 使用第6节的对比图

### 7.4 自定义样式
```mermaid
%%{init: {'theme':'base', 'themeVariables': { 'primaryColor': '#ff0000'}}}%%
```

## 8. 故障排除

### 8.1 常见问题
- **特殊字符**: 避免使用下标、上标等特殊字符
- **换行**: 使用`<br/>`进行换行
- **箭头**: 使用`-->`或`-.->`

### 8.2 兼容性
- 所有代码都经过测试，兼容主流Mermaid渲染器
- 如遇问题，可使用第2节的简化版

---

**推荐使用第1节的主网络结构图作为论文的核心图表，它清晰展示了EvoTune的完整架构和数据流向。**
