import{_ as i,f as d,u as p,g as f,r as v,o as k,c as g,a as e,t as c,d as L,w as x,b as B,h as l}from"./app.9273d30a.js";const N={class:"theme-container"},T={class:"page"},b={class:"theme-default-content"},C=e("h1",null,"404",-1),M=d({__name:"404",setup(R){var a,s,n;const _=p(),o=f(),t=(a=o.value.notFound)!=null?a:["Not Found"],r=()=>t[Math.floor(Math.random()*t.length)],u=(s=o.value.home)!=null?s:_.value,m=(n=o.value.backToHome)!=null?n:"Back to home";return(V,w)=>{const h=v("RouterLink");return k(),g("div",N,[e("main",T,[e("div",b,[C,e("blockquote",null,c(r()),1),L(h,{to:l(u)},{default:x(()=>[B(c(l(m)),1)]),_:1},8,["to"])])])])}}});var F=i(M,[["__file","404.vue"]]);export{F as default};
