# EvoTune LoRA训练配置模板

[model]
pretrained_model_name_or_path = "runwayml/stable-diffusion-v1-5"
v2 = false
v_parameterization = false

[dataset]
train_data_dir = "./train_data/water_oil_scenes"
resolution = 512
batch_size = 4
max_train_epochs = 100
caption_extension = ".txt"

[network]
network_module = "networks.lora"
network_dim = 4
network_alpha = 1.0
network_dropout = 0.1

[optimizer]
optimizer_type = "AdamW8bit"
learning_rate = 1e-4
lr_scheduler = "cosine_with_restarts"
lr_warmup_steps = 100

[evotune]
use_evotune = true
b1 = 1.5
b2 = 1.2
s1 = 0.8
s2 = 0.5
use_agent_attention = false
use_adaptive_fourier = true

[output]
output_dir = "./output/evotune_lora"
output_name = "evotune_water_oil"
save_every_n_epochs = 10
