@echo off
echo ========================================
echo    启动EvoTune增强版SD WebUI (安全模式)
echo ========================================
echo.

cd /d "h:\StableDiffusion_Gaijin\sd-webui-aki-v4.4"

echo 检查EvoTune扩展...
if exist "extensions\evotune\EvoTune_UNetModel.py" (
    echo EvoTune扩展已安装 ✓
) else (
    echo 警告: EvoTune扩展未找到！
    echo 请先运行 install_evotune_to_webui.py
    pause
    exit
)

echo.
echo 禁用可能冲突的扩展...
if exist "extensions\Stable-Diffusion-WebUI-TensorRT-main" (
    echo 临时禁用TensorRT扩展...
    ren "extensions\Stable-Diffusion-WebUI-TensorRT-main" "extensions\Stable-Diffusion-WebUI-TensorRT-main.disabled"
)

if exist "extensions\infinite-zoom-automatic1111-webui" (
    echo 临时禁用infinite-zoom扩展...
    ren "extensions\infinite-zoom-automatic1111-webui" "extensions\infinite-zoom-automatic1111-webui.disabled"
)

echo.
echo 启动WebUI (安全模式)...
echo 浏览器将自动打开 http://localhost:7860
echo.

set COMMANDLINE_ARGS=--theme dark --opt-sdp-attention --api --autolaunch --skip-python-version-check --disable-safe-unpickle
python launch.py %COMMANDLINE_ARGS%

echo.
echo 恢复扩展...
if exist "extensions\Stable-Diffusion-WebUI-TensorRT-main.disabled" (
    ren "extensions\Stable-Diffusion-WebUI-TensorRT-main.disabled" "extensions\Stable-Diffusion-WebUI-TensorRT-main"
)

if exist "extensions\infinite-zoom-automatic1111-webui.disabled" (
    ren "extensions\infinite-zoom-automatic1111-webui.disabled" "extensions\infinite-zoom-automatic1111-webui"
)

pause
