# EvoTune方法论 - 精简版

## 3. 方法论 (Methodology)

### 3.1 问题定义与动机

积水干扰下的渗漏油图像生成面临两个核心挑战：(1) 水纹理的真实感不足；(2) 水油边界的过渡不自然。现有方法如FreeU使用固定参数，无法适应扩散过程不同阶段的需求。我们提出EvoTune，通过动态调谐扩散过程中的关键参数来解决这些问题。

### 3.2 EvoTune核心创新

EvoTune包含三个核心创新模块，如图1所示：

#### 3.2.1 动态时间步调谐

**核心思想**：扩散过程的不同阶段需要不同的参数配置——早期需要建立基础结构，后期需要优化细节纹理。

**数学表达**：
给定时间步 $t \in [0, 1000]$，首先进行标准化：
$$\tilde{t} = \frac{t}{1000} \tag{1}$$

设计相位函数描述扩散阶段：
$$\phi_{early}(t) = \sigma(10(\tilde{t} - 0.8)) \tag{2}$$
$$\phi_{late}(t) = \sigma(10(0.2 - \tilde{t})) \tag{3}$$

其中 $\sigma(x) = 1/(1+e^{-x})$ 为sigmoid函数。

基于相位函数动态调整FreeU参数：
$$b_1(t) = 1.5 + 0.3 \times \phi_{early}(t) \times 0.5 \tag{4}$$
$$s_1(t) = 0.8 + 0.3 \times \phi_{late}(t) \times 0.4 \tag{5}$$
$$s_2(t) = 0.5 + 0.3 \times \phi_{late}(t) \times 0.3 \tag{6}$$

**物理意义**：
- 早期阶段($t=900$)：$\phi_{early}=0.731$，增强骨干特征权重$b_1$建立基础结构
- 后期阶段($t=100$)：$\phi_{late}=0.731$，增强频域缩放$s_1,s_2$优化水纹理细节

#### 3.2.2 水油专用Agent Attention

**核心思想**：水油场景具有明显的水平分布特性，需要专门设计的注意力机制来捕获这种空间模式。

**数学表达**：
给定输入特征 $X \in \mathbb{R}^{B \times C \times H \times W}$，通过自适应池化生成Agent tokens：
$$A = \text{AdaptivePool}(X) \in \mathbb{R}^{B \times 49 \times C} \tag{7}$$

设计双向注意力机制：
$$\text{Attn}_1 = \text{softmax}\left(\frac{A \cdot X^T}{\sqrt{d}}\right) \tag{8}$$
$$\text{Attn}_2 = \text{softmax}\left(\frac{X \cdot A^T}{\sqrt{d}}\right) \tag{9}$$

输出融合：
$$Y = \text{Attn}_2 \cdot (\text{Attn}_1 \cdot X) + X \tag{10}$$

时间步权重调制：
$$Y_{final} = Y \times w_{attn}(t) \tag{11}$$
其中：
$$w_{attn}(t) = 0.5 + 0.5\sigma(8(0.3 - \tilde{t})) \tag{12}$$

**创新点**：
- 49个Agent tokens专门捕获水油区域特征
- 双向注意力增强空间-语义交互
- 时间步权重在后期增强注意力强度

#### 3.2.3 自适应频域处理

**核心思想**：不同扩散阶段需要不同的频域滤波策略——早期保持整体结构，后期增强细节纹理。

**数学表达**：
对输入进行频域变换：
$$X_{freq} = \text{FFT}(X) \tag{13}$$

设计自适应滤波器：
$$\text{Mask}(u,v) = \begin{cases}
s_1(t), & \text{if } |u|, |v| \leq \tau(t) \\
1, & \text{otherwise}
\end{cases} \tag{14}$$

其中动态阈值：
$$\tau(t) = 7 + \lfloor 5(1-\tilde{t}) \rfloor \tag{15}$$

应用滤波并逆变换：
$$X_{enhanced} = \text{IFFT}(X_{freq} \odot \text{Mask}) \tag{16}$$

**自适应策略**：
- 早期：小阈值($\tau=7$)，轻度滤波，保持结构
- 后期：大阈值($\tau=12$)，强度滤波，增强水纹理

### 3.3 完整算法流程

EvoTune的完整前向传播可表示为：
$$\hat{x}_{t-1} = \text{EvoTune}(x_t, t) \tag{17}$$

具体步骤：
1. **参数调谐**：$(b_1(t), s_1(t), s_2(t)) = \text{Scheduler}(t)$
2. **Agent注意力**：$h_1 = \text{WaterOilAttention}(x_t, w_{attn}(t))$
3. **频域增强**：$h_2 = \text{AdaptiveFourier}(h_1, s_1(t), s_2(t))$
4. **UNet处理**：$\hat{x}_{t-1} = \text{UNet}_{original}(h_2, t)$

### 3.4 关键参数演化

表1展示了关键参数在不同时间步的演化：

| 时间步 | 阶段 | $b_1(t)$ | $s_1(t)$ | $w_{attn}(t)$ | 主要作用 |
|--------|------|----------|----------|---------------|----------|
| 900 | 早期 | 1.610 | 0.800 | 0.504 | 建立基础结构 |
| 500 | 中期 | 1.507 | 0.806 | 0.584 | 平衡过渡 |
| 100 | 后期 | 1.500 | 0.888 | 0.916 | 优化水纹理细节 |

### 3.5 算法复杂度分析

**时间复杂度**：$O(N \log N)$，其中$N = H \times W$
- 参数调谐：$O(1)$
- Agent Attention：$O(N \times 49) \approx O(N)$
- 频域处理：$O(N \log N)$

**空间复杂度**：$O(N)$，无显著额外开销

由于Agent数量固定为49，且频域处理可并行化，EvoTune引入的计算开销相对较小。

### 3.6 理论保证

**参数有界性**：所有调谐参数都在有界区间内变化：
$$b_1(t) \in [1.5, 1.575], \quad s_1(t) \in [0.8, 0.888]$$

**连续性**：基于连续sigmoid函数的参数调谐保证了平滑的参数演化。

**单调性**：相位函数的单调性确保了参数演化的稳定性和可预测性。

### 3.7 与现有方法的区别

| 方法 | 参数调整 | 注意力机制 | 频域处理 |
|------|----------|------------|----------|
| FreeU | 固定参数 | 无 | 固定滤波 |
| Agent Attention | 无 | 通用场景 | 无 |
| **EvoTune** | **动态调谐** | **水油专用** | **自适应滤波** |

EvoTune的核心优势在于将三个创新点有机结合，形成了针对积水干扰场景的端到端优化框架。

---

**图1说明**：EvoTune网络结构图展示了三个核心创新模块的数据流向。输入的噪声图像和时间步经过动态时间步调谐模块生成自适应参数，然后依次通过水油专用Agent Attention和自适应频域处理模块，最终输出增强特征送入原始UNet进行去噪处理。
