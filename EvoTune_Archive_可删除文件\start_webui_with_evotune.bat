@echo off
echo ========================================
echo    启动EvoTune增强版SD WebUI
echo ========================================
echo.

cd /d "h:\StableDiffusion_Gaijin\sd-webui-aki-v4.4"

echo 检查EvoTune扩展...
if exist "extensions\evotune\EvoTune_UNetModel.py" (
    echo EvoTune扩展已安装 ✓
) else (
    echo 警告: EvoTune扩展未找到！
    echo 请先运行 install_evotune_to_webui.py
    pause
    exit
)

echo.
echo 启动WebUI...
echo 浏览器将自动打开 http://localhost:7860
echo.

set COMMANDLINE_ARGS=--xformers --api --listen
python launch.py %COMMANDLINE_ARGS%

pause
