# EvoTune - Advanced Diffusion Model Timestep Tuning

EvoTune是一个专为变电设备渗漏油检测设计的Stable Diffusion增强插件，通过时间步感知的动态调谐机制生成高质量的积水干扰训练数据。

## 🎯 核心特性

### 技术创新
- **时间步感知调谐**：根据扩散过程的不同阶段动态调整处理策略
- **Agent Attention机制**：49-token空间注意力，专注水油区域识别
- **自适应频域处理**：分层滤波器优化水纹理的频域特性
- **统一特征调制**：门控融合机制实现智能特征选择

### 系统优势
- **零侵入集成**：不修改SD WebUI核心代码，完全兼容
- **性能监控**：实时监控计算开销和内存使用
- **故障隔离**：EvoTune问题不影响原始功能
- **渐进部署**：可选择性启用调制点

## 📁 文件结构

```
h:\StableDiffusion_Gaijin\extensions\evotune\
├── __init__.py                    # 扩展入口
├── README.md                      # 本文档
├── requirements.txt               # 依赖列表
├── scripts/
│   └── evotune_script.py         # SD WebUI集成脚本
├── modules/
│   ├── __init__.py
│   ├── evotune_controller.py     # 核心控制器
│   ├── evotune_wrapper.py        # UNet包装器
│   ├── agent_attention.py        # Agent Attention实现
│   ├── fourier_processor.py      # 频域处理器
│   ├── timestep_scheduler.py     # 时间步调谐器
│   └── utils.py                  # 工具函数
├── ui/
│   └── evotune_ui.py            # 用户界面
└── tests/
    ├── test_components.py        # 单元测试
    └── test_integration.py       # 集成测试
```

## 🚀 安装指南

### 1. 环境要求
- Python 3.8+
- PyTorch 1.12+
- CUDA 11.0+ (推荐)
- Stable Diffusion WebUI (A启动器.exe)

### 2. 依赖安装
```bash
cd h:\StableDiffusion_Gaijin\extensions\evotune
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple
```

### 3. 验证安装
```bash
# 运行单元测试
python tests/test_components.py

# 运行集成测试
python tests/test_integration.py
```

## ⚙️ 配置说明

### 基础参数配置

#### 骨干特征权重 (Backbone Features)
- **b1_range**: (1.0, 1.3) - 编码器-解码器连接点权重范围
- **b2_range**: (0.8, 1.2) - 跳跃连接权重范围

#### 频域参数 (Frequency Domain)
- **s1_range**: (0.6, 1.0) - 低频滤波参数（积水结构）
- **s2_range**: (0.4, 0.8) - 中频滤波参数（水纹理）

#### 注意力参数 (Agent Attention)
- **w_attn_range**: (0.5, 0.95) - 注意力权重范围
- **num_agents**: 49 - Agent token数量 (7×7网格)
- **agent_dim**: 64 - Agent token维度

### 高级配置

#### 调制点配置
```json
{
  "mid_block": {
    "type": "backbone", 
    "enabled": true, 
    "priority": 1
  },
  "up_blocks.0.resnets.0": {
    "type": "skip", 
    "enabled": true, 
    "priority": 2
  }
}
```

#### 性能优化
- **target_channels**: 320 - 处理通道数
- **performance_monitoring**: true - 性能监控
- **memory_optimization**: true - 内存优化

## 🎮 使用方法

### 1. WebUI界面使用

1. 启动SD WebUI：运行 `A启动器.exe`
2. 在Scripts面板找到"EvoTune"选项
3. 勾选"Enable EvoTune"启用功能
4. 调整参数或选择预设配置
5. 正常生成图像，EvoTune将自动应用

### 2. 编程接口使用

```python
from evotune import EvoTuneWrapper, wrap_unet_with_evotune

# 包装现有UNet
wrapped_unet = wrap_unet_with_evotune(
    original_unet, 
    enabled=True,
    target_channels=320
)

# 使用包装后的UNet
output = wrapped_unet(sample, timestep, encoder_hidden_states)
```

### 3. 参数调优建议

#### 保守配置 (Conservative)
适用于稳定性优先的场景：
```python
params = {
    'b1_range': (1.0, 1.2),
    'b2_range': (0.9, 1.1),
    's1_range': (0.7, 0.9),
    's2_range': (0.5, 0.7),
    'w_attn_range': (0.6, 0.8)
}
```

#### 平衡配置 (Balanced)
推荐的默认配置：
```python
params = {
    'b1_range': (1.0, 1.3),
    'b2_range': (0.8, 1.2),
    's1_range': (0.6, 1.0),
    's2_range': (0.4, 0.8),
    'w_attn_range': (0.5, 0.95)
}
```

#### 激进配置 (Aggressive)
适用于追求最大效果的场景：
```python
params = {
    'b1_range': (1.1, 1.5),
    'b2_range': (0.7, 1.3),
    's1_range': (0.5, 1.2),
    's2_range': (0.3, 0.9),
    'w_attn_range': (0.4, 1.0)
}
```

## 📊 性能监控

### 实时监控指标
- **计算开销**: EvoTune相对原始UNet的额外时间
- **内存使用**: 峰值内存占用情况
- **调制效果**: 各调制点的激活状态

### 性能优化建议
1. **GPU内存不足**：减少batch_size或降低target_channels
2. **计算速度慢**：禁用部分调制点或降低agent数量
3. **效果不明显**：增加参数范围或启用更多调制点

## 🔧 故障排除

### 常见问题

#### 1. 马赛克现象
**症状**: 生成图像出现马赛克或块状artifacts
**原因**: 特征维度不匹配或内存布局问题
**解决**: 
```python
# 检查特征维度
wrapper.get_modulation_status()

# 重置配置
wrapper.configure_modulation_points({
    'mid_block': {'enabled': False}
})
```

#### 2. 内存溢出 (OOM)
**症状**: CUDA out of memory错误
**原因**: 批次大小过大或特征通道数过高
**解决**:
```python
# 降低目标通道数
wrapper = EvoTuneWrapper(unet, target_channels=256)

# 启用内存优化
wrapper.performance_monitoring = True
```

#### 3. 生成速度慢
**症状**: 图像生成时间显著增加
**原因**: EvoTune计算开销过高
**解决**:
```python
# 检查性能统计
stats = wrapper.get_performance_stats()
print(f"Overhead: {stats.get('evotune_overhead', 0):.1f}%")

# 禁用部分调制点
wrapper.configure_modulation_points({
    'up_blocks.2.resnets.0': {'enabled': False}
})
```

#### 4. 效果不明显
**症状**: 启用EvoTune后积水效果改善不明显
**原因**: 参数设置过于保守
**解决**:
```python
# 使用激进配置
wrapper.evotune_controller.timestep_scheduler.b1_range = (1.1, 1.5)
wrapper.evotune_controller.timestep_scheduler.s1_range = (0.5, 1.2)
```

### 调试工具

#### 1. 日志分析
```python
import logging
logging.getLogger('evotune').setLevel(logging.DEBUG)
```

#### 2. 可视化调试
```python
from evotune.modules.utils import create_debug_visualization

# 可视化中间特征
create_debug_visualization(
    tensor, 
    title="EvoTune Feature Map",
    save_path="debug_feature.png"
)
```

#### 3. 性能分析
```python
from evotune.modules.utils import benchmark_function

# 基准测试
results = benchmark_function(
    wrapper.forward, 
    sample, timestep, 
    num_runs=10
)
print(f"Average time: {results['mean_time']:.4f}s")
```

## 📈 技术原理

### 核心算法

EvoTune基于以下数学公式实现：

#### 时间步调谐 (公式1-4)
```
φ_early(t̃) = cos(π * t̃ / 2)
φ_late(t̃) = sin(π * t̃ / 2)
b(t) = b₀ + α · φ(t) ⊙ c
```

#### Agent Attention (公式5-7)
```
A = AdaptivePool(H_encoder)
A_agent = softmax(Q·K^T / √d) · V
Y_out = Y × w_attn(t)
```

#### 频域处理 (公式8-9)
```
τ(t) = 7 + ⌊5(1 - t̃)⌋
M(t) = {s₁(t), s₂(t), 1} 按频率分层
H_fourier = IFFT(FFT(H) ⊙ M(t))
```

#### 统一调制 (公式9a-9i)
```
G = σ(Conv([H_input, H_fourier]))
H_enhanced = H_input ⊙ (1 - G ⊙ b(t)) + F ⊙ (G ⊙ b(t))
```

### 与传统方法的区别

| 特性 | 传统FreeU | EvoTune |
|------|-----------|---------|
| 参数类型 | 固定参数 | 时间步感知动态参数 |
| 处理机制 | 简单频域缩放 | Agent Attention + 频域 + 门控融合 |
| 应用场景 | 通用图像生成 | 专用积水场景优化 |
| 集成方式 | 修改UNet代码 | 零侵入包装器 |

## 📚 API参考

### EvoTuneController
```python
class EvoTuneController(nn.Module):
    def __init__(self, channels, total_timesteps=1000)
    def modulate_backbone_features(self, h_backbone, timesteps)
    def modulate_skip_features(self, h_skip, timesteps)
```

### EvoTuneWrapper  
```python
class EvoTuneWrapper(nn.Module):
    def __init__(self, original_unet, enabled=True)
    def enable_evotune(self)
    def disable_evotune(self)
    def get_performance_stats(self)
```

### TimestepScheduler
```python
class TimestepScheduler(nn.Module):
    def __init__(self, total_timesteps=1000)
    def get_adaptive_params(self, timesteps)
```

## 🤝 贡献指南

### 开发环境设置
```bash
git clone <repository>
cd evotune
pip install -e .
pip install -r requirements-dev.txt
```

### 代码规范
- 遵循PEP 8代码风格
- 添加类型注解
- 编写单元测试
- 更新文档

### 提交流程
1. Fork项目
2. 创建特性分支
3. 提交更改
4. 运行测试
5. 创建Pull Request

## 📄 许可证

本项目采用MIT许可证 - 详见LICENSE文件

## 📞 支持与反馈

- **技术问题**: 提交GitHub Issue
- **功能建议**: 创建Feature Request  
- **使用交流**: 加入讨论群组

---

**EvoTune** - 让积水干扰生成更智能、更高效！
