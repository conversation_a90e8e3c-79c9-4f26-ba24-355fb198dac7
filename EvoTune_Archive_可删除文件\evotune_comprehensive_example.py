"""
EvoTune综合使用示例
展示如何使用EvoTune模型进行积水干扰场景下的油污生成

作者: AI Assistant
日期: 2025-01-11
"""

import torch
import torch.nn.functional as F
import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, List, Optional, Tuple
import os
import time

# 导入EvoTune相关模块
from EvoTune_UNetModel import EvoTune_UNetModel
from evotune_lora_adapter import EvoTuneLoRAAdapter, create_evotune_lora_config
from evotune_evaluation_metrics import EvoTuneEvaluator
from sd_webui_evotune_integration import EvoTuneSDUnet


class EvoTuneWorkflow:
    """
    EvoTune完整工作流程
    包含模型创建、训练、评估和应用的完整流程
    """
    
    def __init__(self, config: Optional[Dict] = None):
        self.config = config or self.get_default_config()
        self.model = None
        self.lora_adapter = None
        self.evaluator = EvoTuneEvaluator()
        self.training_history = []
        
    @staticmethod
    def get_default_config():
        """获取默认配置"""
        return {
            'model_config': {
                'b1': 1.5,
                'b2': 1.2,
                's1': 0.8,
                's2': 0.5,
                'model_channels': 64,
                'num_classes': None,
                'resolution': 32,
                'use_agent_attention': False,  # 暂时禁用直到修复
                'use_adaptive_fourier': True,
                'max_timesteps': 1000
            },
            'lora_config': {
                'rank': 4,
                'alpha': 1.0,
                'dropout': 0.1
            },
            'training_config': {
                'learning_rate': 1e-4,
                'batch_size': 4,
                'num_epochs': 10,
                'save_every': 5
            }
        }
    
    def create_model(self):
        """创建EvoTune模型"""
        print("创建EvoTune模型...")
        self.model = EvoTune_UNetModel(**self.config['model_config'])
        
        total_params = sum(p.numel() for p in self.model.parameters())
        print(f"模型创建完成，总参数量: {total_params:,}")
        
        return self.model
    
    def apply_lora(self):
        """应用LoRA适配器"""
        if self.model is None:
            raise ValueError("请先创建模型")
        
        print("应用LoRA适配器...")
        self.lora_adapter = EvoTuneLoRAAdapter(
            model=self.model,
            **self.config['lora_config']
        )
        
        # 冻结基础模型，只训练LoRA
        self.lora_adapter.freeze_base_model()
        
        trainable_params = sum(p.numel() for p in self.model.parameters() if p.requires_grad)
        total_params = sum(p.numel() for p in self.model.parameters())
        efficiency = trainable_params / total_params * 100
        
        print(f"LoRA适配器应用完成")
        print(f"可训练参数: {trainable_params:,} ({efficiency:.2f}%)")
        
        return self.lora_adapter
    
    def simulate_training_step(self, batch_data: Dict[str, torch.Tensor]) -> Dict[str, float]:
        """
        模拟训练步骤
        在实际应用中，这里会连接到真实的训练数据和损失函数
        """
        if self.model is None:
            raise ValueError("请先创建模型")
        
        # 模拟前向传播
        images = batch_data['images']
        timesteps = batch_data['timesteps']
        
        # 生成预测
        with torch.no_grad():
            predictions = self.model(images, timesteps=timesteps)
        
        # 模拟损失计算
        if 'targets' in batch_data:
            targets = batch_data['targets']
            mse_loss = F.mse_loss(predictions, targets)
            
            # 计算评估指标
            metrics = self.evaluator.evaluate_generation_quality(
                predictions, targets
            )
            
            return {
                'loss': mse_loss.item(),
                'ssim': metrics['ssim'],
                'psnr': metrics['psnr']
            }
        else:
            return {'loss': 0.0, 'ssim': 0.0, 'psnr': 0.0}
    
    def run_training_simulation(self, num_steps: int = 10):
        """运行训练模拟"""
        print(f"开始训练模拟 ({num_steps} 步)...")
        
        for step in range(num_steps):
            # 生成模拟数据
            batch_data = self.generate_mock_batch()
            
            # 训练步骤
            metrics = self.simulate_training_step(batch_data)
            
            # 记录历史
            self.training_history.append({
                'step': step,
                **metrics
            })
            
            if step % 5 == 0:
                print(f"步骤 {step}: Loss={metrics['loss']:.4f}, "
                      f"SSIM={metrics['ssim']:.4f}, PSNR={metrics['psnr']:.4f}")
        
        print("训练模拟完成")
    
    def generate_mock_batch(self) -> Dict[str, torch.Tensor]:
        """生成模拟批次数据"""
        batch_size = self.config['training_config']['batch_size']
        
        # 模拟输入图像（带有积水和油污的场景）
        images = torch.randn(batch_size, 3, 256, 256)
        
        # 添加一些结构化的模式来模拟水面和油污
        for i in range(batch_size):
            # 模拟水面区域（较平滑）
            water_mask = torch.rand(1, 256, 256) > 0.7
            images[i, :, water_mask.squeeze()] *= 0.5
            
            # 模拟油污区域（更多纹理）
            oil_mask = torch.rand(1, 256, 256) > 0.8
            images[i, :, oil_mask.squeeze()] += torch.randn_like(images[i, :, oil_mask.squeeze()]) * 0.3
        
        # 模拟时间步
        timesteps = torch.randint(0, 1000, (batch_size,))
        
        # 模拟目标图像
        targets = images + torch.randn_like(images) * 0.1
        
        return {
            'images': images,
            'timesteps': timesteps,
            'targets': targets
        }
    
    def evaluate_model(self, test_data: Optional[Dict[str, torch.Tensor]] = None):
        """评估模型性能"""
        if self.model is None:
            raise ValueError("请先创建模型")
        
        print("评估模型性能...")
        
        if test_data is None:
            test_data = self.generate_mock_batch()
        
        with torch.no_grad():
            predictions = self.model(
                test_data['images'], 
                timesteps=test_data['timesteps']
            )
        
        if 'targets' in test_data:
            metrics = self.evaluator.evaluate_generation_quality(
                predictions, test_data['targets']
            )
            
            print("评估结果:")
            for metric, value in metrics.items():
                print(f"  {metric}: {value:.4f}")
            
            return metrics
        else:
            print("无目标数据，跳过定量评估")
            return {}
    
    def optimize_parameters(self):
        """优化EvoTune参数"""
        print("开始参数优化...")
        
        best_params = None
        best_score = float('-inf')
        
        # 参数搜索范围
        param_ranges = {
            'b1': [1.2, 1.5, 1.8],
            'b2': [1.0, 1.2, 1.4],
            's1': [0.6, 0.8, 1.0],
            's2': [0.4, 0.5, 0.6]
        }
        
        # 简单的网格搜索
        for b1 in param_ranges['b1']:
            for b2 in param_ranges['b2']:
                for s1 in param_ranges['s1']:
                    for s2 in param_ranges['s2']:
                        # 更新模型参数
                        if hasattr(self.model, 'set_adaptive_params'):
                            self.model.set_adaptive_params(b1=b1, b2=b2, s1=s1, s2=s2)
                        
                        # 评估当前参数组合
                        test_data = self.generate_mock_batch()
                        metrics = self.evaluate_model(test_data)
                        
                        # 计算综合分数
                        score = metrics.get('ssim', 0) + metrics.get('psnr', 0) / 10
                        
                        if score > best_score:
                            best_score = score
                            best_params = {'b1': b1, 'b2': b2, 's1': s1, 's2': s2}
        
        print(f"最佳参数: {best_params}")
        print(f"最佳分数: {best_score:.4f}")
        
        # 应用最佳参数
        if best_params and hasattr(self.model, 'set_adaptive_params'):
            self.model.set_adaptive_params(**best_params)
        
        return best_params
    
    def save_model(self, save_path: str):
        """保存模型"""
        if self.model is None:
            raise ValueError("请先创建模型")
        
        save_dict = {
            'model_state_dict': self.model.state_dict(),
            'config': self.config,
            'training_history': self.training_history
        }
        
        if self.lora_adapter is not None:
            lora_path = save_path.replace('.pth', '_lora.pth')
            self.lora_adapter.save_lora_weights(lora_path)
            save_dict['lora_path'] = lora_path
        
        torch.save(save_dict, save_path)
        print(f"模型已保存到: {save_path}")
    
    def load_model(self, load_path: str):
        """加载模型"""
        checkpoint = torch.load(load_path, map_location='cpu')
        
        # 重新创建模型
        self.config = checkpoint['config']
        self.create_model()
        
        # 加载权重
        self.model.load_state_dict(checkpoint['model_state_dict'])
        
        # 加载训练历史
        self.training_history = checkpoint.get('training_history', [])
        
        # 如果有LoRA权重，也加载
        if 'lora_path' in checkpoint and os.path.exists(checkpoint['lora_path']):
            self.apply_lora()
            self.lora_adapter.load_lora_weights(checkpoint['lora_path'])
        
        print(f"模型已从 {load_path} 加载")
    
    def plot_training_history(self):
        """绘制训练历史"""
        if not self.training_history:
            print("无训练历史数据")
            return
        
        steps = [h['step'] for h in self.training_history]
        losses = [h['loss'] for h in self.training_history]
        ssims = [h['ssim'] for h in self.training_history]
        psnrs = [h['psnr'] for h in self.training_history]
        
        fig, axes = plt.subplots(1, 3, figsize=(15, 5))
        
        axes[0].plot(steps, losses)
        axes[0].set_title('Training Loss')
        axes[0].set_xlabel('Step')
        axes[0].set_ylabel('Loss')
        
        axes[1].plot(steps, ssims)
        axes[1].set_title('SSIM')
        axes[1].set_xlabel('Step')
        axes[1].set_ylabel('SSIM')
        
        axes[2].plot(steps, psnrs)
        axes[2].set_title('PSNR')
        axes[2].set_xlabel('Step')
        axes[2].set_ylabel('PSNR')
        
        plt.tight_layout()
        plt.savefig('evotune_training_history.png')
        plt.show()
        print("训练历史图表已保存为 evotune_training_history.png")


def main():
    """主函数：演示完整的EvoTune工作流程"""
    print("=== EvoTune 综合使用示例 ===")
    
    # 创建工作流程
    workflow = EvoTuneWorkflow()
    
    # 1. 创建模型
    workflow.create_model()
    
    # 2. 应用LoRA适配器
    workflow.apply_lora()
    
    # 3. 运行训练模拟
    workflow.run_training_simulation(num_steps=20)
    
    # 4. 评估模型
    workflow.evaluate_model()
    
    # 5. 参数优化
    best_params = workflow.optimize_parameters()
    
    # 6. 保存模型
    workflow.save_model('evotune_model.pth')
    
    # 7. 绘制训练历史（需要matplotlib）
    try:
        workflow.plot_training_history()
    except ImportError:
        print("matplotlib未安装，跳过绘图")
    
    print("\n=== 工作流程完成 ===")
    print("EvoTune模型已准备就绪，可用于积水干扰场景下的油污生成任务")
    
    # 清理测试文件
    if os.path.exists('evotune_model.pth'):
        os.remove('evotune_model.pth')
    if os.path.exists('evotune_model_lora.pth'):
        os.remove('evotune_model_lora.pth')
    print("测试文件已清理")


if __name__ == "__main__":
    main()
