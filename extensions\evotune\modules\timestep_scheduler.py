"""
EvoTune时间步调谐器
实现公式(1-4)的相位函数和动态参数生成
"""

import torch
import torch.nn as nn
import math
from typing import Tuple, Dict, Any
import logging

logger = logging.getLogger(__name__)

class TimestepScheduler(nn.Module):
    """
    EvoTune时间步调谐器
    根据扩散时间步生成动态参数
    """
    
    def __init__(self, 
                 total_timesteps: int = 1000,
                 b1_range: Tuple[float, float] = (1.0, 1.3),
                 b2_range: Tuple[float, float] = (0.8, 1.2),
                 s1_range: Tuple[float, float] = (0.6, 1.0),
                 s2_range: Tuple[float, float] = (0.4, 0.8),
                 w_attn_range: Tuple[float, float] = (0.5, 0.95)):
        """
        初始化时间步调谐器
        
        Args:
            total_timesteps: 总时间步数
            b1_range: 骨干特征权重范围
            b2_range: 跳跃连接权重范围  
            s1_range: 低频滤波参数范围
            s2_range: 中频滤波参数范围
            w_attn_range: 注意力权重范围
        """
        super().__init__()
        
        self.total_timesteps = total_timesteps
        self.b1_range = b1_range
        self.b2_range = b2_range
        self.s1_range = s1_range
        self.s2_range = s2_range
        self.w_attn_range = w_attn_range
        
        # 注册为buffer，不参与梯度更新但会保存到模型中
        self.register_buffer('pi', torch.tensor(math.pi))
        
        logger.info(f"TimestepScheduler initialized with {total_timesteps} timesteps")
    
    def normalize_timestep(self, t: torch.Tensor) -> torch.Tensor:
        """
        公式(1): 时间步标准化
        t̃ = t / T, where T is total timesteps
        
        Args:
            t: 原始时间步 [B] or scalar
            
        Returns:
            t_normalized: 标准化时间步 [B] or scalar, range [0, 1]
        """
        if isinstance(t, (int, float)):
            t = torch.tensor(t, device=self.pi.device)
        
        # 确保时间步在有效范围内
        t = torch.clamp(t, 0, self.total_timesteps - 1)
        
        # 标准化到[0, 1]
        t_normalized = t.float() / (self.total_timesteps - 1)
        
        return t_normalized
    
    def compute_phase_functions(self, t_normalized: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        计算相位函数
        φ_early = cos(π * t̃ / 2)  # 早期阶段权重
        φ_late = sin(π * t̃ / 2)   # 后期阶段权重
        
        Args:
            t_normalized: 标准化时间步 [B] or scalar
            
        Returns:
            phi_early: 早期相位函数 [B] or scalar
            phi_late: 后期相位函数 [B] or scalar
        """
        phi_early = torch.cos(self.pi * t_normalized / 2)
        phi_late = torch.sin(self.pi * t_normalized / 2)
        
        return phi_early, phi_late
    
    def generate_backbone_weights(self, phi_early: torch.Tensor, phi_late: torch.Tensor) -> torch.Tensor:
        """
        公式(2): 骨干特征权重生成
        b(t) = [b₁(t), b₂(t)]ᵀ
        b₁(t) = b₁_min + (b₁_max - b₁_min) * φ_late
        b₂(t) = b₂_min + (b₂_max - b₂_min) * φ_late
        
        Args:
            phi_early: 早期相位函数
            phi_late: 后期相位函数
            
        Returns:
            backbone_weights: [b1(t), b2(t)] shape [..., 2]
        """
        b1_min, b1_max = self.b1_range
        b2_min, b2_max = self.b2_range
        
        b1_t = b1_min + (b1_max - b1_min) * phi_late
        b2_t = b2_min + (b2_max - b2_min) * phi_late
        
        # 堆叠为向量
        backbone_weights = torch.stack([b1_t, b2_t], dim=-1)
        
        return backbone_weights
    
    def generate_frequency_weights(self, phi_early: torch.Tensor, phi_late: torch.Tensor) -> torch.Tensor:
        """
        公式(3): 频域参数生成
        s(t) = [s₁(t), s₂(t)]ᵀ
        s₁(t) = s₁_min + (s₁_max - s₁_min) * φ_late  # 低频控制
        s₂(t) = s₂_min + (s₂_max - s₂_min) * φ_late  # 中频控制
        
        Args:
            phi_early: 早期相位函数
            phi_late: 后期相位函数
            
        Returns:
            frequency_weights: [s1(t), s2(t)] shape [..., 2]
        """
        s1_min, s1_max = self.s1_range
        s2_min, s2_max = self.s2_range
        
        s1_t = s1_min + (s1_max - s1_min) * phi_late
        s2_t = s2_min + (s2_max - s2_min) * phi_late
        
        # 堆叠为向量
        frequency_weights = torch.stack([s1_t, s2_t], dim=-1)
        
        return frequency_weights
    
    def generate_attention_weight(self, phi_early: torch.Tensor, phi_late: torch.Tensor) -> torch.Tensor:
        """
        公式(4): 注意力权重生成
        w_attn(t) = w_min + (w_max - w_min) * φ_late
        
        Args:
            phi_early: 早期相位函数
            phi_late: 后期相位函数
            
        Returns:
            attention_weight: w_attn(t) scalar or [B]
        """
        w_min, w_max = self.w_attn_range
        
        w_attn_t = w_min + (w_max - w_min) * phi_late
        
        return w_attn_t
    
    def get_adaptive_params(self, timesteps: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        获取时间步感知的自适应参数
        
        Args:
            timesteps: 时间步张量 [B] or scalar
            
        Returns:
            params: 包含所有动态参数的字典
                - b_weights: [b1(t), b2(t)] shape [B, 2] or [2]
                - s_weights: [s1(t), s2(t)] shape [B, 2] or [2] 
                - w_attn: w_attn(t) shape [B] or scalar
                - phi_early: 早期相位函数
                - phi_late: 后期相位函数
                - t_normalized: 标准化时间步
        """
        # 公式(1): 时间步标准化
        t_normalized = self.normalize_timestep(timesteps)
        
        # 计算相位函数
        phi_early, phi_late = self.compute_phase_functions(t_normalized)
        
        # 公式(2): 生成骨干特征权重
        b_weights = self.generate_backbone_weights(phi_early, phi_late)
        
        # 公式(3): 生成频域参数
        s_weights = self.generate_frequency_weights(phi_early, phi_late)
        
        # 公式(4): 生成注意力权重
        w_attn = self.generate_attention_weight(phi_early, phi_late)
        
        params = {
            'b_weights': b_weights,      # [b1(t), b2(t)]
            's_weights': s_weights,      # [s1(t), s2(t)]
            'w_attn': w_attn,           # w_attn(t)
            'phi_early': phi_early,      # 早期相位
            'phi_late': phi_late,        # 后期相位
            't_normalized': t_normalized # 标准化时间步
        }
        
        return params
    
    def forward(self, timesteps: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        前向传播，获取自适应参数
        
        Args:
            timesteps: 时间步张量
            
        Returns:
            自适应参数字典
        """
        return self.get_adaptive_params(timesteps)


class TimestepContextManager:
    """
    时间步上下文管理器
    解决HOOK机制中时间步信息缺失的问题
    """
    
    def __init__(self):
        self.current_timestep = None
        self.current_params = None
        self._context_stack = []
    
    def set_context(self, timestep: torch.Tensor, params: Dict[str, torch.Tensor]):
        """设置当前时间步上下文"""
        self.current_timestep = timestep
        self.current_params = params
    
    def get_context(self) -> Tuple[torch.Tensor, Dict[str, torch.Tensor]]:
        """获取当前时间步上下文"""
        return self.current_timestep, self.current_params
    
    def push_context(self, timestep: torch.Tensor, params: Dict[str, torch.Tensor]):
        """压入上下文栈"""
        self._context_stack.append((self.current_timestep, self.current_params))
        self.set_context(timestep, params)
    
    def pop_context(self):
        """弹出上下文栈"""
        if self._context_stack:
            self.current_timestep, self.current_params = self._context_stack.pop()
    
    def clear_context(self):
        """清空上下文"""
        self.current_timestep = None
        self.current_params = None
        self._context_stack.clear()


# 全局上下文管理器实例
timestep_context = TimestepContextManager()
