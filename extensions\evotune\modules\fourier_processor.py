"""
EvoTune自适应频域处理器
实现公式(8-9)的分层滤波器和FFT/IFFT处理
"""

import torch
import torch.nn as nn
import torch.fft as fft
from typing import Tuple, Optional
import logging

logger = logging.getLogger(__name__)

class AdaptiveFourierProcessor(nn.Module):
    """
    自适应频域处理器
    实现时间步感知的分层频域滤波
    """
    
    def __init__(self, 
                 channels: int,
                 base_threshold: int = 7,
                 threshold_range: int = 5):
        """
        初始化自适应频域处理器
        
        Args:
            channels: 输入特征通道数
            base_threshold: 基础阈值参数
            threshold_range: 阈值变化范围
        """
        super().__init__()
        
        self.channels = channels
        self.base_threshold = base_threshold
        self.threshold_range = threshold_range
        
        # 用于处理非2次幂尺寸的填充层
        self.padding_handler = PaddingHandler()
        
        logger.info(f"AdaptiveFourierProcessor initialized for {channels} channels")
    
    def compute_dynamic_threshold(self, t_normalized: torch.Tensor) -> torch.Tensor:
        """
        公式(8a): 计算动态阈值
        τ(t) = 7 + ⌊5(1 - t̃)⌋
        
        Args:
            t_normalized: 标准化时间步 [B] or scalar, range [0, 1]
            
        Returns:
            threshold: 动态阈值 [B] or scalar
        """
        threshold = self.base_threshold + torch.floor(
            self.threshold_range * (1 - t_normalized)
        ).int()
        
        return threshold
    
    def create_adaptive_filter(self, 
                             H: int, 
                             W: int, 
                             threshold: torch.Tensor,
                             s1: torch.Tensor, 
                             s2: torch.Tensor,
                             device: torch.device) -> torch.Tensor:
        """
        公式(8b): 创建自适应频域滤波器
        
        M_{u,v}(t) = {
            s₁(t), if max(|u - u_c|, |v - v_c|) ≤ τ(t)
            s₂(t), if τ(t) < max(|u - u_c|, |v - v_c|) ≤ 2τ(t)  
            1,     otherwise
        }
        
        Args:
            H, W: 特征图尺寸
            threshold: 动态阈值 τ(t)
            s1: 低频滤波参数
            s2: 中频滤波参数
            device: 设备
            
        Returns:
            filter_mask: 自适应滤波器 [1, 1, H, W] or [B, 1, H, W]
        """
        # 频域中心坐标
        u_c, v_c = H // 2, W // 2
        
        # 创建坐标网格
        u = torch.arange(H, device=device).float() - u_c
        v = torch.arange(W, device=device).float() - v_c
        u_grid, v_grid = torch.meshgrid(u, v, indexing='ij')
        
        # 计算到中心的距离 (使用L∞范数)
        distance = torch.maximum(torch.abs(u_grid), torch.abs(v_grid))
        
        # 处理批次维度
        if threshold.dim() > 0:  # [B]
            B = threshold.shape[0]
            distance = distance.unsqueeze(0).expand(B, -1, -1)  # [B, H, W]
            threshold = threshold.view(B, 1, 1)  # [B, 1, 1]
            s1 = s1.view(B, 1, 1)  # [B, 1, 1]
            s2 = s2.view(B, 1, 1)  # [B, 1, 1]
            filter_shape = (B, 1, H, W)
        else:  # scalar
            filter_shape = (1, 1, H, W)
        
        # 初始化滤波器为1
        filter_mask = torch.ones(filter_shape, device=device, dtype=torch.float32)
        
        # 应用分层滤波
        # 低频区域: distance ≤ threshold
        low_freq_mask = distance <= threshold
        filter_mask[low_freq_mask.unsqueeze(1 if threshold.dim() == 0 else 0)] = s1.flatten()
        
        # 中频区域: threshold < distance ≤ 2*threshold
        mid_freq_mask = (distance > threshold) & (distance <= 2 * threshold)
        filter_mask[mid_freq_mask.unsqueeze(1 if threshold.dim() == 0 else 0)] = s2.flatten()
        
        # 高频区域保持为1 (已经初始化为1)
        
        return filter_mask
    
    def safe_fft2d(self, x: torch.Tensor) -> torch.Tensor:
        """
        安全的2D FFT变换，处理非2次幂尺寸
        
        Args:
            x: 输入特征 [B, C, H, W]
            
        Returns:
            x_freq: 频域特征 [B, C, H, W] (complex)
        """
        try:
            # 确保输入是连续的
            if not x.is_contiguous():
                x = x.contiguous()
            
            # 执行FFT变换
            x_freq = fft.fft2(x, dim=(-2, -1))
            x_freq = fft.fftshift(x_freq, dim=(-2, -1))
            
            return x_freq
            
        except Exception as e:
            logger.warning(f"FFT failed, using padding strategy: {e}")
            # 使用填充策略处理
            x_padded, pad_info = self.padding_handler.pad_to_power_of_2(x)
            x_freq = fft.fft2(x_padded, dim=(-2, -1))
            x_freq = fft.fftshift(x_freq, dim=(-2, -1))
            # 裁剪回原始尺寸
            x_freq = self.padding_handler.unpad(x_freq, pad_info)
            return x_freq
    
    def safe_ifft2d(self, x_freq: torch.Tensor) -> torch.Tensor:
        """
        安全的2D IFFT变换
        
        Args:
            x_freq: 频域特征 [B, C, H, W] (complex)
            
        Returns:
            x: 空间域特征 [B, C, H, W] (real)
        """
        try:
            # 执行IFFT变换
            x_freq = fft.ifftshift(x_freq, dim=(-2, -1))
            x = fft.ifft2(x_freq, dim=(-2, -1)).real
            
            return x
            
        except Exception as e:
            logger.warning(f"IFFT failed: {e}")
            # 简单的降级策略：返回实部
            return x_freq.real
    
    def forward(self, 
                x: torch.Tensor, 
                s1: torch.Tensor, 
                s2: torch.Tensor, 
                t_normalized: torch.Tensor) -> torch.Tensor:
        """
        自适应频域处理前向传播
        
        公式(9b): H_freq = FFT2D(Y_out)
        公式(9c): H_filtered = H_freq ⊙ M(t)  
        公式(9d): H_fourier = IFFT2D(H_filtered)
        
        Args:
            x: 输入特征 [B, C, H, W]
            s1: 低频滤波参数 [B] or scalar
            s2: 中频滤波参数 [B] or scalar
            t_normalized: 标准化时间步 [B] or scalar
            
        Returns:
            output: 频域处理后的特征 [B, C, H, W]
        """
        B, C, H, W = x.shape
        device = x.device
        
        # 公式(8a): 计算动态阈值
        threshold = self.compute_dynamic_threshold(t_normalized)
        
        # 公式(9b): FFT变换到频域
        x_freq = self.safe_fft2d(x)
        
        # 公式(8b): 创建自适应滤波器
        filter_mask = self.create_adaptive_filter(H, W, threshold, s1, s2, device)
        
        # 公式(9c): 应用滤波器
        x_filtered = x_freq * filter_mask
        
        # 公式(9d): IFFT变换回空间域
        output = self.safe_ifft2d(x_filtered)
        
        return output


class PaddingHandler:
    """
    处理非2次幂尺寸的填充工具
    """
    
    def __init__(self):
        pass
    
    def next_power_of_2(self, n: int) -> int:
        """计算下一个2的幂"""
        return 2 ** (n - 1).bit_length()
    
    def pad_to_power_of_2(self, x: torch.Tensor) -> Tuple[torch.Tensor, dict]:
        """
        填充到2的幂尺寸
        
        Args:
            x: 输入张量 [B, C, H, W]
            
        Returns:
            x_padded: 填充后的张量
            pad_info: 填充信息，用于后续裁剪
        """
        B, C, H, W = x.shape
        
        # 计算目标尺寸
        target_H = self.next_power_of_2(H)
        target_W = self.next_power_of_2(W)
        
        # 计算填充量
        pad_H = target_H - H
        pad_W = target_W - W
        
        # 对称填充
        pad_top = pad_H // 2
        pad_bottom = pad_H - pad_top
        pad_left = pad_W // 2
        pad_right = pad_W - pad_left
        
        # 执行填充 (padding格式: left, right, top, bottom)
        x_padded = torch.nn.functional.pad(x, (pad_left, pad_right, pad_top, pad_bottom), mode='reflect')
        
        pad_info = {
            'original_shape': (H, W),
            'target_shape': (target_H, target_W),
            'pad_top': pad_top,
            'pad_bottom': pad_bottom,
            'pad_left': pad_left,
            'pad_right': pad_right
        }
        
        return x_padded, pad_info
    
    def unpad(self, x_padded: torch.Tensor, pad_info: dict) -> torch.Tensor:
        """
        根据填充信息裁剪回原始尺寸
        
        Args:
            x_padded: 填充后的张量
            pad_info: 填充信息
            
        Returns:
            x: 裁剪后的张量
        """
        H_orig, W_orig = pad_info['original_shape']
        pad_top = pad_info['pad_top']
        pad_left = pad_info['pad_left']
        
        # 裁剪回原始尺寸
        x = x_padded[..., pad_top:pad_top+H_orig, pad_left:pad_left+W_orig]
        
        return x
