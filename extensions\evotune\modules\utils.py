"""
EvoTune工具函数
"""

import torch
import torch.nn as nn
import logging
import os
import json
from typing import Dict, Any, Optional, Tuple, List
import numpy as np

logger = logging.getLogger(__name__)

def setup_logging(log_level: str = "INFO", log_file: Optional[str] = None):
    """
    设置日志系统
    
    Args:
        log_level: 日志级别
        log_file: 日志文件路径
    """
    level = getattr(logging, log_level.upper())
    
    # 创建格式器
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    
    # 配置根日志器
    root_logger = logging.getLogger()
    root_logger.setLevel(level)
    root_logger.addHandler(console_handler)
    
    # 文件处理器
    if log_file:
        file_handler = logging.FileHandler(log_file)
        file_handler.setFormatter(formatter)
        root_logger.addHandler(file_handler)
    
    logger.info(f"Logging setup complete, level={log_level}")


def check_cuda_availability() -> Dict[str, Any]:
    """
    检查CUDA可用性
    
    Returns:
        cuda_info: CUDA信息字典
    """
    info = {
        'available': torch.cuda.is_available(),
        'device_count': 0,
        'current_device': None,
        'memory_info': {},
        'device_properties': []
    }
    
    if info['available']:
        info['device_count'] = torch.cuda.device_count()
        info['current_device'] = torch.cuda.current_device()
        
        # 内存信息
        info['memory_info'] = {
            'allocated': torch.cuda.memory_allocated(),
            'cached': torch.cuda.memory_reserved(),
            'max_allocated': torch.cuda.max_memory_allocated()
        }
        
        # 设备属性
        for i in range(info['device_count']):
            props = torch.cuda.get_device_properties(i)
            info['device_properties'].append({
                'name': props.name,
                'total_memory': props.total_memory,
                'major': props.major,
                'minor': props.minor,
                'multi_processor_count': props.multi_processor_count
            })
    
    return info


def validate_tensor_shape(tensor: torch.Tensor, 
                         expected_shape: Tuple[int, ...],
                         name: str = "tensor") -> bool:
    """
    验证张量形状
    
    Args:
        tensor: 输入张量
        expected_shape: 期望形状（-1表示任意维度）
        name: 张量名称
        
    Returns:
        valid: 是否有效
    """
    if tensor.dim() != len(expected_shape):
        logger.error(f"{name} dimension mismatch: got {tensor.dim()}, expected {len(expected_shape)}")
        return False
    
    for i, (actual, expected) in enumerate(zip(tensor.shape, expected_shape)):
        if expected != -1 and actual != expected:
            logger.error(f"{name} shape mismatch at dim {i}: got {actual}, expected {expected}")
            return False
    
    return True


def safe_tensor_operation(operation, *args, **kwargs):
    """
    安全的张量操作包装器
    
    Args:
        operation: 要执行的操作
        *args: 位置参数
        **kwargs: 关键字参数
        
    Returns:
        result: 操作结果，失败时返回None
    """
    try:
        return operation(*args, **kwargs)
    except Exception as e:
        logger.error(f"Tensor operation failed: {e}")
        return None


def calculate_model_parameters(model: nn.Module) -> Dict[str, int]:
    """
    计算模型参数数量
    
    Args:
        model: PyTorch模型
        
    Returns:
        param_info: 参数信息
    """
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    return {
        'total_parameters': total_params,
        'trainable_parameters': trainable_params,
        'non_trainable_parameters': total_params - trainable_params
    }


def estimate_memory_usage(batch_size: int, 
                         channels: int, 
                         height: int, 
                         width: int,
                         dtype: torch.dtype = torch.float32) -> Dict[str, float]:
    """
    估算内存使用量
    
    Args:
        batch_size: 批次大小
        channels: 通道数
        height: 高度
        width: 宽度
        dtype: 数据类型
        
    Returns:
        memory_info: 内存信息（MB）
    """
    # 每个元素的字节数
    bytes_per_element = {
        torch.float32: 4,
        torch.float16: 2,
        torch.int32: 4,
        torch.int64: 8
    }.get(dtype, 4)
    
    # 基础张量大小
    base_size = batch_size * channels * height * width * bytes_per_element
    
    # 估算各组件内存使用
    memory_info = {
        'input_tensor': base_size / (1024**2),  # MB
        'agent_attention': base_size * 0.3 / (1024**2),  # 30%额外开销
        'fourier_processing': base_size * 0.5 / (1024**2),  # 50%额外开销（复数）
        'feature_fusion': base_size * 0.2 / (1024**2),  # 20%额外开销
        'gradients': base_size / (1024**2),  # 梯度存储
        'total_estimated': base_size * 3.0 / (1024**2)  # 总估算（3倍安全系数）
    }
    
    return memory_info


def create_debug_visualization(tensor: torch.Tensor, 
                             title: str = "Tensor Visualization",
                             save_path: Optional[str] = None) -> Optional[str]:
    """
    创建张量调试可视化
    
    Args:
        tensor: 输入张量
        title: 图表标题
        save_path: 保存路径
        
    Returns:
        image_path: 生成的图像路径
    """
    try:
        import matplotlib.pyplot as plt
        
        # 处理不同维度的张量
        if tensor.dim() == 4:  # [B, C, H, W]
            # 取第一个批次，第一个通道
            data = tensor[0, 0].detach().cpu().numpy()
        elif tensor.dim() == 3:  # [B, H, W] or [C, H, W]
            data = tensor[0].detach().cpu().numpy()
        elif tensor.dim() == 2:  # [H, W]
            data = tensor.detach().cpu().numpy()
        else:
            logger.warning(f"Unsupported tensor dimension: {tensor.dim()}")
            return None
        
        # 创建图表
        plt.figure(figsize=(10, 8))
        plt.imshow(data, cmap='viridis')
        plt.colorbar()
        plt.title(f"{title}\nShape: {tensor.shape}, Range: [{tensor.min():.3f}, {tensor.max():.3f}]")
        
        # 保存或显示
        if save_path:
            plt.savefig(save_path, dpi=150, bbox_inches='tight')
            plt.close()
            return save_path
        else:
            plt.show()
            return None
            
    except ImportError:
        logger.warning("Matplotlib not available for visualization")
        return None
    except Exception as e:
        logger.error(f"Visualization failed: {e}")
        return None


def save_config(config: Dict[str, Any], filepath: str):
    """
    保存配置到JSON文件
    
    Args:
        config: 配置字典
        filepath: 文件路径
    """
    try:
        # 确保目录存在
        os.makedirs(os.path.dirname(filepath), exist_ok=True)
        
        # 转换不可序列化的对象
        serializable_config = {}
        for key, value in config.items():
            if isinstance(value, torch.Tensor):
                serializable_config[key] = {
                    'type': 'tensor',
                    'shape': list(value.shape),
                    'dtype': str(value.dtype),
                    'data': value.tolist() if value.numel() < 100 else 'too_large'
                }
            elif isinstance(value, torch.dtype):
                serializable_config[key] = str(value)
            elif isinstance(value, (tuple, list)):
                serializable_config[key] = list(value)
            else:
                serializable_config[key] = value
        
        with open(filepath, 'w') as f:
            json.dump(serializable_config, f, indent=2)
        
        logger.info(f"Config saved to {filepath}")
        
    except Exception as e:
        logger.error(f"Failed to save config: {e}")


def load_config(filepath: str) -> Optional[Dict[str, Any]]:
    """
    从JSON文件加载配置
    
    Args:
        filepath: 文件路径
        
    Returns:
        config: 配置字典
    """
    try:
        with open(filepath, 'r') as f:
            config = json.load(f)
        
        logger.info(f"Config loaded from {filepath}")
        return config
        
    except Exception as e:
        logger.error(f"Failed to load config: {e}")
        return None


def benchmark_function(func, *args, num_runs: int = 10, **kwargs) -> Dict[str, float]:
    """
    基准测试函数
    
    Args:
        func: 要测试的函数
        *args: 函数参数
        num_runs: 运行次数
        **kwargs: 函数关键字参数
        
    Returns:
        benchmark_results: 基准测试结果
    """
    import time
    
    times = []
    
    # 预热
    for _ in range(3):
        _ = func(*args, **kwargs)
    
    # 正式测试
    for _ in range(num_runs):
        start_time = time.time()
        _ = func(*args, **kwargs)
        end_time = time.time()
        times.append(end_time - start_time)
    
    times = np.array(times)
    
    return {
        'mean_time': float(times.mean()),
        'std_time': float(times.std()),
        'min_time': float(times.min()),
        'max_time': float(times.max()),
        'median_time': float(np.median(times))
    }


def get_system_info() -> Dict[str, Any]:
    """
    获取系统信息
    
    Returns:
        system_info: 系统信息字典
    """
    import platform
    import psutil
    
    info = {
        'platform': platform.platform(),
        'python_version': platform.python_version(),
        'pytorch_version': torch.__version__,
        'cpu_count': psutil.cpu_count(),
        'memory_total': psutil.virtual_memory().total,
        'memory_available': psutil.virtual_memory().available,
        'cuda_info': check_cuda_availability()
    }
    
    return info


# 全局配置
DEFAULT_CONFIG = {
    'log_level': 'INFO',
    'performance_monitoring': True,
    'debug_visualization': False,
    'memory_optimization': True,
    'gradient_checkpointing': False
}

# 初始化日志
setup_logging()
