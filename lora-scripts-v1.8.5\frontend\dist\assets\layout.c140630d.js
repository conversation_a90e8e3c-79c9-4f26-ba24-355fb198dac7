import{n as nextTick,E as EVENT_CODE,f as defineComponent,i as ElButton,j as ElFocusTrap,k as ElInput,l as ElOverlay,m as ElIcon,T as TypeComponents,p as isValidComponentSize,q as useGlobalComponentSettings,s as computed,v as ref,x as reactive,y as TypeComponentsMap,z as useId,A as watch,B as useDraggable,C as onMounted,D as onBeforeUnmount,F as useLockscreen,G as toRefs,H as _export_sfc,I as useSameTarget,r as resolveComponent,o as openBlock,J as createBlock,w as withCtx,K as withDirectives,d as createVNode,a as createBaseVNode,L as normalizeClass,M as normalizeStyle,N as withModifiers,c as createElementBlock,O as resolveDynamicComponent,P as createCommentVNode,t as toDisplayString,Q as withKeys,R as renderSlot,b as createTextVNode,S as vShow,U as Transition,V as isClient,W as isString,X as isVNode,Y as render,Z as hasOwn,$ as isObject,a0 as isUndefined,a1 as isFunction,a2 as isElement,_ as _export_sfc$1,a3 as usePageFrontmatter,a4 as Fragment,a5 as renderList,a6 as isArray,a7 as useRoute,h as unref,a8 as mergeProps,a9 as isLinkHttp,aa as isLinkMailto,ab as isLinkTel,ac as useSiteData,ad as useSiteLocaleData,ae as useDarkMode,af as h,ag as withBase,ah as ClientOnly,u as useRouteLocale,g as useThemeLocaleData,ai as removeLeadingSlash,aj as removeEndingSlash,ak as useRouter,al as useNavLink,e as createStaticVNode,am as usePageData,an as useSidebarItems,ao as isPlainObject,ap as useToggle,aq as ElMessage,ar as pushScopeId,as as popScopeId,at as onUnmounted,au as useScrollPromise,av as clone,aw as Schema,ax as get,ay as useI18n,az as onBeforeMount,aA as ElAlert,aB as post,aC as isNullable,aD as createSlots}from"./app.9273d30a.js";const FOCUSABLE_ELEMENT_SELECTORS='a[href],button:not([disabled]),button:not([hidden]),:not([tabindex="-1"]),input:not([disabled]),input:not([type="hidden"]),select:not([disabled]),textarea:not([disabled])',isVisible=e=>getComputedStyle(e).position==="fixed"?!1:e.offsetParent!==null,obtainAllFocusableElements=e=>Array.from(e.querySelectorAll(FOCUSABLE_ELEMENT_SELECTORS)).filter(t=>isFocusable(t)&&isVisible(t)),isFocusable=e=>{if(e.tabIndex>0||e.tabIndex===0&&e.getAttribute("tabIndex")!==null)return!0;if(e.disabled)return!1;switch(e.nodeName){case"A":return!!e.href&&e.rel!=="ignore";case"INPUT":return!(e.type==="hidden"||e.type==="file");case"BUTTON":case"SELECT":case"TEXTAREA":return!0;default:return!1}},FOCUSABLE_CHILDREN="_trap-focus-children",FOCUS_STACK=[],FOCUS_HANDLER=e=>{if(FOCUS_STACK.length===0)return;const t=FOCUS_STACK[FOCUS_STACK.length-1][FOCUSABLE_CHILDREN];if(t.length>0&&e.code===EVENT_CODE.tab){if(t.length===1){e.preventDefault(),document.activeElement!==t[0]&&t[0].focus();return}const r=e.shiftKey,n=e.target===t[0],s=e.target===t[t.length-1];n&&r&&(e.preventDefault(),t[t.length-1].focus()),s&&!r&&(e.preventDefault(),t[0].focus())}},TrapFocus={beforeMount(e){e[FOCUSABLE_CHILDREN]=obtainAllFocusableElements(e),FOCUS_STACK.push(e),FOCUS_STACK.length<=1&&document.addEventListener("keydown",FOCUS_HANDLER)},updated(e){nextTick(()=>{e[FOCUSABLE_CHILDREN]=obtainAllFocusableElements(e)})},unmounted(){FOCUS_STACK.shift(),FOCUS_STACK.length===0&&document.removeEventListener("keydown",FOCUS_HANDLER)}},_sfc_main$r=defineComponent({name:"ElMessageBox",directives:{TrapFocus},components:{ElButton,ElFocusTrap,ElInput,ElOverlay,ElIcon,...TypeComponents},inheritAttrs:!1,props:{buttonSize:{type:String,validator:isValidComponentSize},modal:{type:Boolean,default:!0},lockScroll:{type:Boolean,default:!0},showClose:{type:Boolean,default:!0},closeOnClickModal:{type:Boolean,default:!0},closeOnPressEscape:{type:Boolean,default:!0},closeOnHashChange:{type:Boolean,default:!0},center:Boolean,draggable:Boolean,roundButton:{default:!1,type:Boolean},container:{type:String,default:"body"},boxType:{type:String,default:""}},emits:["vanish","action"],setup(e,{emit:t}){const{locale:r,zIndex:n,ns:s,size:a}=useGlobalComponentSettings("message-box",computed(()=>e.buttonSize)),{t:u}=r,{nextZIndex:c}=n,l=ref(!1),o=reactive({autofocus:!0,beforeClose:null,callback:null,cancelButtonText:"",cancelButtonClass:"",confirmButtonText:"",confirmButtonClass:"",customClass:"",customStyle:{},dangerouslyUseHTMLString:!1,distinguishCancelAndClose:!1,icon:"",inputPattern:null,inputPlaceholder:"",inputType:"text",inputValue:null,inputValidator:null,inputErrorMessage:"",message:null,modalFade:!0,modalClass:"",showCancelButton:!1,showConfirmButton:!0,type:"",title:void 0,showInput:!1,action:"",confirmButtonLoading:!1,cancelButtonLoading:!1,confirmButtonDisabled:!1,editorErrorMessage:"",validateError:!1,zIndex:c()}),d=computed(()=>{const b=o.type;return{[s.bm("icon",b)]:b&&TypeComponentsMap[b]}}),_=useId(),i=useId(),F=computed(()=>o.icon||TypeComponentsMap[o.type]||""),S=computed(()=>!!o.message),C=ref(),E=ref(),B=ref(),A=ref(),x=ref(),R=computed(()=>o.confirmButtonClass);watch(()=>o.inputValue,async b=>{await nextTick(),e.boxType==="prompt"&&b!==null&&O()},{immediate:!0}),watch(()=>l.value,b=>{var y,T;b&&(e.boxType!=="prompt"&&(o.autofocus?B.value=(T=(y=x.value)==null?void 0:y.$el)!=null?T:C.value:B.value=C.value),o.zIndex=c()),e.boxType==="prompt"&&(b?nextTick().then(()=>{var p;A.value&&A.value.$el&&(o.autofocus?B.value=(p=K())!=null?p:C.value:B.value=C.value)}):(o.editorErrorMessage="",o.validateError=!1))});const L=computed(()=>e.draggable);useDraggable(C,E,L),onMounted(async()=>{await nextTick(),e.closeOnHashChange&&window.addEventListener("hashchange",$)}),onBeforeUnmount(()=>{e.closeOnHashChange&&window.removeEventListener("hashchange",$)});function $(){!l.value||(l.value=!1,nextTick(()=>{o.action&&t("action",o.action)}))}const V=()=>{e.closeOnClickModal&&D(o.distinguishCancelAndClose?"close":"cancel")},z=useSameTarget(V),j=b=>{if(o.inputType!=="textarea")return b.preventDefault(),D("confirm")},D=b=>{var y;e.boxType==="prompt"&&b==="confirm"&&!O()||(o.action=b,o.beforeClose?(y=o.beforeClose)==null||y.call(o,b,o,$):$())},O=()=>{if(e.boxType==="prompt"){const b=o.inputPattern;if(b&&!b.test(o.inputValue||""))return o.editorErrorMessage=o.inputErrorMessage||u("el.messagebox.error"),o.validateError=!0,!1;const y=o.inputValidator;if(typeof y=="function"){const T=y(o.inputValue);if(T===!1)return o.editorErrorMessage=o.inputErrorMessage||u("el.messagebox.error"),o.validateError=!0,!1;if(typeof T=="string")return o.editorErrorMessage=T,o.validateError=!0,!1}}return o.editorErrorMessage="",o.validateError=!1,!0},K=()=>{const b=A.value.$refs;return b.input||b.textarea},M=()=>{D("close")},W=()=>{e.closeOnPressEscape&&M()};return e.lockScroll&&useLockscreen(l),{...toRefs(o),ns:s,overlayEvent:z,visible:l,hasMessage:S,typeClass:d,contentId:_,inputId:i,btnSize:a,iconComponent:F,confirmButtonClasses:R,rootRef:C,focusStartRef:B,headerRef:E,inputRef:A,confirmRef:x,doClose:$,handleClose:M,onCloseRequested:W,handleWrapperClick:V,handleInputEnter:j,handleAction:D,t:u}}}),_hoisted_1$m=["aria-label","aria-describedby"],_hoisted_2$g=["aria-label"],_hoisted_3$c=["id"];function _sfc_render$1(e,t,r,n,s,a){const u=resolveComponent("el-icon"),c=resolveComponent("close"),l=resolveComponent("el-input"),o=resolveComponent("el-button"),d=resolveComponent("el-focus-trap"),_=resolveComponent("el-overlay");return openBlock(),createBlock(Transition,{name:"fade-in-linear",onAfterLeave:t[11]||(t[11]=i=>e.$emit("vanish")),persisted:""},{default:withCtx(()=>[withDirectives(createVNode(_,{"z-index":e.zIndex,"overlay-class":[e.ns.is("message-box"),e.modalClass],mask:e.modal},{default:withCtx(()=>[createBaseVNode("div",{role:"dialog","aria-label":e.title,"aria-modal":"true","aria-describedby":e.showInput?void 0:e.contentId,class:normalizeClass(`${e.ns.namespace.value}-overlay-message-box`),onClick:t[8]||(t[8]=(...i)=>e.overlayEvent.onClick&&e.overlayEvent.onClick(...i)),onMousedown:t[9]||(t[9]=(...i)=>e.overlayEvent.onMousedown&&e.overlayEvent.onMousedown(...i)),onMouseup:t[10]||(t[10]=(...i)=>e.overlayEvent.onMouseup&&e.overlayEvent.onMouseup(...i))},[createVNode(d,{loop:"",trapped:e.visible,"focus-trap-el":e.rootRef,"focus-start-el":e.focusStartRef,onReleaseRequested:e.onCloseRequested},{default:withCtx(()=>[createBaseVNode("div",{ref:"rootRef",class:normalizeClass([e.ns.b(),e.customClass,e.ns.is("draggable",e.draggable),{[e.ns.m("center")]:e.center}]),style:normalizeStyle(e.customStyle),tabindex:"-1",onClick:t[7]||(t[7]=withModifiers(()=>{},["stop"]))},[e.title!==null&&e.title!==void 0?(openBlock(),createElementBlock("div",{key:0,ref:"headerRef",class:normalizeClass(e.ns.e("header"))},[createBaseVNode("div",{class:normalizeClass(e.ns.e("title"))},[e.iconComponent&&e.center?(openBlock(),createBlock(u,{key:0,class:normalizeClass([e.ns.e("status"),e.typeClass])},{default:withCtx(()=>[(openBlock(),createBlock(resolveDynamicComponent(e.iconComponent)))]),_:1},8,["class"])):createCommentVNode("v-if",!0),createBaseVNode("span",null,toDisplayString(e.title),1)],2),e.showClose?(openBlock(),createElementBlock("button",{key:0,type:"button",class:normalizeClass(e.ns.e("headerbtn")),"aria-label":e.t("el.messagebox.close"),onClick:t[0]||(t[0]=i=>e.handleAction(e.distinguishCancelAndClose?"close":"cancel")),onKeydown:t[1]||(t[1]=withKeys(withModifiers(i=>e.handleAction(e.distinguishCancelAndClose?"close":"cancel"),["prevent"]),["enter"]))},[createVNode(u,{class:normalizeClass(e.ns.e("close"))},{default:withCtx(()=>[createVNode(c)]),_:1},8,["class"])],42,_hoisted_2$g)):createCommentVNode("v-if",!0)],2)):createCommentVNode("v-if",!0),createBaseVNode("div",{id:e.contentId,class:normalizeClass(e.ns.e("content"))},[createBaseVNode("div",{class:normalizeClass(e.ns.e("container"))},[e.iconComponent&&!e.center&&e.hasMessage?(openBlock(),createBlock(u,{key:0,class:normalizeClass([e.ns.e("status"),e.typeClass])},{default:withCtx(()=>[(openBlock(),createBlock(resolveDynamicComponent(e.iconComponent)))]),_:1},8,["class"])):createCommentVNode("v-if",!0),e.hasMessage?(openBlock(),createElementBlock("div",{key:1,class:normalizeClass(e.ns.e("message"))},[renderSlot(e.$slots,"default",{},()=>[e.dangerouslyUseHTMLString?(openBlock(),createBlock(resolveDynamicComponent(e.showInput?"label":"p"),{key:1,for:e.showInput?e.inputId:void 0,innerHTML:e.message},null,8,["for","innerHTML"])):(openBlock(),createBlock(resolveDynamicComponent(e.showInput?"label":"p"),{key:0,for:e.showInput?e.inputId:void 0},{default:withCtx(()=>[createTextVNode(toDisplayString(e.dangerouslyUseHTMLString?"":e.message),1)]),_:1},8,["for"]))])],2)):createCommentVNode("v-if",!0)],2),withDirectives(createBaseVNode("div",{class:normalizeClass(e.ns.e("input"))},[createVNode(l,{id:e.inputId,ref:"inputRef",modelValue:e.inputValue,"onUpdate:modelValue":t[2]||(t[2]=i=>e.inputValue=i),type:e.inputType,placeholder:e.inputPlaceholder,"aria-invalid":e.validateError,class:normalizeClass({invalid:e.validateError}),onKeydown:withKeys(e.handleInputEnter,["enter"])},null,8,["id","modelValue","type","placeholder","aria-invalid","class","onKeydown"]),createBaseVNode("div",{class:normalizeClass(e.ns.e("errormsg")),style:normalizeStyle({visibility:e.editorErrorMessage?"visible":"hidden"})},toDisplayString(e.editorErrorMessage),7)],2),[[vShow,e.showInput]])],10,_hoisted_3$c),createBaseVNode("div",{class:normalizeClass(e.ns.e("btns"))},[e.showCancelButton?(openBlock(),createBlock(o,{key:0,loading:e.cancelButtonLoading,class:normalizeClass([e.cancelButtonClass]),round:e.roundButton,size:e.btnSize,onClick:t[3]||(t[3]=i=>e.handleAction("cancel")),onKeydown:t[4]||(t[4]=withKeys(withModifiers(i=>e.handleAction("cancel"),["prevent"]),["enter"]))},{default:withCtx(()=>[createTextVNode(toDisplayString(e.cancelButtonText||e.t("el.messagebox.cancel")),1)]),_:1},8,["loading","class","round","size"])):createCommentVNode("v-if",!0),withDirectives(createVNode(o,{ref:"confirmRef",type:"primary",loading:e.confirmButtonLoading,class:normalizeClass([e.confirmButtonClasses]),round:e.roundButton,disabled:e.confirmButtonDisabled,size:e.btnSize,onClick:t[5]||(t[5]=i=>e.handleAction("confirm")),onKeydown:t[6]||(t[6]=withKeys(withModifiers(i=>e.handleAction("confirm"),["prevent"]),["enter"]))},{default:withCtx(()=>[createTextVNode(toDisplayString(e.confirmButtonText||e.t("el.messagebox.confirm")),1)]),_:1},8,["loading","class","round","disabled","size"]),[[vShow,e.showConfirmButton]])],2)],6)]),_:3},8,["trapped","focus-trap-el","focus-start-el","onReleaseRequested"])],42,_hoisted_1$m)]),_:3},8,["z-index","overlay-class","mask"]),[[vShow,e.visible]])]),_:3})}var MessageBoxConstructor=_export_sfc(_sfc_main$r,[["render",_sfc_render$1],["__file","/home/<USER>/work/element-plus/element-plus/packages/components/message-box/src/index.vue"]]);const messageInstance=new Map,getAppendToElement=e=>{let t=document.body;return e.appendTo&&(isString(e.appendTo)&&(t=document.querySelector(e.appendTo)),isElement(e.appendTo)&&(t=e.appendTo),isElement(t)||(t=document.body)),t},initInstance=(e,t,r=null)=>{const n=createVNode(MessageBoxConstructor,e,isFunction(e.message)||isVNode(e.message)?{default:isFunction(e.message)?e.message:()=>e.message}:null);return n.appContext=r,render(n,t),getAppendToElement(e).appendChild(t.firstElementChild),n.component},genContainer=()=>document.createElement("div"),showMessage=(e,t)=>{const r=genContainer();e.onVanish=()=>{render(null,r),messageInstance.delete(s)},e.onAction=a=>{const u=messageInstance.get(s);let c;e.showInput?c={value:s.inputValue,action:a}:c=a,e.callback?e.callback(c,n.proxy):a==="cancel"||a==="close"?e.distinguishCancelAndClose&&a!=="cancel"?u.reject("close"):u.reject("cancel"):u.resolve(c)};const n=initInstance(e,r,t),s=n.proxy;for(const a in e)hasOwn(e,a)&&!hasOwn(s.$props,a)&&(s[a]=e[a]);return s.visible=!0,s};function MessageBox(e,t=null){if(!isClient)return Promise.reject();let r;return isString(e)||isVNode(e)?e={message:e}:r=e.callback,new Promise((n,s)=>{const a=showMessage(e,t!=null?t:MessageBox._context);messageInstance.set(a,{options:e,callback:r,resolve:n,reject:s})})}const MESSAGE_BOX_VARIANTS=["alert","confirm","prompt"],MESSAGE_BOX_DEFAULT_OPTS={alert:{closeOnPressEscape:!1,closeOnClickModal:!1},confirm:{showCancelButton:!0},prompt:{showCancelButton:!0,showInput:!0}};MESSAGE_BOX_VARIANTS.forEach(e=>{MessageBox[e]=messageBoxFactory(e)});function messageBoxFactory(e){return(t,r,n,s)=>{let a="";return isObject(r)?(n=r,a=""):isUndefined(r)?a="":a=r,MessageBox(Object.assign({title:a,message:t,type:"",...MESSAGE_BOX_DEFAULT_OPTS[e]},n,{boxType:e}),s)}}MessageBox.close=()=>{messageInstance.forEach((e,t)=>{t.doClose()}),messageInstance.clear()};MessageBox._context=null;const _MessageBox=MessageBox;_MessageBox.install=e=>{_MessageBox._context=e._context,e.config.globalProperties.$msgbox=_MessageBox,e.config.globalProperties.$messageBox=_MessageBox,e.config.globalProperties.$alert=_MessageBox.alert,e.config.globalProperties.$confirm=_MessageBox.confirm,e.config.globalProperties.$prompt=_MessageBox.prompt};const ElMessageBox=_MessageBox,_sfc_main$q={},_hoisted_1$l={class:"theme-default-content"};function _sfc_render(e,t){const r=resolveComponent("Content");return openBlock(),createElementBlock("div",_hoisted_1$l,[createVNode(r)])}var HomeContent=_export_sfc$1(_sfc_main$q,[["render",_sfc_render],["__file","HomeContent.vue"]]);const _hoisted_1$k={key:0,class:"features"},_sfc_main$p=defineComponent({__name:"HomeFeatures",setup(e){const t=usePageFrontmatter(),r=computed(()=>isArray(t.value.features)?t.value.features:[]);return(n,s)=>r.value.length?(openBlock(),createElementBlock("div",_hoisted_1$k,[(openBlock(!0),createElementBlock(Fragment,null,renderList(r.value,a=>(openBlock(),createElementBlock("div",{key:a.title,class:"feature"},[createBaseVNode("h2",null,toDisplayString(a.title),1),createBaseVNode("p",null,toDisplayString(a.details),1)]))),128))])):createCommentVNode("",!0)}});var HomeFeatures=_export_sfc$1(_sfc_main$p,[["__file","HomeFeatures.vue"]]);const _hoisted_1$j=["innerHTML"],_hoisted_2$f=["textContent"],_sfc_main$o=defineComponent({__name:"HomeFooter",setup(e){const t=usePageFrontmatter(),r=computed(()=>t.value.footer),n=computed(()=>t.value.footerHtml);return(s,a)=>r.value?(openBlock(),createElementBlock(Fragment,{key:0},[n.value?(openBlock(),createElementBlock("div",{key:0,class:"footer",innerHTML:r.value},null,8,_hoisted_1$j)):(openBlock(),createElementBlock("div",{key:1,class:"footer",textContent:toDisplayString(r.value)},null,8,_hoisted_2$f))],64)):createCommentVNode("",!0)}});var HomeFooter=_export_sfc$1(_sfc_main$o,[["__file","HomeFooter.vue"]]);const _hoisted_1$i=["href","rel","target","aria-label"],__default__=defineComponent({inheritAttrs:!1}),_sfc_main$n=defineComponent({...__default__,__name:"AutoLink",props:{item:{type:Object,required:!0}},setup(e){const t=e,r=useRoute(),n=useSiteData(),{item:s}=toRefs(t),a=computed(()=>isLinkHttp(s.value.link)),u=computed(()=>isLinkMailto(s.value.link)||isLinkTel(s.value.link)),c=computed(()=>{if(!u.value){if(s.value.target)return s.value.target;if(a.value)return"_blank"}}),l=computed(()=>c.value==="_blank"),o=computed(()=>!a.value&&!u.value&&!l.value),d=computed(()=>{if(!u.value){if(s.value.rel)return s.value.rel;if(l.value)return"noopener noreferrer"}}),_=computed(()=>s.value.ariaLabel||s.value.text),i=computed(()=>{const C=Object.keys(n.value.locales);return C.length?!C.some(E=>E===s.value.link):s.value.link!=="/"}),F=computed(()=>i.value?r.path.startsWith(s.value.link):!1),S=computed(()=>o.value?s.value.activeMatch?new RegExp(s.value.activeMatch).test(r.path):F.value:!1);return(C,E)=>{const B=resolveComponent("RouterLink"),A=resolveComponent("AutoLinkExternalIcon");return o.value?(openBlock(),createBlock(B,mergeProps({key:0,class:{"router-link-active":S.value},to:unref(s).link,"aria-label":_.value},C.$attrs),{default:withCtx(()=>[renderSlot(C.$slots,"before"),createTextVNode(" "+toDisplayString(unref(s).text)+" ",1),renderSlot(C.$slots,"after")]),_:3},16,["class","to","aria-label"])):(openBlock(),createElementBlock("a",mergeProps({key:1,class:"external-link",href:unref(s).link,rel:d.value,target:c.value,"aria-label":_.value},C.$attrs),[renderSlot(C.$slots,"before"),createTextVNode(" "+toDisplayString(unref(s).text)+" ",1),l.value?(openBlock(),createBlock(A,{key:0})):createCommentVNode("",!0),renderSlot(C.$slots,"after")],16,_hoisted_1$i))}}});var AutoLink=_export_sfc$1(_sfc_main$n,[["__file","AutoLink.vue"]]);const _hoisted_1$h={class:"hero"},_hoisted_2$e={key:0,id:"main-title"},_hoisted_3$b={key:1,class:"description"},_hoisted_4$9={key:2,class:"actions"},_sfc_main$m=defineComponent({__name:"HomeHero",setup(e){const t=usePageFrontmatter(),r=useSiteLocaleData(),n=useDarkMode(),s=computed(()=>n.value&&t.value.heroImageDark!==void 0?t.value.heroImageDark:t.value.heroImage),a=computed(()=>t.value.heroText===null?null:t.value.heroText||r.value.title||"Hello"),u=computed(()=>t.value.heroAlt||a.value||"hero"),c=computed(()=>t.value.tagline===null?null:t.value.tagline||r.value.description||"Welcome to your VuePress site"),l=computed(()=>isArray(t.value.actions)?t.value.actions.map(({text:d,link:_,type:i="primary"})=>({text:d,link:_,type:i})):[]),o=()=>{if(!s.value)return null;const d=h("img",{src:withBase(s.value),alt:u.value});return t.value.heroImageDark===void 0?d:h(ClientOnly,()=>d)};return(d,_)=>(openBlock(),createElementBlock("header",_hoisted_1$h,[createVNode(o),a.value?(openBlock(),createElementBlock("h1",_hoisted_2$e,toDisplayString(a.value),1)):createCommentVNode("",!0),c.value?(openBlock(),createElementBlock("p",_hoisted_3$b,toDisplayString(c.value),1)):createCommentVNode("",!0),l.value.length?(openBlock(),createElementBlock("p",_hoisted_4$9,[(openBlock(!0),createElementBlock(Fragment,null,renderList(l.value,i=>(openBlock(),createBlock(AutoLink,{key:i.text,class:normalizeClass(["action-button",[i.type]]),item:i},null,8,["class","item"]))),128))])):createCommentVNode("",!0)]))}});var HomeHero=_export_sfc$1(_sfc_main$m,[["__file","HomeHero.vue"]]);const _hoisted_1$g={class:"home"},_sfc_main$l=defineComponent({__name:"Home",setup(e){return(t,r)=>(openBlock(),createElementBlock("main",_hoisted_1$g,[createVNode(HomeHero),createVNode(HomeFeatures),createVNode(HomeContent),createVNode(HomeFooter)]))}});var Home=_export_sfc$1(_sfc_main$l,[["__file","Home.vue"]]);const _sfc_main$k=defineComponent({__name:"NavbarBrand",setup(e){const t=useRouteLocale(),r=useSiteLocaleData(),n=useThemeLocaleData(),s=useDarkMode(),a=computed(()=>n.value.home||t.value),u=computed(()=>r.value.title),c=computed(()=>s.value&&n.value.logoDark!==void 0?n.value.logoDark:n.value.logo),l=()=>{if(!c.value)return null;const o=h("img",{class:"logo",src:withBase(c.value),alt:u.value});return n.value.logoDark===void 0?o:h(ClientOnly,()=>o)};return(o,d)=>{const _=resolveComponent("RouterLink");return openBlock(),createBlock(_,{to:a.value},{default:withCtx(()=>[createVNode(l),u.value?(openBlock(),createElementBlock("span",{key:0,class:normalizeClass(["site-name",{"can-hide":c.value}])},toDisplayString(u.value),3)):createCommentVNode("",!0)]),_:1},8,["to"])}}});var NavbarBrand=_export_sfc$1(_sfc_main$k,[["__file","NavbarBrand.vue"]]);const _sfc_main$j=defineComponent({__name:"DropdownTransition",setup(e){const t=n=>{n.style.height=n.scrollHeight+"px"},r=n=>{n.style.height=""};return(n,s)=>(openBlock(),createBlock(Transition,{name:"dropdown",onEnter:t,onAfterEnter:r,onBeforeLeave:t},{default:withCtx(()=>[renderSlot(n.$slots,"default")]),_:3}))}});var DropdownTransition=_export_sfc$1(_sfc_main$j,[["__file","DropdownTransition.vue"]]);const _hoisted_1$f=["aria-label"],_hoisted_2$d={class:"title"},_hoisted_3$a=createBaseVNode("span",{class:"arrow down"},null,-1),_hoisted_4$8=["aria-label"],_hoisted_5$7={class:"title"},_hoisted_6$7={class:"navbar-dropdown"},_hoisted_7$4={class:"navbar-dropdown-subtitle"},_hoisted_8$3={key:1},_hoisted_9$3={class:"navbar-dropdown-subitem-wrapper"},_sfc_main$i=defineComponent({__name:"NavbarDropdown",props:{item:{type:Object,required:!0}},setup(e){const t=e,{item:r}=toRefs(t),n=computed(()=>r.value.ariaLabel||r.value.text),s=ref(!1),a=useRoute();watch(()=>a.path,()=>{s.value=!1});const u=l=>{l.detail===0?s.value=!s.value:s.value=!1},c=(l,o)=>o[o.length-1]===l;return(l,o)=>(openBlock(),createElementBlock("div",{class:normalizeClass(["navbar-dropdown-wrapper",{open:s.value}])},[createBaseVNode("button",{class:"navbar-dropdown-title",type:"button","aria-label":n.value,onClick:u},[createBaseVNode("span",_hoisted_2$d,toDisplayString(unref(r).text),1),_hoisted_3$a],8,_hoisted_1$f),createBaseVNode("button",{class:"navbar-dropdown-title-mobile",type:"button","aria-label":n.value,onClick:o[0]||(o[0]=d=>s.value=!s.value)},[createBaseVNode("span",_hoisted_5$7,toDisplayString(unref(r).text),1),createBaseVNode("span",{class:normalizeClass(["arrow",s.value?"down":"right"])},null,2)],8,_hoisted_4$8),createVNode(DropdownTransition,null,{default:withCtx(()=>[withDirectives(createBaseVNode("ul",_hoisted_6$7,[(openBlock(!0),createElementBlock(Fragment,null,renderList(unref(r).children,d=>(openBlock(),createElementBlock("li",{key:d.text,class:"navbar-dropdown-item"},[d.children?(openBlock(),createElementBlock(Fragment,{key:0},[createBaseVNode("h4",_hoisted_7$4,[d.link?(openBlock(),createBlock(AutoLink,{key:0,item:d,onFocusout:_=>c(d,unref(r).children)&&d.children.length===0&&(s.value=!1)},null,8,["item","onFocusout"])):(openBlock(),createElementBlock("span",_hoisted_8$3,toDisplayString(d.text),1))]),createBaseVNode("ul",_hoisted_9$3,[(openBlock(!0),createElementBlock(Fragment,null,renderList(d.children,_=>(openBlock(),createElementBlock("li",{key:_.link,class:"navbar-dropdown-subitem"},[createVNode(AutoLink,{item:_,onFocusout:i=>c(_,d.children)&&c(d,unref(r).children)&&(s.value=!1)},null,8,["item","onFocusout"])]))),128))])],64)):(openBlock(),createBlock(AutoLink,{key:1,item:d,onFocusout:_=>c(d,unref(r).children)&&(s.value=!1)},null,8,["item","onFocusout"]))]))),128))],512),[[vShow,s.value]])]),_:1})],2))}});var NavbarDropdown=_export_sfc$1(_sfc_main$i,[["__file","NavbarDropdown.vue"]]);const normalizePath=e=>decodeURI(e).replace(/#.*$/,"").replace(/(index)?\.(md|html)$/,""),isActiveLink=(e,t)=>{if(t.hash===e)return!0;const r=normalizePath(t.path),n=normalizePath(e);return r===n},isActiveSidebarItem=(e,t)=>e.link&&isActiveLink(e.link,t)?!0:e.children?e.children.some(r=>isActiveSidebarItem(r,t)):!1,resolveRepoType=e=>!isLinkHttp(e)||/github\.com/.test(e)?"GitHub":/bitbucket\.org/.test(e)?"Bitbucket":/gitlab\.com/.test(e)?"GitLab":/gitee\.com/.test(e)?"Gitee":null,editLinkPatterns={GitHub:":repo/edit/:branch/:path",GitLab:":repo/-/edit/:branch/:path",Gitee:":repo/edit/:branch/:path",Bitbucket:":repo/src/:branch/:path?mode=edit&spa=0&at=:branch&fileviewer=file-view-default"},resolveEditLinkPatterns=({docsRepo:e,editLinkPattern:t})=>{if(t)return t;const r=resolveRepoType(e);return r!==null?editLinkPatterns[r]:null},resolveEditLink=({docsRepo:e,docsBranch:t,docsDir:r,filePathRelative:n,editLinkPattern:s})=>{if(!n)return null;const a=resolveEditLinkPatterns({docsRepo:e,editLinkPattern:s});return a?a.replace(/:repo/,isLinkHttp(e)?e:`https://github.com/${e}`).replace(/:branch/,t).replace(/:path/,removeLeadingSlash(`${removeEndingSlash(r)}/${n}`)):null},_hoisted_1$e={key:0,class:"navbar-items"},_sfc_main$h=defineComponent({__name:"NavbarItems",setup(e){const t=()=>{const o=useRouter(),d=useRouteLocale(),_=useSiteLocaleData(),i=useThemeLocaleData();return computed(()=>{var A,x,R;const F=Object.keys(_.value.locales);if(F.length<2)return[];const S=o.currentRoute.value.path,C=o.currentRoute.value.fullPath,E=o.currentRoute.value.hash;return[{text:(A=i.value.selectLanguageText)!=null?A:"unknown language",ariaLabel:(R=(x=i.value.selectLanguageAriaLabel)!=null?x:i.value.selectLanguageText)!=null?R:"unknown language",children:F.map(L=>{var O,K,M,W,b,y;const $=(K=(O=_.value.locales)==null?void 0:O[L])!=null?K:{},V=(W=(M=i.value.locales)==null?void 0:M[L])!=null?W:{},z=`${$.lang}`,j=(b=V.selectLanguageName)!=null?b:z;let D;if(z===_.value.lang)D=C;else{const T=S.replace(d.value,L);o.getRoutes().some(p=>p.path===T)?D=`${T}${E}`:D=(y=V.home)!=null?y:L}return{text:j,link:D}})}]})},r=()=>{const o=useThemeLocaleData(),d=computed(()=>o.value.repo),_=computed(()=>d.value?resolveRepoType(d.value):null),i=computed(()=>d.value&&!isLinkHttp(d.value)?`https://github.com/${d.value}`:d.value),F=computed(()=>i.value?o.value.repoLabel?o.value.repoLabel:_.value===null?"Source":_.value:null);return computed(()=>!i.value||!F.value?[]:[{text:F.value,link:i.value}])},n=o=>isString(o)?useNavLink(o):o.children?{...o,children:o.children.map(n)}:o,a=(()=>{const o=useThemeLocaleData();return computed(()=>(o.value.navbar||[]).map(n))})(),u=t(),c=r(),l=computed(()=>[...a.value,...u.value,...c.value]);return(o,d)=>l.value.length?(openBlock(),createElementBlock("nav",_hoisted_1$e,[(openBlock(!0),createElementBlock(Fragment,null,renderList(l.value,_=>(openBlock(),createElementBlock("div",{key:_.text,class:"navbar-item"},[_.children?(openBlock(),createBlock(NavbarDropdown,{key:0,item:_},null,8,["item"])):(openBlock(),createBlock(AutoLink,{key:1,item:_},null,8,["item"]))]))),128))])):createCommentVNode("",!0)}});var NavbarItems=_export_sfc$1(_sfc_main$h,[["__file","NavbarItems.vue"]]);const _hoisted_1$d=["title"],_hoisted_2$c={class:"icon",focusable:"false",viewBox:"0 0 32 32"},_hoisted_3$9=createStaticVNode('<path d="M16 12.005a4 4 0 1 1-4 4a4.005 4.005 0 0 1 4-4m0-2a6 6 0 1 0 6 6a6 6 0 0 0-6-6z" fill="currentColor"></path><path d="M5.394 6.813l1.414-1.415l3.506 3.506L8.9 10.318z" fill="currentColor"></path><path d="M2 15.005h5v2H2z" fill="currentColor"></path><path d="M5.394 25.197L8.9 21.691l1.414 1.415l-3.506 3.505z" fill="currentColor"></path><path d="M15 25.005h2v5h-2z" fill="currentColor"></path><path d="M21.687 23.106l1.414-1.415l3.506 3.506l-1.414 1.414z" fill="currentColor"></path><path d="M25 15.005h5v2h-5z" fill="currentColor"></path><path d="M21.687 8.904l3.506-3.506l1.414 1.415l-3.506 3.505z" fill="currentColor"></path><path d="M15 2.005h2v5h-2z" fill="currentColor"></path>',9),_hoisted_12$1=[_hoisted_3$9],_hoisted_13$1={class:"icon",focusable:"false",viewBox:"0 0 32 32"},_hoisted_14$1=createBaseVNode("path",{d:"M13.502 5.414a15.075 15.075 0 0 0 11.594 18.194a11.113 11.113 0 0 1-7.975 3.39c-.138 0-.278.005-.418 0a11.094 11.094 0 0 1-3.2-21.584M14.98 3a1.002 1.002 0 0 0-.175.016a13.096 13.096 0 0 0 1.825 25.981c.164.006.328 0 .49 0a13.072 13.072 0 0 0 10.703-5.555a1.01 1.01 0 0 0-.783-1.565A13.08 13.08 0 0 1 15.89 4.38A1.015 1.015 0 0 0 14.98 3z",fill:"currentColor"},null,-1),_hoisted_15$1=[_hoisted_14$1],_sfc_main$g=defineComponent({__name:"ToggleColorModeButton",setup(e){const t=useThemeLocaleData(),r=useDarkMode(),n=()=>{r.value=!r.value};return(s,a)=>(openBlock(),createElementBlock("button",{class:"toggle-color-mode-button",title:unref(t).toggleColorMode,onClick:n},[withDirectives((openBlock(),createElementBlock("svg",_hoisted_2$c,_hoisted_12$1,512)),[[vShow,!unref(r)]]),withDirectives((openBlock(),createElementBlock("svg",_hoisted_13$1,_hoisted_15$1,512)),[[vShow,unref(r)]])],8,_hoisted_1$d))}});var ToggleColorModeButton=_export_sfc$1(_sfc_main$g,[["__file","ToggleColorModeButton.vue"]]);const _hoisted_1$c=["title"],_hoisted_2$b=createBaseVNode("div",{class:"icon","aria-hidden":"true"},[createBaseVNode("span"),createBaseVNode("span"),createBaseVNode("span")],-1),_hoisted_3$8=[_hoisted_2$b],_sfc_main$f=defineComponent({__name:"ToggleSidebarButton",emits:["toggle"],setup(e){const t=useThemeLocaleData();return(r,n)=>(openBlock(),createElementBlock("div",{class:"toggle-sidebar-button",title:unref(t).toggleSidebar,"aria-expanded":"false",role:"button",tabindex:"0",onClick:n[0]||(n[0]=s=>r.$emit("toggle"))},_hoisted_3$8,8,_hoisted_1$c))}});var ToggleSidebarButton=_export_sfc$1(_sfc_main$f,[["__file","ToggleSidebarButton.vue"]]);const _sfc_main$e=defineComponent({__name:"Navbar",emits:["toggle-sidebar"],setup(e){const t=useThemeLocaleData(),r=ref(null),n=ref(null),s=ref(0),a=computed(()=>s.value?{maxWidth:s.value+"px"}:{});onMounted(()=>{const l=u(r.value,"paddingLeft")+u(r.value,"paddingRight"),o=()=>{var d;window.innerWidth<=719?s.value=0:s.value=r.value.offsetWidth-l-(((d=n.value)==null?void 0:d.offsetWidth)||0)};o(),window.addEventListener("resize",o,!1),window.addEventListener("orientationchange",o,!1)});function u(c,l){var _,i,F;const o=(F=(i=(_=c==null?void 0:c.ownerDocument)==null?void 0:_.defaultView)==null?void 0:i.getComputedStyle(c,null))==null?void 0:F[l],d=Number.parseInt(o,10);return Number.isNaN(d)?0:d}return(c,l)=>{const o=resolveComponent("NavbarSearch");return openBlock(),createElementBlock("header",{ref_key:"navbar",ref:r,class:"navbar"},[createVNode(ToggleSidebarButton,{onToggle:l[0]||(l[0]=d=>c.$emit("toggle-sidebar"))}),createBaseVNode("span",{ref_key:"navbarBrand",ref:n},[createVNode(NavbarBrand)],512),createBaseVNode("div",{class:"navbar-items-wrapper",style:normalizeStyle(a.value)},[renderSlot(c.$slots,"before"),createVNode(NavbarItems,{class:"can-hide"}),renderSlot(c.$slots,"after"),unref(t).colorModeSwitch?(openBlock(),createBlock(ToggleColorModeButton,{key:0})):createCommentVNode("",!0),createVNode(o)],4)],512)}}});var Navbar=_export_sfc$1(_sfc_main$e,[["__file","Navbar.vue"]]);const _hoisted_1$b={class:"page-meta"},_hoisted_2$a={key:0,class:"meta-item edit-link"},_hoisted_3$7={key:1,class:"meta-item last-updated"},_hoisted_4$7={class:"meta-item-label"},_hoisted_5$6={class:"meta-item-info"},_hoisted_6$6={key:2,class:"meta-item contributors"},_hoisted_7$3={class:"meta-item-label"},_hoisted_8$2={class:"meta-item-info"},_hoisted_9$2=["title"],_sfc_main$d=defineComponent({__name:"PageMeta",setup(e){const t=()=>{const l=useThemeLocaleData(),o=usePageData(),d=usePageFrontmatter();return computed(()=>{var A,x,R;if(!((x=(A=d.value.editLink)!=null?A:l.value.editLink)!=null?x:!0))return null;const{repo:i,docsRepo:F=i,docsBranch:S="main",docsDir:C="",editLinkText:E}=l.value;if(!F)return null;const B=resolveEditLink({docsRepo:F,docsBranch:S,docsDir:C,filePathRelative:o.value.filePathRelative,editLinkPattern:(R=d.value.editLinkPattern)!=null?R:l.value.editLinkPattern});return B?{text:E!=null?E:"Edit this page",link:B}:null})},r=()=>{const l=useThemeLocaleData(),o=usePageData(),d=usePageFrontmatter();return computed(()=>{var F,S,C,E;return!((S=(F=d.value.lastUpdated)!=null?F:l.value.lastUpdated)!=null?S:!0)||!((C=o.value.git)!=null&&C.updatedTime)?null:new Date((E=o.value.git)==null?void 0:E.updatedTime).toLocaleString()})},n=()=>{const l=useThemeLocaleData(),o=usePageData(),d=usePageFrontmatter();return computed(()=>{var i,F,S,C;return((F=(i=d.value.contributors)!=null?i:l.value.contributors)!=null?F:!0)&&(C=(S=o.value.git)==null?void 0:S.contributors)!=null?C:null})},s=useThemeLocaleData(),a=t(),u=r(),c=n();return(l,o)=>{const d=resolveComponent("ClientOnly");return openBlock(),createElementBlock("footer",_hoisted_1$b,[unref(a)?(openBlock(),createElementBlock("div",_hoisted_2$a,[createVNode(AutoLink,{class:"meta-item-label",item:unref(a)},null,8,["item"])])):createCommentVNode("",!0),unref(u)?(openBlock(),createElementBlock("div",_hoisted_3$7,[createBaseVNode("span",_hoisted_4$7,toDisplayString(unref(s).lastUpdatedText)+": ",1),createVNode(d,null,{default:withCtx(()=>[createBaseVNode("span",_hoisted_5$6,toDisplayString(unref(u)),1)]),_:1})])):createCommentVNode("",!0),unref(c)&&unref(c).length?(openBlock(),createElementBlock("div",_hoisted_6$6,[createBaseVNode("span",_hoisted_7$3,toDisplayString(unref(s).contributorsText)+": ",1),createBaseVNode("span",_hoisted_8$2,[(openBlock(!0),createElementBlock(Fragment,null,renderList(unref(c),(_,i)=>(openBlock(),createElementBlock(Fragment,{key:i},[createBaseVNode("span",{class:"contributor",title:`email: ${_.email}`},toDisplayString(_.name),9,_hoisted_9$2),i!==unref(c).length-1?(openBlock(),createElementBlock(Fragment,{key:0},[createTextVNode(", ")],64)):createCommentVNode("",!0)],64))),128))])])):createCommentVNode("",!0)])}}});var PageMeta=_export_sfc$1(_sfc_main$d,[["__file","PageMeta.vue"]]);const _hoisted_1$a={key:0,class:"page-nav"},_hoisted_2$9={class:"inner"},_hoisted_3$6={key:0,class:"prev"},_hoisted_4$6={key:1,class:"next"},_sfc_main$c=defineComponent({__name:"PageNav",setup(e){const t=l=>l===!1?null:isString(l)?useNavLink(l):isPlainObject(l)?l:!1,r=(l,o,d)=>{const _=l.findIndex(i=>i.link===o);if(_!==-1){const i=l[_+d];return i!=null&&i.link?i:null}for(const i of l)if(i.children){const F=r(i.children,o,d);if(F)return F}return null},n=usePageFrontmatter(),s=useSidebarItems(),a=useRoute(),u=computed(()=>{const l=t(n.value.prev);return l!==!1?l:r(s.value,a.path,-1)}),c=computed(()=>{const l=t(n.value.next);return l!==!1?l:r(s.value,a.path,1)});return(l,o)=>u.value||c.value?(openBlock(),createElementBlock("nav",_hoisted_1$a,[createBaseVNode("p",_hoisted_2$9,[u.value?(openBlock(),createElementBlock("span",_hoisted_3$6,[createVNode(AutoLink,{item:u.value},null,8,["item"])])):createCommentVNode("",!0),c.value?(openBlock(),createElementBlock("span",_hoisted_4$6,[createVNode(AutoLink,{item:c.value},null,8,["item"])])):createCommentVNode("",!0)])])):createCommentVNode("",!0)}});var PageNav=_export_sfc$1(_sfc_main$c,[["__file","PageNav.vue"]]);const _hoisted_1$9={class:"page"},_hoisted_2$8={class:"theme-default-content"},_sfc_main$b=defineComponent({__name:"Page",setup(e){return(t,r)=>{const n=resolveComponent("Content");return openBlock(),createElementBlock("main",_hoisted_1$9,[renderSlot(t.$slots,"top"),createBaseVNode("div",_hoisted_2$8,[renderSlot(t.$slots,"content-top"),createVNode(n),renderSlot(t.$slots,"content-bottom")]),createVNode(PageMeta),createVNode(PageNav),renderSlot(t.$slots,"bottom")])}}});var Page=_export_sfc$1(_sfc_main$b,[["__file","Page.vue"]]);const _hoisted_1$8=["onKeydown"],_hoisted_2$7={class:"sidebar-item-children"},_sfc_main$a=defineComponent({__name:"SidebarItem",props:{item:{type:Object,required:!0},depth:{type:Number,required:!1,default:0}},setup(e){const t=e,{item:r,depth:n}=toRefs(t),s=useRoute(),a=useRouter(),u=computed(()=>isActiveSidebarItem(r.value,s)),c=computed(()=>({"sidebar-item":!0,"sidebar-heading":n.value===0,active:u.value,collapsible:r.value.collapsible})),[l,o]=useToggle(u.value),d=i=>{r.value.collapsible&&(i.preventDefault(),o())},_=a.afterEach(i=>{nextTick(()=>{l.value=r.value.collapsible?u.value:!0})});return onBeforeUnmount(()=>{_()}),(i,F)=>{var C;const S=resolveComponent("SidebarItem",!0);return openBlock(),createElementBlock("li",null,[unref(r).link?(openBlock(),createBlock(AutoLink,{key:0,class:normalizeClass(c.value),item:unref(r)},null,8,["class","item"])):(openBlock(),createElementBlock("p",{key:1,tabindex:"0",class:normalizeClass(c.value),onClick:d,onKeydown:withKeys(d,["enter"])},[createTextVNode(toDisplayString(unref(r).text)+" ",1),unref(r).collapsible?(openBlock(),createElementBlock("span",{key:0,class:normalizeClass(["arrow",unref(l)?"down":"right"])},null,2)):createCommentVNode("",!0)],42,_hoisted_1$8)),(C=unref(r).children)!=null&&C.length?(openBlock(),createBlock(DropdownTransition,{key:2},{default:withCtx(()=>[withDirectives(createBaseVNode("ul",_hoisted_2$7,[(openBlock(!0),createElementBlock(Fragment,null,renderList(unref(r).children,E=>(openBlock(),createBlock(S,{key:`${unref(n)}${E.text}${E.link}`,item:E,depth:unref(n)+1},null,8,["item","depth"]))),128))],512),[[vShow,unref(l)]])]),_:1})):createCommentVNode("",!0)])}}});var SidebarItem=_export_sfc$1(_sfc_main$a,[["__file","SidebarItem.vue"]]);const _hoisted_1$7={key:0,class:"sidebar-items"},_sfc_main$9=defineComponent({__name:"SidebarItems",setup(e){const t=useRoute(),r=useSidebarItems();return onMounted(()=>{watch(()=>t.hash,n=>{const s=document.querySelector(".sidebar");if(!s)return;const a=document.querySelector(`.sidebar a.sidebar-item[href="${t.path}${n}"]`);if(!a)return;const{top:u,height:c}=s.getBoundingClientRect(),{top:l,height:o}=a.getBoundingClientRect();l<u?a.scrollIntoView(!0):l+o>u+c&&a.scrollIntoView(!1)})}),(n,s)=>unref(r).length?(openBlock(),createElementBlock("ul",_hoisted_1$7,[(openBlock(!0),createElementBlock(Fragment,null,renderList(unref(r),a=>(openBlock(),createBlock(SidebarItem,{key:`${a.text}${a.link}`,item:a},null,8,["item"]))),128))])):createCommentVNode("",!0)}});var SidebarItems=_export_sfc$1(_sfc_main$9,[["__file","SidebarItems.vue"]]),sidebar_vue_vue_type_style_index_0_scoped_true_lang="";const _withScopeId$3=e=>(pushScopeId("data-v-db8971c4"),e=e(),popScopeId(),e),_hoisted_1$6={class:"sidebar"},_hoisted_2$6={class:"sidebar-container"},_hoisted_3$5={class:"sidebar-bottom"},_hoisted_4$5=_withScopeId$3(()=>createBaseVNode("li",{class:"sidebar-item sidebar-heading appearance"},[createTextVNode(" Github "),createBaseVNode("a",{class:"icon",href:"https://github.com/Akegarasu/lora-scripts",target:"_blank","aria-label":"GitHub"},[createBaseVNode("svg",{xmlns:"http://www.w3.org/2000/svg",preserveAspectRatio:"xMidYMid meet",viewBox:"0 0 24 24"},[createBaseVNode("path",{d:"M12 2C6.475 2 2 6.475 2 12a9.994 9.994 0 0 0 6.838 9.488c.5.087.687-.213.687-.476c0-.237-.013-1.024-.013-1.862c-2.512.463-3.162-.612-3.362-1.175c-.113-.288-.6-1.175-1.025-1.413c-.35-.187-.85-.65-.013-.662c.788-.013 1.35.725 1.538 1.025c.9 1.512 2.338 1.087 2.912.825c.088-.65.35-1.087.638-1.337c-2.225-.25-4.55-1.113-4.55-4.938c0-1.088.387-1.987 1.025-2.688c-.1-.25-.45-1.275.1-2.65c0 0 .837-.262 2.75 1.026a9.28 9.28 0 0 1 2.5-.338c.85 0 1.7.112 2.5.337c1.912-1.3 2.75-1.024 2.75-1.024c.55 1.375.2 2.4.1 2.65c.637.7 1.025 1.587 1.025 2.687c0 3.838-2.337 4.688-4.562 4.938c.362.312.675.912.675 1.85c0 1.337-.013 2.412-.013 2.75c0 .262.188.574.688.474A10.016 10.016 0 0 0 22 12c0-5.525-4.475-10-10-10z",fill:"currentColor"})])])],-1)),_hoisted_5$5=_withScopeId$3(()=>createBaseVNode("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},[createBaseVNode("path",{d:" M12.87 15.07l-2.54-2.51.03-.03c1.74-1.94 2.98-4.17 3.71-6.53H17V4h-7V2H8v2H1v1.99h11.17C11.5 7.92 10.44 9.75 9 11.35 8.07 10.32 7.3 9.19 6.69 8h-2c.73 1.63 1.73 3.17 2.98 4.56l-5.09 5.02L4 19l5-5 3.11 3.11.76-2.04zM18.5 10h-2L12 22h2l1.12-3h4.75L21 22h2l-4.5-12zm-2.62 7l1.62-4.33L19.12 17h-3.24z ",fill:"currentColor"})],-1)),_hoisted_6$5=[_hoisted_5$5],_hoisted_7$2={class:"sidebar-item sidebar-heading appearance"},_sfc_main$8=defineComponent({__name:"sidebar",setup(e){const t=()=>{window.i18n.global.locale.value=window.i18n.global.locale.value==="en-US"?"zh-CN":"en-US",ElMessage.success("Switch language success")};return(r,n)=>{const s=resolveComponent("el-scrollbar");return openBlock(),createElementBlock("aside",_hoisted_1$6,[createVNode(s,null,{default:withCtx(()=>[createBaseVNode("div",_hoisted_2$6,[createVNode(NavbarItems),createVNode(SidebarItems),createBaseVNode("ul",_hoisted_3$5,[_hoisted_4$5,createBaseVNode("li",{class:"sidebar-item sidebar-heading appearance"},[createTextVNode(" Language "),createBaseVNode("button",{class:"toggle-color-mode-button",onClick:t},_hoisted_6$5)]),createBaseVNode("li",_hoisted_7$2,[createTextVNode(" \u706F\u6CE1 "),createVNode(ToggleColorModeButton)])])])]),_:1})])}}});var Sidebar=_export_sfc$1(_sfc_main$8,[["__scopeId","data-v-db8971c4"],["__file","sidebar.vue"]]);const _sfc_main$7=defineComponent({__name:"Layout",setup(e){const t=usePageData(),r=usePageFrontmatter(),n=useThemeLocaleData(),s=computed(()=>r.value.navbar!==!1&&n.value.navbar!==!1),a=useSidebarItems(),u=ref(!1),c=E=>{u.value=typeof E=="boolean"?E:!u.value},l={x:0,y:0},o=E=>{l.x=E.changedTouches[0].clientX,l.y=E.changedTouches[0].clientY},d=E=>{const B=E.changedTouches[0].clientX-l.x,A=E.changedTouches[0].clientY-l.y;Math.abs(B)>Math.abs(A)&&Math.abs(B)>40&&(B>0&&l.x<=80?c(!0):c(!1))},_=computed(()=>[{"no-navbar":!s.value,"no-sidebar":!a.value.length,"sidebar-open":u.value},r.value.pageClass]);let i;onMounted(()=>{i=useRouter().afterEach(()=>{c(!1)})}),onUnmounted(()=>{i()});const F=useScrollPromise(),S=F.resolve,C=F.pending;return(E,B)=>(openBlock(),createElementBlock("div",{class:normalizeClass(["theme-container",_.value]),onTouchstart:o,onTouchend:d},[renderSlot(E.$slots,"navbar",{},()=>[s.value?(openBlock(),createBlock(Navbar,{key:0,onToggleSidebar:c},{before:withCtx(()=>[renderSlot(E.$slots,"navbar-before")]),after:withCtx(()=>[renderSlot(E.$slots,"navbar-after")]),_:3})):createCommentVNode("",!0)]),createBaseVNode("div",{class:"sidebar-mask",onClick:B[0]||(B[0]=A=>c(!1))}),renderSlot(E.$slots,"sidebar",{},()=>[createVNode(Sidebar,null,{top:withCtx(()=>[renderSlot(E.$slots,"sidebar-top")]),bottom:withCtx(()=>[renderSlot(E.$slots,"sidebar-bottom")]),_:3})]),renderSlot(E.$slots,"page",{},()=>[unref(r).home?(openBlock(),createBlock(Home,{key:0})):(openBlock(),createBlock(Transition,{key:1,name:"fade-slide-y",mode:"out-in",onBeforeEnter:unref(S),onBeforeLeave:unref(C)},{default:withCtx(()=>[(openBlock(),createBlock(Page,{key:unref(t).path},{top:withCtx(()=>[renderSlot(E.$slots,"page-top")]),"content-top":withCtx(()=>[renderSlot(E.$slots,"page-content-top")]),"content-bottom":withCtx(()=>[renderSlot(E.$slots,"page-content-bottom")]),bottom:withCtx(()=>[renderSlot(E.$slots,"page-bottom")]),_:3}))]),_:3},8,["onBeforeEnter","onBeforeLeave"]))])],34))}});var ParentLayout=_export_sfc$1(_sfc_main$7,[["__file","Layout.vue"]]);function stringify(e){if(e===null)throw typeError("null");if(e===void 0)throw typeError("undefined");if(typeof e!="object")throw typeError(typeof e);if(typeof e.toJSON=="function"&&(e=e.toJSON()),e==null)return null;const t=tomlType$1(e);if(t!=="table")throw typeError(t);return stringifyObject("","",e)}function typeError(e){return new Error("Can only stringify objects, not "+e)}function arrayOneTypeError(){return new Error("Array values can't have mixed types")}function getInlineKeys(e){return Object.keys(e).filter(t=>isInline(e[t]))}function getComplexKeys(e){return Object.keys(e).filter(t=>!isInline(e[t]))}function toJSON(e){let t=Array.isArray(e)?[]:Object.prototype.hasOwnProperty.call(e,"__proto__")?{["__proto__"]:void 0}:{};for(let r of Object.keys(e))e[r]&&typeof e[r].toJSON=="function"&&!("toISOString"in e[r])?t[r]=e[r].toJSON():t[r]=e[r];return t}function stringifyObject(e,t,r){r=toJSON(r);var n,s;n=getInlineKeys(r),s=getComplexKeys(r);var a=[],u=t||"";n.forEach(l=>{var o=tomlType$1(r[l]);o!=="undefined"&&o!=="null"&&a.push(u+stringifyKey(l)+" = "+stringifyAnyInline(r[l],!0))}),a.length>0&&a.push("");var c=e&&n.length>0?t+"  ":"";return s.forEach(l=>{a.push(stringifyComplex(e,c,l,r[l]))}),a.join(`
`)}function isInline(e){switch(tomlType$1(e)){case"undefined":case"null":case"integer":case"nan":case"float":case"boolean":case"string":case"datetime":return!0;case"array":return e.length===0||tomlType$1(e[0])!=="table";case"table":return Object.keys(e).length===0;default:return!1}}function tomlType$1(e){return e===void 0?"undefined":e===null?"null":typeof e=="bigint"||Number.isInteger(e)&&!Object.is(e,-0)?"integer":typeof e=="number"?"float":typeof e=="boolean"?"boolean":typeof e=="string"?"string":"toISOString"in e?isNaN(e)?"undefined":"datetime":Array.isArray(e)?"array":"table"}function stringifyKey(e){var t=String(e);return/^[-A-Za-z0-9_]+$/.test(t)?t:stringifyBasicString(t)}function stringifyBasicString(e){return'"'+escapeString(e).replace(/"/g,'\\"')+'"'}function stringifyLiteralString(e){return"'"+e+"'"}function numpad(e,t){for(;t.length<e;)t="0"+t;return t}function escapeString(e){return e.replace(/\\/g,"\\\\").replace(/[\b]/g,"\\b").replace(/\t/g,"\\t").replace(/\n/g,"\\n").replace(/\f/g,"\\f").replace(/\r/g,"\\r").replace(/([\u0000-\u001f\u007f])/,t=>"\\u"+numpad(4,t.codePointAt(0).toString(16)))}function stringifyMultilineString(e){let t=e.split(/\n/).map(r=>escapeString(r).replace(/"(?="")/g,'\\"')).join(`
`);return t.slice(-1)==='"'&&(t+=`\\
`),`"""
`+t+'"""'}function stringifyAnyInline(e,t){let r=tomlType$1(e);return r==="string"&&(t&&/\n/.test(e)?r="string-multiline":!/[\b\t\n\f\r']/.test(e)&&/"/.test(e)&&(r="string-literal")),stringifyInline(e,r)}function stringifyInline(e,t){switch(t||(t=tomlType$1(e)),t){case"string-multiline":return stringifyMultilineString(e);case"string":return stringifyBasicString(e);case"string-literal":return stringifyLiteralString(e);case"integer":return stringifyInteger(e);case"float":return stringifyFloat(e);case"boolean":return stringifyBoolean(e);case"datetime":return stringifyDatetime(e);case"array":return stringifyInlineArray(e.filter(r=>tomlType$1(r)!=="null"&&tomlType$1(r)!=="undefined"&&tomlType$1(r)!=="nan"));case"table":return stringifyInlineTable(e);default:throw typeError(t)}}function stringifyInteger(e){return String(e)}function stringifyFloat(e){if(e===1/0)return"inf";if(e===-1/0)return"-inf";if(Object.is(e,NaN))return"nan";if(Object.is(e,-0))return"-0.0";var t=String(e).split("."),r=t[0];if(t.length==1)return stringifyInteger(r);var n=t[1]||0;return stringifyInteger(r)+"."+n}function stringifyBoolean(e){return String(e)}function stringifyDatetime(e){return e.toISOString()}function isNumber(e){return e==="float"||e==="integer"}function arrayType(e){var t=tomlType$1(e[0]);return e.every(r=>tomlType$1(r)===t)?t:e.every(r=>isNumber(tomlType$1(r)))?"float":"mixed"}function validateArray(e){const t=arrayType(e);if(t==="mixed")throw arrayOneTypeError();return t}function stringifyInlineArray(e){e=toJSON(e);const t=validateArray(e);var r="[",n=e.map(s=>stringifyInline(s,t));return n.join(", ").length>60||/\n/.test(n)?r+=`
  `+n.join(`,
  `)+`
`:r+=" "+n.join(", ")+(n.length>0?" ":""),r+"]"}function stringifyInlineTable(e){e=toJSON(e);var t=[];return Object.keys(e).forEach(r=>{t.push(stringifyKey(r)+" = "+stringifyAnyInline(e[r],!1))}),"{ "+t.join(", ")+(t.length>0?" ":"")+"}"}function stringifyComplex(e,t,r,n){var s=tomlType$1(n);if(s==="array")return stringifyArrayOfTables(e,t,r,n);if(s==="table")return stringifyComplexTable(e,t,r,n);throw typeError(s)}function stringifyArrayOfTables(e,t,r,n){n=toJSON(n),validateArray(n);var s=tomlType$1(n[0]);if(s!=="table")throw typeError(s);var a=e+stringifyKey(r),u="";return n.forEach(c=>{u.length>0&&(u+=`
`),u+=t+"[["+a+`]]
`,u+=stringifyObject(a+".",t,c)}),u}function stringifyComplexTable(e,t,r,n){var s=e+stringifyKey(r),a="";return getInlineKeys(n).length>0&&(a+=t+"["+s+`]
`),a+stringifyObject(s+".",t,n)}const ParserEND=1114112;class ParserError extends Error{constructor(t,r,n){super("[ParserError] "+t,r,n),this.name="ParserError",this.code="ParserError",Error.captureStackTrace&&Error.captureStackTrace(this,ParserError)}}class State{constructor(t){this.parser=t,this.buf="",this.returned=null,this.result=null,this.resultTable=null,this.resultArr=null}}class Parser{constructor(){this.pos=0,this.col=0,this.line=0,this.obj={},this.ctx=this.obj,this.stack=[],this._buf="",this.char=null,this.ii=0,this.state=new State(this.parseStart)}parse(t){if(t.length===0||t.length==null)return;this._buf=String(t),this.ii=-1,this.char=-1;let r;for(;r===!1||this.nextChar();)r=this.runOne();this._buf=null}nextChar(){return this.char===10&&(++this.line,this.col=-1),++this.ii,this.char=this._buf.codePointAt(this.ii),++this.pos,++this.col,this.haveBuffer()}haveBuffer(){return this.ii<this._buf.length}runOne(){return this.state.parser.call(this,this.state.returned)}finish(){this.char=ParserEND;let t;do t=this.state.parser,this.runOne();while(this.state.parser!==t);return this.ctx=null,this.state=null,this._buf=null,this.obj}next(t){if(typeof t!="function")throw new ParserError("Tried to set state to non-existent state: "+JSON.stringify(t));this.state.parser=t}goto(t){return this.next(t),this.runOne()}call(t,r){r&&this.next(r),this.stack.push(this.state),this.state=new State(t)}callNow(t,r){return this.call(t,r),this.runOne()}return(t){if(this.stack.length===0)throw this.error(new ParserError("Stack underflow"));t===void 0&&(t=this.state.buf),this.state=this.stack.pop(),this.state.returned=t}returnNow(t){return this.return(t),this.runOne()}consume(){if(this.char===ParserEND)throw this.error(new ParserError("Unexpected end-of-buffer"));this.state.buf+=this._buf[this.ii]}error(t){return t.line=this.line,t.col=this.col,t.pos=this.pos,t}parseStart(){throw new ParserError("Must declare a parseStart method")}}Parser.END=ParserEND;Parser.Error=ParserError;const f=(e,t)=>{for(t=String(t);t.length<e;)t="0"+t;return t},DateTime=globalThis.Date;class Date$1 extends DateTime{constructor(t){super(t),this.isDate=!0}toISOString(){return`${this.getUTCFullYear()}-${f(2,this.getUTCMonth()+1)}-${f(2,this.getUTCDate())}`}}const createDate=e=>{const t=new Date$1(e);if(isNaN(t))throw new TypeError("Invalid Datetime");return t};class FloatingDateTime extends Date$1{constructor(t){super(t+"Z"),this.isFloating=!0}toISOString(){const t=`${this.getUTCFullYear()}-${f(2,this.getUTCMonth()+1)}-${f(2,this.getUTCDate())}`,r=`${f(2,this.getUTCHours())}:${f(2,this.getUTCMinutes())}:${f(2,this.getUTCSeconds())}.${f(3,this.getUTCMilliseconds())}`;return`${t}T${r}`}}const createDateTime=e=>{const t=new Date$1(e);if(isNaN(t))throw new TypeError("Invalid Datetime");return t},createDateTimeFloat=e=>{const t=new FloatingDateTime(e);if(isNaN(t))throw new TypeError("Invalid Datetime");return t};class Time extends Date$1{constructor(t){super(`0000-01-01T${t}Z`),this.isTime=!0}toISOString(){return`${f(2,this.getUTCHours())}:${f(2,this.getUTCMinutes())}:${f(2,this.getUTCSeconds())}.${f(3,this.getUTCMilliseconds())}`}}const createTime=e=>{const t=new Time(e);if(isNaN(t))throw new TypeError("Invalid Datetime");return t},parserClass=makeParserClass(Parser);class TomlError extends Error{constructor(t){super(t),this.name="TomlError",Error.captureStackTrace&&Error.captureStackTrace(this,TomlError),this.fromTOML=!0,this.wrapped=null}}TomlError.wrap=e=>{const t=new TomlError(e.message);return t.code=e.code,t.wrapped=e,t};const CTRL_I=9,CTRL_J=10,CTRL_M=13,CTRL_CHAR_BOUNDARY=31,CHAR_SP=32,CHAR_QUOT=34,CHAR_NUM=35,CHAR_APOS=39,CHAR_PLUS=43,CHAR_COMMA=44,CHAR_HYPHEN=45,CHAR_PERIOD=46,CHAR_0=48,CHAR_1=49,CHAR_7=55,CHAR_9=57,CHAR_COLON=58,CHAR_EQUALS=61,CHAR_A=65,CHAR_E=69,CHAR_F=70,CHAR_T=84,CHAR_U=85,CHAR_Z=90,CHAR_LOWBAR=95,CHAR_a=97,CHAR_b=98,CHAR_e=101,CHAR_f=102,CHAR_i=105,CHAR_l=108,CHAR_n=110,CHAR_o=111,CHAR_r=114,CHAR_s=115,CHAR_t=116,CHAR_u=117,CHAR_x=120,CHAR_z=122,CHAR_LCUB=123,CHAR_RCUB=125,CHAR_LSQB=91,CHAR_BSOL=92,CHAR_RSQB=93,CHAR_DEL=127,SURROGATE_FIRST=55296,SURROGATE_LAST=57343,escapes={[CHAR_b]:"\b",[CHAR_t]:"	",[CHAR_n]:`
`,[CHAR_f]:"\f",[CHAR_r]:"\r",[CHAR_QUOT]:'"',[CHAR_BSOL]:"\\"};function isDigit(e){return e>=CHAR_0&&e<=CHAR_9}function isHexit(e){return e>=CHAR_A&&e<=CHAR_F||e>=CHAR_a&&e<=CHAR_f||e>=CHAR_0&&e<=CHAR_9}function isBit(e){return e===CHAR_1||e===CHAR_0}function isOctit(e){return e>=CHAR_0&&e<=CHAR_7}function isAlphaNumQuoteHyphen(e){return e>=CHAR_A&&e<=CHAR_Z||e>=CHAR_a&&e<=CHAR_z||e>=CHAR_0&&e<=CHAR_9||e===CHAR_APOS||e===CHAR_QUOT||e===CHAR_LOWBAR||e===CHAR_HYPHEN}function isAlphaNumHyphen(e){return e>=CHAR_A&&e<=CHAR_Z||e>=CHAR_a&&e<=CHAR_z||e>=CHAR_0&&e<=CHAR_9||e===CHAR_LOWBAR||e===CHAR_HYPHEN}const _type=Symbol("type"),_declared=Symbol("declared"),hasOwnProperty=Object.prototype.hasOwnProperty,defineProperty=Object.defineProperty,descriptor={configurable:!0,enumerable:!0,writable:!0,value:void 0};function hasKey(e,t){return hasOwnProperty.call(e,t)?!0:(t==="__proto__"&&defineProperty(e,"__proto__",descriptor),!1)}const INLINE_TABLE=Symbol("inline-table");function InlineTable(){return Object.defineProperties({},{[_type]:{value:INLINE_TABLE}})}function isInlineTable(e){return e===null||typeof e!="object"?!1:e[_type]===INLINE_TABLE}const TABLE=Symbol("table");function Table(){return Object.defineProperties({},{[_type]:{value:TABLE},[_declared]:{value:!1,writable:!0}})}function isTable(e){return e===null||typeof e!="object"?!1:e[_type]===TABLE}const _contentType=Symbol("content-type"),INLINE_LIST=Symbol("inline-list");function InlineList(e){return Object.defineProperties([],{[_type]:{value:INLINE_LIST},[_contentType]:{value:e}})}function isInlineList(e){return e===null||typeof e!="object"?!1:e[_type]===INLINE_LIST}const LIST=Symbol("list");function List(){return Object.defineProperties([],{[_type]:{value:LIST}})}function isList(e){return e===null||typeof e!="object"?!1:e[_type]===LIST}let _custom;try{const utilInspect=eval("require('util').inspect");_custom=utilInspect.custom}catch(e){}const _inspect=_custom||"inspect";class BoxedBigInt{constructor(t){try{this.value=globalThis.BigInt.asIntN(64,t)}catch{this.value=null}Object.defineProperty(this,_type,{value:INTEGER})}isNaN(){return this.value===null}toString(){return String(this.value)}[_inspect](){return`[BigInt: ${this.toString()}]}`}valueOf(){return this.value}}const INTEGER=Symbol("integer");function Integer(e){let t=Number(e);return Object.is(t,-0)&&(t=0),globalThis.BigInt&&!Number.isSafeInteger(t)?new BoxedBigInt(e):Object.defineProperties(new Number(t),{isNaN:{value:function(){return isNaN(this)}},[_type]:{value:INTEGER},[_inspect]:{value:()=>`[Integer: ${e}]`}})}function isInteger(e){return e===null||typeof e!="object"?!1:e[_type]===INTEGER}const FLOAT=Symbol("float");function Float(e){return Object.defineProperties(new Number(e),{[_type]:{value:FLOAT},[_inspect]:{value:()=>`[Float: ${e}]`}})}function isFloat(e){return e===null||typeof e!="object"?!1:e[_type]===FLOAT}function tomlType(e){const t=typeof e;if(t==="object"){if(e===null)return"null";if(e instanceof Date)return"datetime";if(_type in e)switch(e[_type]){case INLINE_TABLE:return"inline-table";case INLINE_LIST:return"inline-list";case TABLE:return"table";case LIST:return"list";case FLOAT:return"float";case INTEGER:return"integer"}}return t}function makeParserClass(e){class t extends e{constructor(){super(),this.ctx=this.obj=Table()}atEndOfWord(){return this.char===CHAR_NUM||this.char===CTRL_I||this.char===CHAR_SP||this.atEndOfLine()}atEndOfLine(){return this.char===e.END||this.char===CTRL_J||this.char===CTRL_M}parseStart(){if(this.char===e.END)return null;if(this.char===CHAR_LSQB)return this.call(this.parseTableOrList);if(this.char===CHAR_NUM)return this.call(this.parseComment);if(this.char===CTRL_J||this.char===CHAR_SP||this.char===CTRL_I||this.char===CTRL_M)return null;if(isAlphaNumQuoteHyphen(this.char))return this.callNow(this.parseAssignStatement);throw this.error(new TomlError(`Unknown character "${this.char}"`))}parseWhitespaceToEOL(){if(this.char===CHAR_SP||this.char===CTRL_I||this.char===CTRL_M)return null;if(this.char===CHAR_NUM)return this.goto(this.parseComment);if(this.char===e.END||this.char===CTRL_J)return this.return();throw this.error(new TomlError("Unexpected character, expected only whitespace or comments till end of line"))}parseAssignStatement(){return this.callNow(this.parseAssign,this.recordAssignStatement)}recordAssignStatement(n){let s=this.ctx,a=n.key.pop();for(let u of n.key){if(hasKey(s,u)&&!isTable(s[u]))throw this.error(new TomlError("Can't redefine existing key"));s=s[u]=s[u]||Table()}if(hasKey(s,a))throw this.error(new TomlError("Can't redefine existing key"));return s[_declared]=!0,isInteger(n.value)||isFloat(n.value)?s[a]=n.value.valueOf():s[a]=n.value,this.goto(this.parseWhitespaceToEOL)}parseAssign(){return this.callNow(this.parseKeyword,this.recordAssignKeyword)}recordAssignKeyword(n){return this.state.resultTable?this.state.resultTable.push(n):this.state.resultTable=[n],this.goto(this.parseAssignKeywordPreDot)}parseAssignKeywordPreDot(){if(this.char===CHAR_PERIOD)return this.next(this.parseAssignKeywordPostDot);if(this.char!==CHAR_SP&&this.char!==CTRL_I)return this.goto(this.parseAssignEqual)}parseAssignKeywordPostDot(){if(this.char!==CHAR_SP&&this.char!==CTRL_I)return this.callNow(this.parseKeyword,this.recordAssignKeyword)}parseAssignEqual(){if(this.char===CHAR_EQUALS)return this.next(this.parseAssignPreValue);throw this.error(new TomlError('Invalid character, expected "="'))}parseAssignPreValue(){return this.char===CHAR_SP||this.char===CTRL_I?null:this.callNow(this.parseValue,this.recordAssignValue)}recordAssignValue(n){return this.returnNow({key:this.state.resultTable,value:n})}parseComment(){do{if(this.char===e.END||this.char===CTRL_J)return this.return();if(this.char===CHAR_DEL||this.char<=CTRL_CHAR_BOUNDARY&&this.char!==CTRL_I)throw this.errorControlCharIn("comments")}while(this.nextChar())}parseTableOrList(){if(this.char===CHAR_LSQB)this.next(this.parseList);else return this.goto(this.parseTable)}parseTable(){return this.ctx=this.obj,this.goto(this.parseTableNext)}parseTableNext(){return this.char===CHAR_SP||this.char===CTRL_I?null:this.callNow(this.parseKeyword,this.parseTableMore)}parseTableMore(n){if(this.char===CHAR_SP||this.char===CTRL_I)return null;if(this.char===CHAR_RSQB){if(hasKey(this.ctx,n)&&(!isTable(this.ctx[n])||this.ctx[n][_declared]))throw this.error(new TomlError("Can't redefine existing key"));return this.ctx=this.ctx[n]=this.ctx[n]||Table(),this.ctx[_declared]=!0,this.next(this.parseWhitespaceToEOL)}else if(this.char===CHAR_PERIOD){if(!hasKey(this.ctx,n))this.ctx=this.ctx[n]=Table();else if(isTable(this.ctx[n]))this.ctx=this.ctx[n];else if(isList(this.ctx[n]))this.ctx=this.ctx[n][this.ctx[n].length-1];else throw this.error(new TomlError("Can't redefine existing key"));return this.next(this.parseTableNext)}else throw this.error(new TomlError("Unexpected character, expected whitespace, . or ]"))}parseList(){return this.ctx=this.obj,this.goto(this.parseListNext)}parseListNext(){return this.char===CHAR_SP||this.char===CTRL_I?null:this.callNow(this.parseKeyword,this.parseListMore)}parseListMore(n){if(this.char===CHAR_SP||this.char===CTRL_I)return null;if(this.char===CHAR_RSQB){if(hasKey(this.ctx,n)||(this.ctx[n]=List()),isInlineList(this.ctx[n]))throw this.error(new TomlError("Can't extend an inline array"));if(isList(this.ctx[n])){const s=Table();this.ctx[n].push(s),this.ctx=s}else throw this.error(new TomlError("Can't redefine an existing key"));return this.next(this.parseListEnd)}else if(this.char===CHAR_PERIOD){if(!hasKey(this.ctx,n))this.ctx=this.ctx[n]=Table();else{if(isInlineList(this.ctx[n]))throw this.error(new TomlError("Can't extend an inline array"));if(isInlineTable(this.ctx[n]))throw this.error(new TomlError("Can't extend an inline table"));if(isList(this.ctx[n]))this.ctx=this.ctx[n][this.ctx[n].length-1];else if(isTable(this.ctx[n]))this.ctx=this.ctx[n];else throw this.error(new TomlError("Can't redefine an existing key"))}return this.next(this.parseListNext)}else throw this.error(new TomlError("Unexpected character, expected whitespace, . or ]"))}parseListEnd(n){if(this.char===CHAR_RSQB)return this.next(this.parseWhitespaceToEOL);throw this.error(new TomlError("Unexpected character, expected whitespace, . or ]"))}parseValue(){if(this.char===e.END)throw this.error(new TomlError("Key without value"));if(this.char===CHAR_QUOT)return this.next(this.parseDoubleString);if(this.char===CHAR_APOS)return this.next(this.parseSingleString);if(this.char===CHAR_HYPHEN||this.char===CHAR_PLUS)return this.goto(this.parseNumberSign);if(this.char===CHAR_i)return this.next(this.parseInf);if(this.char===CHAR_n)return this.next(this.parseNan);if(isDigit(this.char))return this.goto(this.parseNumberOrDateTime);if(this.char===CHAR_t||this.char===CHAR_f)return this.goto(this.parseBoolean);if(this.char===CHAR_LSQB)return this.call(this.parseInlineList,this.recordValue);if(this.char===CHAR_LCUB)return this.call(this.parseInlineTable,this.recordValue);throw this.error(new TomlError("Unexpected character, expecting string, number, datetime, boolean, inline array or inline table"))}recordValue(n){return this.returnNow(n)}parseInf(){if(this.char===CHAR_n)return this.next(this.parseInf2);throw this.error(new TomlError('Unexpected character, expected "inf", "+inf" or "-inf"'))}parseInf2(){if(this.char===CHAR_f)return this.state.buf==="-"?this.return(-1/0):this.return(1/0);throw this.error(new TomlError('Unexpected character, expected "inf", "+inf" or "-inf"'))}parseNan(){if(this.char===CHAR_a)return this.next(this.parseNan2);throw this.error(new TomlError('Unexpected character, expected "nan"'))}parseNan2(){if(this.char===CHAR_n)return this.return(NaN);throw this.error(new TomlError('Unexpected character, expected "nan"'))}parseKeyword(){return this.char===CHAR_QUOT?this.next(this.parseBasicString):this.char===CHAR_APOS?this.next(this.parseLiteralString):this.goto(this.parseBareKey)}parseBareKey(){do{if(this.char===e.END)throw this.error(new TomlError("Key ended without value"));if(isAlphaNumHyphen(this.char))this.consume();else{if(this.state.buf.length===0)throw this.error(new TomlError("Empty bare keys are not allowed"));return this.returnNow()}}while(this.nextChar())}parseSingleString(){return this.char===CHAR_APOS?this.next(this.parseLiteralMultiStringMaybe):this.goto(this.parseLiteralString)}parseLiteralString(){do{if(this.char===CHAR_APOS)return this.return();if(this.atEndOfLine())throw this.error(new TomlError("Unterminated string"));if(this.char===CHAR_DEL||this.char<=CTRL_CHAR_BOUNDARY&&this.char!==CTRL_I)throw this.errorControlCharIn("strings");this.consume()}while(this.nextChar())}parseLiteralMultiStringMaybe(){return this.char===CHAR_APOS?this.next(this.parseLiteralMultiString):this.returnNow()}parseLiteralMultiString(){return this.char===CTRL_M?null:this.char===CTRL_J?this.next(this.parseLiteralMultiStringContent):this.goto(this.parseLiteralMultiStringContent)}parseLiteralMultiStringContent(){do{if(this.char===CHAR_APOS)return this.next(this.parseLiteralMultiEnd);if(this.char===e.END)throw this.error(new TomlError("Unterminated multi-line string"));if(this.char===CHAR_DEL||this.char<=CTRL_CHAR_BOUNDARY&&this.char!==CTRL_I&&this.char!==CTRL_J&&this.char!==CTRL_M)throw this.errorControlCharIn("strings");this.consume()}while(this.nextChar())}parseLiteralMultiEnd(){return this.char===CHAR_APOS?this.next(this.parseLiteralMultiEnd2):(this.state.buf+="'",this.goto(this.parseLiteralMultiStringContent))}parseLiteralMultiEnd2(){return this.char===CHAR_APOS?this.next(this.parseLiteralMultiEnd3):(this.state.buf+="''",this.goto(this.parseLiteralMultiStringContent))}parseLiteralMultiEnd3(){return this.char===CHAR_APOS?(this.state.buf+="'",this.next(this.parseLiteralMultiEnd4)):this.returnNow()}parseLiteralMultiEnd4(){return this.char===CHAR_APOS?(this.state.buf+="'",this.return()):this.returnNow()}parseDoubleString(){return this.char===CHAR_QUOT?this.next(this.parseMultiStringMaybe):this.goto(this.parseBasicString)}parseBasicString(){do{if(this.char===CHAR_BSOL)return this.call(this.parseEscape,this.recordEscapeReplacement);if(this.char===CHAR_QUOT)return this.return();if(this.atEndOfLine())throw this.error(new TomlError("Unterminated string"));if(this.char===CHAR_DEL||this.char<=CTRL_CHAR_BOUNDARY&&this.char!==CTRL_I)throw this.errorControlCharIn("strings");this.consume()}while(this.nextChar())}recordEscapeReplacement(n){return this.state.buf+=n,this.goto(this.parseBasicString)}parseMultiStringMaybe(){return this.char===CHAR_QUOT?this.next(this.parseMultiString):this.returnNow()}parseMultiString(){return this.char===CTRL_M?null:this.char===CTRL_J?this.next(this.parseMultiStringContent):this.goto(this.parseMultiStringContent)}parseMultiStringContent(){do{if(this.char===CHAR_BSOL)return this.call(this.parseMultiEscape,this.recordMultiEscapeReplacement);if(this.char===CHAR_QUOT)return this.next(this.parseMultiEnd);if(this.char===e.END)throw this.error(new TomlError("Unterminated multi-line string"));if(this.char===CHAR_DEL||this.char<=CTRL_CHAR_BOUNDARY&&this.char!==CTRL_I&&this.char!==CTRL_J&&this.char!==CTRL_M)throw this.errorControlCharIn("strings");this.consume()}while(this.nextChar())}errorControlCharIn(n){let s="\\u00";return this.char<16&&(s+="0"),s+=this.char.toString(16),this.error(new TomlError(`Control characters (codes < 0x1f and 0x7f) are not allowed in ${n}, use ${s} instead`))}recordMultiEscapeReplacement(n){return this.state.buf+=n,this.goto(this.parseMultiStringContent)}parseMultiEnd(){return this.char===CHAR_QUOT?this.next(this.parseMultiEnd2):(this.state.buf+='"',this.goto(this.parseMultiStringContent))}parseMultiEnd2(){return this.char===CHAR_QUOT?this.next(this.parseMultiEnd3):(this.state.buf+='""',this.goto(this.parseMultiStringContent))}parseMultiEnd3(){return this.char===CHAR_QUOT?(this.state.buf+='"',this.next(this.parseMultiEnd4)):this.returnNow()}parseMultiEnd4(){return this.char===CHAR_QUOT?(this.state.buf+='"',this.return()):this.returnNow()}parseMultiEscape(){return this.char===CTRL_M||this.char===CTRL_J?this.next(this.parseMultiTrim):this.char===CHAR_SP||this.char===CTRL_I?this.next(this.parsePreMultiTrim):this.goto(this.parseEscape)}parsePreMultiTrim(){if(this.char===CHAR_SP||this.char===CTRL_I)return null;if(this.char===CTRL_M||this.char===CTRL_J)return this.next(this.parseMultiTrim);throw this.error(new TomlError("Can't escape whitespace"))}parseMultiTrim(){return this.char===CTRL_J||this.char===CHAR_SP||this.char===CTRL_I||this.char===CTRL_M?null:this.returnNow()}parseEscape(){if(this.char in escapes)return this.return(escapes[this.char]);if(this.char===CHAR_u)return this.call(this.parseSmallUnicode,this.parseUnicodeReturn);if(this.char===CHAR_U)return this.call(this.parseLargeUnicode,this.parseUnicodeReturn);throw this.error(new TomlError("Unknown escape character: "+this.char))}parseUnicodeReturn(n){try{const s=parseInt(n,16);if(s>=SURROGATE_FIRST&&s<=SURROGATE_LAST)throw this.error(new TomlError("Invalid unicode, character in range 0xD800 - 0xDFFF is reserved"));return this.returnNow(String.fromCodePoint(s))}catch(s){throw this.error(TomlError.wrap(s))}}parseSmallUnicode(){if(isHexit(this.char)){if(this.consume(),this.state.buf.length>=4)return this.return()}else throw this.error(new TomlError("Invalid character in unicode sequence, expected hex"))}parseLargeUnicode(){if(isHexit(this.char)){if(this.consume(),this.state.buf.length>=8)return this.return()}else throw this.error(new TomlError("Invalid character in unicode sequence, expected hex"))}parseNumberSign(){return this.consume(),this.next(this.parseMaybeSignedInfOrNan)}parseMaybeSignedInfOrNan(){return this.char===CHAR_i?this.next(this.parseInf):this.char===CHAR_n?this.next(this.parseNan):this.callNow(this.parseNoUnder,this.parseNumberIntegerStart)}parseNumberIntegerStart(){return this.char===CHAR_0?(this.consume(),this.next(this.parseNumberIntegerExponentOrDecimal)):this.goto(this.parseNumberInteger)}parseNumberIntegerExponentOrDecimal(){return this.char===CHAR_PERIOD?(this.consume(),this.call(this.parseNoUnder,this.parseNumberFloat)):this.char===CHAR_E||this.char===CHAR_e?(this.consume(),this.next(this.parseNumberExponentSign)):this.returnNow(Integer(this.state.buf))}parseNumberInteger(){if(isDigit(this.char))this.consume();else{if(this.char===CHAR_LOWBAR)return this.call(this.parseNoUnder);if(this.char===CHAR_E||this.char===CHAR_e)return this.consume(),this.next(this.parseNumberExponentSign);if(this.char===CHAR_PERIOD)return this.consume(),this.call(this.parseNoUnder,this.parseNumberFloat);{const n=Integer(this.state.buf);if(n.isNaN())throw this.error(new TomlError("Invalid number"));return this.returnNow(n)}}}parseNoUnder(){if(this.char===CHAR_LOWBAR||this.char===CHAR_PERIOD||this.char===CHAR_E||this.char===CHAR_e)throw this.error(new TomlError("Unexpected character, expected digit"));if(this.atEndOfWord())throw this.error(new TomlError("Incomplete number"));return this.returnNow()}parseNoUnderHexOctBinLiteral(){if(this.char===CHAR_LOWBAR||this.char===CHAR_PERIOD)throw this.error(new TomlError("Unexpected character, expected digit"));if(this.atEndOfWord())throw this.error(new TomlError("Incomplete number"));return this.returnNow()}parseNumberFloat(){if(this.char===CHAR_LOWBAR)return this.call(this.parseNoUnder,this.parseNumberFloat);if(isDigit(this.char))this.consume();else return this.char===CHAR_E||this.char===CHAR_e?(this.consume(),this.next(this.parseNumberExponentSign)):this.returnNow(Float(this.state.buf))}parseNumberExponentSign(){if(isDigit(this.char))return this.goto(this.parseNumberExponent);if(this.char===CHAR_HYPHEN||this.char===CHAR_PLUS)this.consume(),this.call(this.parseNoUnder,this.parseNumberExponent);else throw this.error(new TomlError("Unexpected character, expected -, + or digit"))}parseNumberExponent(){if(isDigit(this.char))this.consume();else return this.char===CHAR_LOWBAR?this.call(this.parseNoUnder):this.returnNow(Float(this.state.buf))}parseNumberOrDateTime(){return this.char===CHAR_0?(this.consume(),this.next(this.parseNumberBaseOrDateTime)):this.goto(this.parseNumberOrDateTimeOnly)}parseNumberOrDateTimeOnly(){if(this.char===CHAR_LOWBAR)return this.call(this.parseNoUnder,this.parseNumberInteger);if(isDigit(this.char))this.consume(),this.state.buf.length>4&&this.next(this.parseNumberInteger);else return this.char===CHAR_E||this.char===CHAR_e?(this.consume(),this.next(this.parseNumberExponentSign)):this.char===CHAR_PERIOD?(this.consume(),this.call(this.parseNoUnder,this.parseNumberFloat)):this.char===CHAR_HYPHEN?this.goto(this.parseDateTime):this.char===CHAR_COLON?this.goto(this.parseOnlyTimeHour):this.returnNow(Integer(this.state.buf))}parseDateTimeOnly(){if(this.state.buf.length<4){if(isDigit(this.char))return this.consume();if(this.char===CHAR_COLON)return this.goto(this.parseOnlyTimeHour);throw this.error(new TomlError("Expected digit while parsing year part of a date"))}else{if(this.char===CHAR_HYPHEN)return this.goto(this.parseDateTime);throw this.error(new TomlError("Expected hyphen (-) while parsing year part of date"))}}parseNumberBaseOrDateTime(){return this.char===CHAR_b?(this.consume(),this.call(this.parseNoUnderHexOctBinLiteral,this.parseIntegerBin)):this.char===CHAR_o?(this.consume(),this.call(this.parseNoUnderHexOctBinLiteral,this.parseIntegerOct)):this.char===CHAR_x?(this.consume(),this.call(this.parseNoUnderHexOctBinLiteral,this.parseIntegerHex)):this.char===CHAR_PERIOD?this.goto(this.parseNumberInteger):isDigit(this.char)?this.goto(this.parseDateTimeOnly):this.returnNow(Integer(this.state.buf))}parseIntegerHex(){if(isHexit(this.char))this.consume();else{if(this.char===CHAR_LOWBAR)return this.call(this.parseNoUnderHexOctBinLiteral);{const n=Integer(this.state.buf);if(n.isNaN())throw this.error(new TomlError("Invalid number"));return this.returnNow(n)}}}parseIntegerOct(){if(isOctit(this.char))this.consume();else{if(this.char===CHAR_LOWBAR)return this.call(this.parseNoUnderHexOctBinLiteral);{const n=Integer(this.state.buf);if(n.isNaN())throw this.error(new TomlError("Invalid number"));return this.returnNow(n)}}}parseIntegerBin(){if(isBit(this.char))this.consume();else{if(this.char===CHAR_LOWBAR)return this.call(this.parseNoUnderHexOctBinLiteral);{const n=Integer(this.state.buf);if(n.isNaN())throw this.error(new TomlError("Invalid number"));return this.returnNow(n)}}}parseDateTime(){if(this.state.buf.length<4)throw this.error(new TomlError("Years less than 1000 must be zero padded to four characters"));return this.state.result=this.state.buf,this.state.buf="",this.next(this.parseDateMonth)}parseDateMonth(){if(this.char===CHAR_HYPHEN){if(this.state.buf.length<2)throw this.error(new TomlError("Months less than 10 must be zero padded to two characters"));return this.state.result+="-"+this.state.buf,this.state.buf="",this.next(this.parseDateDay)}else if(isDigit(this.char))this.consume();else throw this.error(new TomlError("Incomplete datetime"))}parseDateDay(){if(this.char===CHAR_T||this.char===CHAR_SP){if(this.state.buf.length<2)throw this.error(new TomlError("Days less than 10 must be zero padded to two characters"));return this.state.result+="-"+this.state.buf,this.state.buf="",this.next(this.parseStartTimeHour)}else{if(this.atEndOfWord())return this.returnNow(createDate(this.state.result+"-"+this.state.buf));if(isDigit(this.char))this.consume();else throw this.error(new TomlError("Incomplete datetime"))}}parseStartTimeHour(){return this.atEndOfWord()?this.returnNow(createDate(this.state.result)):this.goto(this.parseTimeHour)}parseTimeHour(){if(this.char===CHAR_COLON){if(this.state.buf.length<2)throw this.error(new TomlError("Hours less than 10 must be zero padded to two characters"));return this.state.result+="T"+this.state.buf,this.state.buf="",this.next(this.parseTimeMin)}else if(isDigit(this.char))this.consume();else throw this.error(new TomlError("Incomplete datetime"))}parseTimeMin(){if(this.state.buf.length<2&&isDigit(this.char))this.consume();else{if(this.state.buf.length===2&&this.char===CHAR_COLON)return this.state.result+=":"+this.state.buf,this.state.buf="",this.next(this.parseTimeSec);throw this.error(new TomlError("Incomplete datetime"))}}parseTimeSec(){if(isDigit(this.char)){if(this.consume(),this.state.buf.length===2)return this.state.result+=":"+this.state.buf,this.state.buf="",this.next(this.parseTimeZoneOrFraction)}else throw this.error(new TomlError("Incomplete datetime"))}parseOnlyTimeHour(){if(this.char===CHAR_COLON){if(this.state.buf.length<2)throw this.error(new TomlError("Hours less than 10 must be zero padded to two characters"));return this.state.result=this.state.buf,this.state.buf="",this.next(this.parseOnlyTimeMin)}else throw this.error(new TomlError("Incomplete time"))}parseOnlyTimeMin(){if(this.state.buf.length<2&&isDigit(this.char))this.consume();else{if(this.state.buf.length===2&&this.char===CHAR_COLON)return this.state.result+=":"+this.state.buf,this.state.buf="",this.next(this.parseOnlyTimeSec);throw this.error(new TomlError("Incomplete time"))}}parseOnlyTimeSec(){if(isDigit(this.char)){if(this.consume(),this.state.buf.length===2)return this.next(this.parseOnlyTimeFractionMaybe)}else throw this.error(new TomlError("Incomplete time"))}parseOnlyTimeFractionMaybe(){if(this.state.result+=":"+this.state.buf,this.char===CHAR_PERIOD)this.state.buf="",this.next(this.parseOnlyTimeFraction);else return this.return(createTime(this.state.result))}parseOnlyTimeFraction(){if(isDigit(this.char))this.consume();else if(this.atEndOfWord()){if(this.state.buf.length===0)throw this.error(new TomlError("Expected digit in milliseconds"));return this.returnNow(createTime(this.state.result+"."+this.state.buf))}else throw this.error(new TomlError("Unexpected character in datetime, expected period (.), minus (-), plus (+) or Z"))}parseTimeZoneOrFraction(){if(this.char===CHAR_PERIOD)this.consume(),this.next(this.parseDateTimeFraction);else if(this.char===CHAR_HYPHEN||this.char===CHAR_PLUS)this.consume(),this.next(this.parseTimeZoneHour);else{if(this.char===CHAR_Z)return this.consume(),this.return(createDateTime(this.state.result+this.state.buf));if(this.atEndOfWord())return this.returnNow(createDateTimeFloat(this.state.result+this.state.buf));throw this.error(new TomlError("Unexpected character in datetime, expected period (.), minus (-), plus (+) or Z"))}}parseDateTimeFraction(){if(isDigit(this.char))this.consume();else{if(this.state.buf.length===1)throw this.error(new TomlError("Expected digit in milliseconds"));if(this.char===CHAR_HYPHEN||this.char===CHAR_PLUS)this.consume(),this.next(this.parseTimeZoneHour);else{if(this.char===CHAR_Z)return this.consume(),this.return(createDateTime(this.state.result+this.state.buf));if(this.atEndOfWord())return this.returnNow(createDateTimeFloat(this.state.result+this.state.buf));throw this.error(new TomlError("Unexpected character in datetime, expected period (.), minus (-), plus (+) or Z"))}}}parseTimeZoneHour(){if(isDigit(this.char)){if(this.consume(),/\d\d$/.test(this.state.buf))return this.next(this.parseTimeZoneSep)}else throw this.error(new TomlError("Unexpected character in datetime, expected digit"))}parseTimeZoneSep(){if(this.char===CHAR_COLON)this.consume(),this.next(this.parseTimeZoneMin);else throw this.error(new TomlError("Unexpected character in datetime, expected colon"))}parseTimeZoneMin(){if(isDigit(this.char)){if(this.consume(),/\d\d$/.test(this.state.buf))return this.return(createDateTime(this.state.result+this.state.buf))}else throw this.error(new TomlError("Unexpected character in datetime, expected digit"))}parseBoolean(){if(this.char===CHAR_t)return this.consume(),this.next(this.parseTrue_r);if(this.char===CHAR_f)return this.consume(),this.next(this.parseFalse_a)}parseTrue_r(){if(this.char===CHAR_r)return this.consume(),this.next(this.parseTrue_u);throw this.error(new TomlError("Invalid boolean, expected true or false"))}parseTrue_u(){if(this.char===CHAR_u)return this.consume(),this.next(this.parseTrue_e);throw this.error(new TomlError("Invalid boolean, expected true or false"))}parseTrue_e(){if(this.char===CHAR_e)return this.return(!0);throw this.error(new TomlError("Invalid boolean, expected true or false"))}parseFalse_a(){if(this.char===CHAR_a)return this.consume(),this.next(this.parseFalse_l);throw this.error(new TomlError("Invalid boolean, expected true or false"))}parseFalse_l(){if(this.char===CHAR_l)return this.consume(),this.next(this.parseFalse_s);throw this.error(new TomlError("Invalid boolean, expected true or false"))}parseFalse_s(){if(this.char===CHAR_s)return this.consume(),this.next(this.parseFalse_e);throw this.error(new TomlError("Invalid boolean, expected true or false"))}parseFalse_e(){if(this.char===CHAR_e)return this.return(!1);throw this.error(new TomlError("Invalid boolean, expected true or false"))}parseInlineList(){if(this.char===CHAR_SP||this.char===CTRL_I||this.char===CTRL_M||this.char===CTRL_J)return null;if(this.char===e.END)throw this.error(new TomlError("Unterminated inline array"));return this.char===CHAR_NUM?this.call(this.parseComment):this.char===CHAR_RSQB?this.return(this.state.resultArr||InlineList()):this.callNow(this.parseValue,this.recordInlineListValue)}recordInlineListValue(n){return this.state.resultArr||(this.state.resultArr=InlineList(tomlType(n))),isFloat(n)||isInteger(n)?this.state.resultArr.push(n.valueOf()):this.state.resultArr.push(n),this.goto(this.parseInlineListNext)}parseInlineListNext(){if(this.char===CHAR_SP||this.char===CTRL_I||this.char===CTRL_M||this.char===CTRL_J)return null;if(this.char===CHAR_NUM)return this.call(this.parseComment);if(this.char===CHAR_COMMA)return this.next(this.parseInlineList);if(this.char===CHAR_RSQB)return this.goto(this.parseInlineList);throw this.error(new TomlError("Invalid character, expected whitespace, comma (,) or close bracket (])"))}parseInlineTable(){if(this.char===CHAR_SP||this.char===CTRL_I)return null;if(this.char===e.END||this.char===CHAR_NUM||this.char===CTRL_J||this.char===CTRL_M)throw this.error(new TomlError("Unterminated inline array"));return this.char===CHAR_RCUB?this.return(this.state.resultTable||InlineTable()):(this.state.resultTable||(this.state.resultTable=InlineTable()),this.callNow(this.parseAssign,this.recordInlineTableValue))}recordInlineTableValue(n){let s=this.state.resultTable,a=n.key.pop();for(let u of n.key){if(hasKey(s,u)&&(!isTable(s[u])||s[u][_declared]))throw this.error(new TomlError("Can't redefine existing key"));s=s[u]=s[u]||Table()}if(hasKey(s,a))throw this.error(new TomlError("Can't redefine existing key"));return isInteger(n.value)||isFloat(n.value)?s[a]=n.value.valueOf():s[a]=n.value,this.goto(this.parseInlineTableNext)}parseInlineTableNext(){if(this.char===CHAR_SP||this.char===CTRL_I)return null;if(this.char===e.END||this.char===CHAR_NUM||this.char===CTRL_J||this.char===CTRL_M)throw this.error(new TomlError("Unterminated inline array"));if(this.char===CHAR_COMMA)return this.next(this.parseInlineTablePostComma);if(this.char===CHAR_RCUB)return this.goto(this.parseInlineTable);throw this.error(new TomlError("Invalid character, expected whitespace, comma (,) or close bracket (])"))}parseInlineTablePostComma(){if(this.char===CHAR_SP||this.char===CTRL_I)return null;if(this.char===e.END||this.char===CHAR_NUM||this.char===CTRL_J||this.char===CTRL_M)throw this.error(new TomlError("Unterminated inline array"));if(this.char===CHAR_COMMA)throw this.error(new TomlError("Empty elements in inline tables are not permitted"));if(this.char===CHAR_RCUB)throw this.error(new TomlError("Trailing commas in inline tables are not permitted"));return this.goto(this.parseInlineTable)}}return t}function TomlParse(e){globalThis.Buffer&&globalThis.Buffer.isBuffer(e)&&(e=e.toString("utf8"));const t=new parserClass;try{return t.parse(e),t.finish()}catch(r){console.log(r)}}const pathParams=["pretrained_model_name_or_path","train_data_dir","reg_data_dir","output_dir","network_weights"],floatParmas=["learning_rate","unet_lr","text_encoder_lr","learning_rate_te","learning_rate_te1","learning_rate_te2"],needDeleteParams=["lycoris_algo","conv_dim","conv_alpha","dropout","dylora_unit","lokr_factor","down_lr_weight","mid_lr_weight","up_lr_weight","block_lr_zero_threshold","enable_block_weights","enable_preview","network_args_custom","optimizer_args_custom","enable_base_weight","prodigy_d0","prodigy_d_coef","ui_custom_params"],testNoneParams=["vae","reg_data_dir","network_weights","noise_offset","multires_noise_iterations","multires_noise_discount","caption_dropout_rate","network_dropout","scale_weight_norms","gpu_ids"],conflictParams=[["cache_text_encoder_outputs","shuffle_caption"],["noise_offset","multires_noise_iterations"],["cache_latents","color_aug"],["cache_latents","random_crop"]],checkParams=e=>{let t={warnings:[],errors:[]};const r=n=>!!n;e.optimizer_type.startsWith("DAdapt")&&e.lr_scheduler!="constant"&&t.warnings.push("\u60A8\u4F7F\u7528\u4E86 DAdaptation \u7CFB\u5217\u4F18\u5316\u5668\uFF0C\u82E5\u60F3\u5229\u7528\u5B83\u83B7\u5F97\u5B66\u4E60\u7387\uFF0C\u8BF7\u5C06 lr_scheduler \u8BBE\u7F6E\u4E3A constant"),e.optimizer_type.startsWith("prodigy")&&(e.unet_lr!=1||e.text_encoder_lr!=1)&&t.warnings.push("\u60A8\u4F7F\u7528\u4E86 prodigy \u4F18\u5316\u5668\uFF0C\u82E5\u60F3\u5229\u7528\u5B83\u83B7\u5F97\u5B66\u4E60\u7387\uFF0C\u8BF7\u5C06 unet_lr\u3001text_encoder_lr \u8BBE\u7F6E\u4E3A 1"),e.network_module=="networks.oft"&&e.model_train_type!="sdxl-lora"&&t.errors.push("OFT \u76EE\u524D\u4EC5\u5BF9 SDXL \u53EF\u7528");for(let n of conflictParams)r(e[n[0]])&&r(e[n[1]])&&t.errors.push(`\u53C2\u6570 ${n[0]} \u4E0E ${n[1]} \u51B2\u7A81\uFF0C\u8BF7\u53EA\u542F\u7528\u5176\u4E2D\u4E00\u4E2A`);return t},parseParamsRe=e=>{for(const t of floatParmas){if(!e.hasOwnProperty(t))continue;let r=e[t].toExponential();r.length<=6?e[t]=r:e[t]=e[t].toString()}return e},isSDXLModel=e=>{var t;return(t=e.model_train_type)==null?void 0:t.startsWith("sdxl")},parseParams=(e,t)=>{if(e.network_args=[],e.optimizer_args=[],t=="lora-basic"){let r=e;e=clone(BasicLoraParams),e=Object.assign(e,r)}e.model_train_type=="sd-dreambooth"?(delete e.learning_rate_te1,delete e.learning_rate_te2):isSDXLModel(e)&&(delete e.learning_rate_te,delete e.stop_text_encoder_training,(e.learning_rate_te1||e.learning_rate_te2)&&(e.train_text_encoder=!0)),isSDXLModel(e)&&delete e.clip_skip,e.network_module=="lycoris.kohya"?(e.network_args=[...e.network_args,"conv_dim="+e.conv_dim,"conv_alpha="+e.conv_alpha,"dropout="+e.dropout,"algo="+e.lycoris_algo],e.lokr_factor&&e.network_args.push(`factor=${e.lokr_factor}`)):e.network_module=="networks.dylora"&&(e.network_args=[...e.network_args,"unit="+e.dylora_unit]),e.optimizer_type.toLowerCase().startsWith("dada")?((e.optimizer_type=="DAdaptation"||e.optimizer_type=="DAdaptAdam")&&(e.optimizer_args=["decouple=True","weight_decay=0.01"]),e.learning_rate=1,e.unet_lr=1,e.text_encoder_lr=1):e.optimizer_type.toLowerCase()=="prodigy"&&(e.optimizer_args=["decouple=True","weight_decay=0.01","use_bias_correction=True",`d_coef=${e.prodigy_d_coef}`],e.lr_warmup_steps&&e.optimizer_args.push("safeguard_warmup=True"),e.prodigy_d0&&e.optimizer_args.push(`d0=${e.prodigy_d0}`)),e.enable_block_weights&&(e.network_args=[...e.network_args,"down_lr_weight="+e.down_lr_weight,"mid_lr_weight="+e.mid_lr_weight,"up_lr_weight="+e.up_lr_weight,"block_lr_zero_threshold="+e.block_lr_zero_threshold],delete e.block_lr_zero_threshold),e.enable_base_weight?(e.base_weights&&(e.base_weights=e.base_weights.split(`
`)),e.base_weights_multiplier&&(e.base_weights_multiplier=e.base_weights_multiplier.split(`
`).map(r=>parseFloat(r)))):(delete e.base_weights,delete e.base_weights_multiplier),e.network_args_custom&&(e.network_args=[...e.network_args,...e.network_args_custom]),e.optimizer_args_custom&&(e.optimizer_args=[...e.optimizer_args,...e.optimizer_args_custom]),e.enable_preview||(delete e.sample_prompts,delete e.sample_sampler,delete e.sample_every_n_epochs);for(const r of floatParmas)if(e.hasOwnProperty(r)){const n=parseFloat(e[r]);e[r]=Number.isNaN(n)?0:n}for(const r of testNoneParams)if(e.hasOwnProperty(r)){let n=e[r];(n===0||n===""||Array.isArray(n)&&n.length===0)&&delete e[r]}for(const r of pathParams)e.hasOwnProperty(r)&&e[r]&&(e[r]=e[r].replaceAll("\\","/"));if(["network_args","optimizer_args"].forEach(r=>{e[r].length==0&&delete e[r]}),e.ui_custom_params){let r;try{r=TomlParse(e.ui_custom_params),r&&typeof r=="object"&&(e=Object.assign(e,r))}catch(n){console.error(n)}}for(const r of needDeleteParams)e.hasOwnProperty(r)&&delete e[r];return e.gpu_ids&&(e.gpu_ids=e.gpu_ids.map(r=>r.match(/GPU (\d+):/)[1])),e},BasicLoraParams={pretrained_model_name_or_path:"./sd-models/model.safetensors",train_data_dir:"./train/aki",resolution:"512,512",enable_bucket:!0,min_bucket_reso:256,max_bucket_reso:1024,output_name:"aki",output_dir:"./output",save_model_as:"safetensors",save_every_n_epochs:2,max_train_epochs:10,train_batch_size:1,network_train_unet_only:!1,network_train_text_encoder_only:!1,learning_rate:1e-4,unet_lr:1e-4,text_encoder_lr:1e-5,lr_scheduler:"cosine_with_restarts",optimizer_type:"AdamW8bit",lr_scheduler_num_cycles:1,network_module:"networks.lora",network_dim:32,network_alpha:32,logging_dir:"./logs",caption_extension:".txt",shuffle_caption:!0,keep_tokens:0,max_token_length:255,seed:1337,prior_loss_weight:1,clip_skip:2,mixed_precision:"fp16",save_precision:"fp16",xformers:!0,cache_latents:!0,persistent_data_loader_workers:!0},SAMPLE_PROMPTS_DEFAULT="(masterpiece, best quality:1.2), 1girl, solo, --n lowres, bad anatomy, bad hands, text, error, missing fingers, extra digit, fewer digits, cropped, worst quality, low quality, normal quality, jpeg artifacts,signature, watermark, username, blurry,  --w 512  --h 768  --l 7  --s 24  --d 1337",SAMPLE_PROMPTS_DESCRIPTION="\u9884\u89C8\u56FE\u751F\u6210\u53C2\u6570\u3002\u53EF\u586B\u5199\u76F4\u63A5\u586B\u5199\u53C2\u6570\uFF0C\u6216\u5355\u72EC\u5199\u5165txt\u6587\u4EF6\u586B\u5199\u8DEF\u5F84<br>`--n` \u540E\u65B9\u4E3A\u53CD\u5411\u63D0\u793A\u8BCD<br>`--w`\u5BBD\uFF0C`--h`\u9AD8<br>`--l`: CFG Scale<br>`--s`: \u8FED\u4EE3\u6B65\u6570<br>`--d`: \u79CD\u5B50",createSchemaLoraBasic=()=>Schema.intersect([Schema.object({pretrained_model_name_or_path:Schema.string().role("filepicker").default("./sd-models/model.safetensors").description("\u5E95\u6A21\u6587\u4EF6\u8DEF\u5F84")}).description("\u8BAD\u7EC3\u7528\u6A21\u578B"),Schema.object({train_data_dir:Schema.string().role("filepicker",{type:"folder"}).default("./train/aki").description("\u8BAD\u7EC3\u6570\u636E\u96C6\u8DEF\u5F84"),reg_data_dir:Schema.string().role("filepicker",{type:"folder"}).description("\u6B63\u5219\u5316\u6570\u636E\u96C6\u8DEF\u5F84\u3002\u9ED8\u8BA4\u7559\u7A7A\uFF0C\u4E0D\u4F7F\u7528\u6B63\u5219\u5316\u56FE\u50CF"),resolution:Schema.string().default("512,512").description("\u8BAD\u7EC3\u56FE\u7247\u5206\u8FA8\u7387\uFF0C\u5BBDx\u9AD8\u3002\u652F\u6301\u975E\u6B63\u65B9\u5F62\uFF0C\u4F46\u5FC5\u987B\u662F 64 \u500D\u6570\u3002")}).description("\u6570\u636E\u96C6\u8BBE\u7F6E"),Schema.object({output_name:Schema.string().default("aki").description("\u6A21\u578B\u4FDD\u5B58\u540D\u79F0"),output_dir:Schema.string().default("./output").role("filepicker",{type:"folder"}).description("\u6A21\u578B\u4FDD\u5B58\u6587\u4EF6\u5939"),save_every_n_epochs:Schema.number().default(2).description("\u6BCF N epoch\uFF08\u8F6E\uFF09\u81EA\u52A8\u4FDD\u5B58\u4E00\u6B21\u6A21\u578B")}).description("\u4FDD\u5B58\u8BBE\u7F6E"),Schema.object({max_train_epochs:Schema.number().min(1).default(10).description("\u6700\u5927\u8BAD\u7EC3 epoch\uFF08\u8F6E\u6570\uFF09"),train_batch_size:Schema.number().min(1).default(1).description("\u6279\u91CF\u5927\u5C0F")}).description("\u8BAD\u7EC3\u76F8\u5173\u53C2\u6570"),Schema.intersect([Schema.object({unet_lr:Schema.string().default("1e-4").description("U-Net \u5B66\u4E60\u7387"),text_encoder_lr:Schema.string().default("1e-5").description("\u6587\u672C\u7F16\u7801\u5668\u5B66\u4E60\u7387"),lr_scheduler:Schema.union(["cosine","cosine_with_restarts","constant","constant_with_warmup"]).default("cosine_with_restarts").description("\u5B66\u4E60\u7387\u8C03\u5EA6\u5668\u8BBE\u7F6E"),lr_warmup_steps:Schema.number().default(0).description("\u5B66\u4E60\u7387\u9884\u70ED\u6B65\u6570")}).description("\u5B66\u4E60\u7387\u4E0E\u4F18\u5316\u5668\u8BBE\u7F6E"),Schema.union([Schema.object({lr_scheduler:Schema.const("cosine_with_restarts"),lr_scheduler_num_cycles:Schema.number().default(1).description("\u91CD\u542F\u6B21\u6570")}),Schema.object({})]),Schema.object({optimizer_type:Schema.union(["AdamW8bit","Lion"]).default("AdamW8bit").description("\u4F18\u5316\u5668\u8BBE\u7F6E")})]),Schema.intersect([Schema.object({enable_preview:Schema.boolean().default(!1).description("\u542F\u7528\u8BAD\u7EC3\u9884\u89C8\u56FE")}).description("\u8BAD\u7EC3\u9884\u89C8\u56FE\u8BBE\u7F6E"),Schema.union([Schema.object({enable_preview:Schema.const(!0).required(),sample_prompts:Schema.string().role("textarea").default(SAMPLE_PROMPTS_DEFAULT).description(SAMPLE_PROMPTS_DESCRIPTION),sample_sampler:Schema.union(["ddim","pndm","lms","euler","euler_a","heun","dpm_2","dpm_2_a","dpmsolver","dpmsolver++","dpmsingle","k_lms","k_euler","k_euler_a","k_dpm_2","k_dpm_2_a"]).default("euler_a").description("\u751F\u6210\u9884\u89C8\u56FE\u6240\u7528\u91C7\u6837\u5668"),sample_every_n_epochs:Schema.number().default(2).description("\u6BCF N \u4E2A epoch \u751F\u6210\u4E00\u6B21\u9884\u89C8\u56FE")}),Schema.object({})])]),Schema.intersect([Schema.object({network_weights:Schema.string().role("filepicker").description("\u4ECE\u5DF2\u6709\u7684 LoRA \u6A21\u578B\u4E0A\u7EE7\u7EED\u8BAD\u7EC3\uFF0C\u586B\u5199\u8DEF\u5F84"),network_dim:Schema.number().min(8).max(256).step(8).default(32).description("\u7F51\u7EDC\u7EF4\u5EA6\uFF0C\u5E38\u7528 4~128\uFF0C\u4E0D\u662F\u8D8A\u5927\u8D8A\u597D"),network_alpha:Schema.number().min(1).default(32).description("\u5E38\u7528\u503C\uFF1A\u7B49\u4E8E network_dim \u6216 network_dim*1/2 \u6216 1\u3002\u4F7F\u7528\u8F83\u5C0F\u7684 alpha \u9700\u8981\u63D0\u5347\u5B66\u4E60\u7387\u3002")}).description("\u7F51\u7EDC\u8BBE\u7F6E")]),Schema.object({shuffle_caption:Schema.boolean().default(!0).description("\u8BAD\u7EC3\u65F6\u968F\u673A\u6253\u4E71 tokens"),keep_tokens:Schema.number().min(0).max(255).step(1).default(0).description("\u5728\u968F\u673A\u6253\u4E71 tokens \u65F6\uFF0C\u4FDD\u7559\u524D N \u4E2A\u4E0D\u53D8")}).description("caption \u9009\u9879"),Schema.object({mixed_precision:Schema.union(["no","fp16","bf16"]).default("fp16").description("\u6DF7\u5408\u7CBE\u5EA6"),no_half_vae:Schema.boolean().description("\u4E0D\u4F7F\u7528\u534A\u7CBE\u5EA6 VAE\uFF0C\u5F53\u51FA\u73B0 NaN detected in latents \u62A5\u9519\u65F6\u4F7F\u7528"),xformers:Schema.boolean().default(!0).description("\u542F\u7528 xformers"),cache_latents:Schema.boolean().default(!0).description("\u7F13\u5B58\u56FE\u50CF latent")}).description("\u901F\u5EA6\u4F18\u5316\u9009\u9879")]),createSchemaLoraMaster=()=>Schema.intersect([Schema.intersect([Schema.object({model_train_type:Schema.union(["sd-lora","sdxl-lora"]).default("sd-lora").description("\u8BAD\u7EC3\u79CD\u7C7B"),pretrained_model_name_or_path:Schema.string().role("filepicker").default("./sd-models/model.safetensors").description("\u5E95\u6A21\u6587\u4EF6\u8DEF\u5F84"),resume:Schema.string().role("filepicker").description("\u4ECE\u67D0\u4E2A `save_state` \u4FDD\u5B58\u7684\u4E2D\u65AD\u72B6\u6001\u7EE7\u7EED\u8BAD\u7EC3\uFF0C\u586B\u5199\u6587\u4EF6\u8DEF\u5F84"),vae:Schema.string().role("filepicker").description("(\u53EF\u9009) VAE \u6A21\u578B\u6587\u4EF6\u8DEF\u5F84\uFF0C\u4F7F\u7528\u5916\u7F6E VAE \u6587\u4EF6\u8986\u76D6\u6A21\u578B\u5185\u672C\u8EAB\u7684"),v2:Schema.boolean().default(!1).description("\u5E95\u6A21\u4E3A sd2.0 \u4EE5\u540E\u7684\u7248\u672C\u9700\u8981\u542F\u7528")}).description("\u8BAD\u7EC3\u7528\u6A21\u578B"),Schema.union([Schema.object({v2:Schema.const(!0).required(),v_parameterization:Schema.boolean().default(!1).description("v-parameterization \u5B66\u4E60"),scale_v_pred_loss_like_noise_pred:Schema.boolean().default(!1).description("\u7F29\u653E v-prediction \u635F\u5931\uFF08\u4E0Ev-parameterization\u914D\u5408\u4F7F\u7528\uFF09")}),Schema.object({})])]),Schema.object({train_data_dir:Schema.string().role("filepicker",{type:"folder"}).default("./train/aki").description("\u8BAD\u7EC3\u6570\u636E\u96C6\u8DEF\u5F84"),reg_data_dir:Schema.string().role("filepicker",{type:"folder"}).description("\u6B63\u5219\u5316\u6570\u636E\u96C6\u8DEF\u5F84\u3002\u9ED8\u8BA4\u7559\u7A7A\uFF0C\u4E0D\u4F7F\u7528\u6B63\u5219\u5316\u56FE\u50CF"),prior_loss_weight:Schema.number().step(.1).default(1).description("\u6B63\u5219\u5316 - \u5148\u9A8C\u635F\u5931\u6743\u91CD"),resolution:Schema.string().default("512,512").description("\u8BAD\u7EC3\u56FE\u7247\u5206\u8FA8\u7387\uFF0C\u5BBDx\u9AD8\u3002\u652F\u6301\u975E\u6B63\u65B9\u5F62\uFF0C\u4F46\u5FC5\u987B\u662F 64 \u500D\u6570\u3002"),enable_bucket:Schema.boolean().default(!0).description("\u542F\u7528 arb \u6876\u4EE5\u5141\u8BB8\u975E\u56FA\u5B9A\u5BBD\u9AD8\u6BD4\u7684\u56FE\u7247"),min_bucket_reso:Schema.number().default(256).description("arb \u6876\u6700\u5C0F\u5206\u8FA8\u7387"),max_bucket_reso:Schema.number().default(1024).description("arb \u6876\u6700\u5927\u5206\u8FA8\u7387"),bucket_reso_steps:Schema.number().default(64).description("arb \u6876\u5206\u8FA8\u7387\u5212\u5206\u5355\u4F4D\uFF0CSDXL \u53EF\u4EE5\u4F7F\u7528 32")}).description("\u6570\u636E\u96C6\u8BBE\u7F6E"),Schema.object({output_name:Schema.string().default("aki").description("\u6A21\u578B\u4FDD\u5B58\u540D\u79F0"),output_dir:Schema.string().role("filepicker",{type:"folder"}).default("./output").description("\u6A21\u578B\u4FDD\u5B58\u6587\u4EF6\u5939"),save_model_as:Schema.union(["safetensors","pt","ckpt"]).default("safetensors").description("\u6A21\u578B\u4FDD\u5B58\u683C\u5F0F"),save_precision:Schema.union(["fp16","float","bf16"]).default("fp16").description("\u6A21\u578B\u4FDD\u5B58\u7CBE\u5EA6"),save_every_n_epochs:Schema.number().default(2).description("\u6BCF N epoch\uFF08\u8F6E\uFF09\u81EA\u52A8\u4FDD\u5B58\u4E00\u6B21\u6A21\u578B"),save_state:Schema.boolean().description("\u4FDD\u5B58\u8BAD\u7EC3\u72B6\u6001 \u914D\u5408 `resume` \u53C2\u6570\u53EF\u4EE5\u7EE7\u7EED\u4ECE\u67D0\u4E2A\u72B6\u6001\u8BAD\u7EC3")}).description("\u4FDD\u5B58\u8BBE\u7F6E"),Schema.object({max_train_epochs:Schema.number().min(1).default(10).description("\u6700\u5927\u8BAD\u7EC3 epoch\uFF08\u8F6E\u6570\uFF09"),train_batch_size:Schema.number().min(1).default(1).description("\u6279\u91CF\u5927\u5C0F"),gradient_checkpointing:Schema.boolean().default(!1).description("\u68AF\u5EA6\u68C0\u67E5\u70B9"),gradient_accumulation_steps:Schema.number().min(1).description("\u68AF\u5EA6\u7D2F\u52A0\u6B65\u6570"),network_train_unet_only:Schema.boolean().default(!1).description("\u4EC5\u8BAD\u7EC3 U-Net"),network_train_text_encoder_only:Schema.boolean().default(!1).description("\u4EC5\u8BAD\u7EC3\u6587\u672C\u7F16\u7801\u5668")}).description("\u8BAD\u7EC3\u76F8\u5173\u53C2\u6570"),Schema.intersect([Schema.object({learning_rate:Schema.string().default("1e-4").description("\u603B\u5B66\u4E60\u7387\uFF0C\u5728\u5206\u5F00\u8BBE\u7F6E U-Net \u4E0E\u6587\u672C\u7F16\u7801\u5668\u5B66\u4E60\u7387\u540E\u8FD9\u4E2A\u503C\u5931\u6548\u3002"),unet_lr:Schema.string().default("1e-4").description("U-Net \u5B66\u4E60\u7387"),text_encoder_lr:Schema.string().default("1e-5").description("\u6587\u672C\u7F16\u7801\u5668\u5B66\u4E60\u7387"),lr_scheduler:Schema.union(["linear","cosine","cosine_with_restarts","polynomial","constant","constant_with_warmup"]).default("cosine_with_restarts").description("\u5B66\u4E60\u7387\u8C03\u5EA6\u5668\u8BBE\u7F6E"),lr_warmup_steps:Schema.number().default(0).description("\u5B66\u4E60\u7387\u9884\u70ED\u6B65\u6570")}).description("\u5B66\u4E60\u7387\u4E0E\u4F18\u5316\u5668\u8BBE\u7F6E"),Schema.union([Schema.object({lr_scheduler:Schema.const("cosine_with_restarts"),lr_scheduler_num_cycles:Schema.number().default(1).description("\u91CD\u542F\u6B21\u6570")}),Schema.object({})]),Schema.object({optimizer_type:Schema.union(["AdamW","AdamW8bit","PagedAdamW8bit","Lion","Lion8bit","PagedLion8bit","SGDNesterov","SGDNesterov8bit","DAdaptation","DAdaptAdam","DAdaptAdaGrad","DAdaptAdanIP","DAdaptLion","DAdaptSGD","AdaFactor","Prodigy"]).default("AdamW8bit").description("\u4F18\u5316\u5668\u8BBE\u7F6E"),min_snr_gamma:Schema.number().step(.1).description("\u6700\u5C0F\u4FE1\u566A\u6BD4\u4F3D\u9A6C\u503C\uFF0C\u5982\u679C\u542F\u7528\u63A8\u8350\u4E3A 5")}),Schema.union([Schema.object({optimizer_type:Schema.const("Prodigy").required(),prodigy_d0:Schema.string(),prodigy_d_coef:Schema.string().default("2.0")}),Schema.object({})]),Schema.object({optimizer_args_custom:Schema.array(String).role("table").description("\u81EA\u5B9A\u4E49 optimizer_args\uFF0C\u4E00\u884C\u4E00\u4E2A")})]),Schema.intersect([Schema.object({network_module:Schema.union(["networks.lora","networks.dylora","networks.oft","lycoris.kohya"]).default("networks.lora").description("\u8BAD\u7EC3\u7F51\u7EDC\u6A21\u5757"),network_weights:Schema.string().role("filepicker").description("\u4ECE\u5DF2\u6709\u7684 LoRA \u6A21\u578B\u4E0A\u7EE7\u7EED\u8BAD\u7EC3\uFF0C\u586B\u5199\u8DEF\u5F84"),network_dim:Schema.number().min(1).default(32).description("\u7F51\u7EDC\u7EF4\u5EA6\uFF0C\u5E38\u7528 4~128\uFF0C\u4E0D\u662F\u8D8A\u5927\u8D8A\u597D"),network_alpha:Schema.number().min(1).default(32).description("\u5E38\u7528\u503C\uFF1A\u7B49\u4E8E network_dim \u6216 network_dim*1/2 \u6216 1\u3002\u4F7F\u7528\u8F83\u5C0F\u7684 alpha \u9700\u8981\u63D0\u5347\u5B66\u4E60\u7387\u3002"),network_dropout:Schema.number().step(.01).default(0).description("dropout \u6982\u7387 \uFF08\u4E0E lycoris \u4E0D\u517C\u5BB9\uFF0C\u9700\u8981\u7528 lycoris \u81EA\u5E26\u7684\uFF09"),scale_weight_norms:Schema.number().step(.01).min(0).description("\u6700\u5927\u8303\u6570\u6B63\u5219\u5316\u3002\u5982\u679C\u4F7F\u7528\uFF0C\u63A8\u8350\u4E3A 1"),network_args_custom:Schema.array(String).role("table").description("\u81EA\u5B9A\u4E49 network_args\uFF0C\u4E00\u884C\u4E00\u4E2A"),enable_block_weights:Schema.boolean().default(!1).description("\u542F\u7528\u5206\u5C42\u5B66\u4E60\u7387\u8BAD\u7EC3\uFF08\u53EA\u652F\u6301\u7F51\u7EDC\u6A21\u5757 networks.lora\uFF09"),enable_base_weight:Schema.boolean().default(!1).description("\u542F\u7528\u57FA\u7840\u6743\u91CD\uFF08\u5DEE\u5F02\u70BC\u4E39\uFF09")}).description("\u7F51\u7EDC\u8BBE\u7F6E"),Schema.union([Schema.object({network_module:Schema.const("lycoris.kohya").required(),lycoris_algo:Schema.union(["locon","loha","lokr","ia3","dylora"]).default("locon").description("LyCORIS \u7F51\u7EDC\u7B97\u6CD5"),conv_dim:Schema.number().default(4),conv_alpha:Schema.number().default(1),dropout:Schema.number().step(.01).default(0).description("dropout \u6982\u7387\u3002\u63A8\u8350 0~0.5\uFF0CLoHa/LoKr/(IA)^3\u6682\u4E0D\u652F\u6301"),train_norm:Schema.boolean().default(!1).description("\u8BAD\u7EC3 Norm \u5C42\uFF0C\u4E0D\u652F\u6301 (IA)^3")}),Schema.object({network_module:Schema.const("networks.dylora").required(),dylora_unit:Schema.number().min(1).default(4).description("\u5206\u5272\u5757\u6570\u5355\u4F4D\uFF0C\u6700\u5C0F 1 \u4E5F\u6700\u6162\u3002\u4E00\u822C4\u30018\u300112\u300116\u8FD9\u51E0\u4E2A\u9009")}),Schema.object({})]),Schema.union([Schema.object({lycoris_algo:Schema.const("lokr").required(),lokr_factor:Schema.number().min(-1).default(-1).description("\u5E38\u7528 `4~\u65E0\u7A77`\uFF08\u586B\u5199 -1 \u4E3A\u65E0\u7A77\uFF09")}),Schema.object({})]),Schema.union([Schema.object({enable_block_weights:Schema.const(!0).required(),down_lr_weight:Schema.string().role("folder").default("1,1,1,1,1,1,1,1,1,1,1,1").description("U-Net \u7684 Encoder \u5C42\u5206\u5C42\u5B66\u4E60\u7387\u6743\u91CD\uFF0C\u5171 12 \u5C42"),mid_lr_weight:Schema.string().role("folder").default("1").description("U-Net \u7684 Mid \u5C42\u5206\u5C42\u5B66\u4E60\u7387\u6743\u91CD\uFF0C\u5171 1 \u5C42"),up_lr_weight:Schema.string().role("folder").default("1,1,1,1,1,1,1,1,1,1,1,1").description("U-Net \u7684 Decoder \u5C42\u5206\u5C42\u5B66\u4E60\u7387\u6743\u91CD\uFF0C\u5171 12 \u5C42"),block_lr_zero_threshold:Schema.number().step(.01).default(0).description("\u5206\u5C42\u5B66\u4E60\u7387\u7F6E 0 \u9608\u503C")}),Schema.object({})]),Schema.union([Schema.object({enable_base_weight:Schema.const(!0).required(),base_weights:Schema.string().role("textarea").description("\u5408\u5E76\u5165\u5E95\u6A21\u7684 LoRA \u8DEF\u5F84\uFF0C\u4E00\u884C\u4E00\u4E2A\u8DEF\u5F84"),base_weights_multiplier:Schema.string().role("textarea").description("\u5408\u5E76\u5165\u5E95\u6A21\u7684 LoRA \u6743\u91CD\uFF0C\u4E00\u884C\u4E00\u4E2A\u6570\u5B57")}),Schema.object({})])]),Schema.intersect([Schema.object({enable_preview:Schema.boolean().default(!1).description("\u542F\u7528\u8BAD\u7EC3\u9884\u89C8\u56FE")}).description("\u8BAD\u7EC3\u9884\u89C8\u56FE\u8BBE\u7F6E"),Schema.union([Schema.object({enable_preview:Schema.const(!0).required(),sample_prompts:Schema.string().role("textarea").default(SAMPLE_PROMPTS_DEFAULT).description(SAMPLE_PROMPTS_DESCRIPTION),sample_sampler:Schema.union(["ddim","pndm","lms","euler","euler_a","heun","dpm_2","dpm_2_a","dpmsolver","dpmsolver++","dpmsingle","k_lms","k_euler","k_euler_a","k_dpm_2","k_dpm_2_a"]).default("euler_a").description("\u751F\u6210\u9884\u89C8\u56FE\u6240\u7528\u91C7\u6837\u5668"),sample_every_n_epochs:Schema.number().default(2).description("\u6BCF N \u4E2A epoch \u751F\u6210\u4E00\u6B21\u9884\u89C8\u56FE")}),Schema.object({})])]),Schema.intersect([Schema.object({log_with:Schema.union(["tensorboard","wandb"]).default("tensorboard").description("\u65E5\u5FD7\u6A21\u5757"),log_prefix:Schema.string().description("\u65E5\u5FD7\u524D\u7F00"),log_tracker_name:Schema.string().description("\u65E5\u5FD7\u8FFD\u8E2A\u5668\u540D\u79F0"),logging_dir:Schema.string().default("./logs").description("\u65E5\u5FD7\u4FDD\u5B58\u6587\u4EF6\u5939")}).description("\u65E5\u5FD7\u8BBE\u7F6E"),Schema.union([Schema.object({log_with:Schema.const("wandb").required(),wandb_api_key:Schema.string().required().description("wandb \u7684 api \u5BC6\u94A5")}),Schema.object({})])]),Schema.object({caption_extension:Schema.string().default(".txt").description("Tag \u6587\u4EF6\u6269\u5C55\u540D"),shuffle_caption:Schema.boolean().default(!0).description("\u8BAD\u7EC3\u65F6\u968F\u673A\u6253\u4E71 tokens"),weighted_captions:Schema.boolean().description("\u4F7F\u7528\u5E26\u6743\u91CD\u7684 token\uFF0C\u4E0D\u63A8\u8350\u4E0E shuffle_caption \u4E00\u540C\u5F00\u542F"),keep_tokens:Schema.number().min(0).max(255).step(1).default(0).description("\u5728\u968F\u673A\u6253\u4E71 tokens \u65F6\uFF0C\u4FDD\u7559\u524D N \u4E2A\u4E0D\u53D8"),keep_tokens_separator:Schema.string().description("\u4FDD\u7559 tokens \u65F6\u4F7F\u7528\u7684\u5206\u9694\u7B26"),max_token_length:Schema.number().default(255).description("\u6700\u5927 token \u957F\u5EA6"),caption_dropout_rate:Schema.number().min(0).step(.01).description("\u4E22\u5F03\u5168\u90E8\u6807\u7B7E\u7684\u6982\u7387\uFF0C\u5BF9\u4E00\u4E2A\u56FE\u7247\u6982\u7387\u4E0D\u4F7F\u7528 caption \u6216 class token"),caption_dropout_every_n_epochs:Schema.number().min(0).max(100).step(1).description("\u6BCF N \u4E2A epoch \u4E22\u5F03\u5168\u90E8\u6807\u7B7E"),caption_tag_dropout_rate:Schema.number().min(0).step(.01).description("\u6309\u9017\u53F7\u5206\u9694\u7684\u6807\u7B7E\u6765\u968F\u673A\u4E22\u5F03 tag \u7684\u6982\u7387")}).description("caption\uFF08Tag\uFF09\u9009\u9879"),Schema.object({noise_offset:Schema.number().step(.01).description("\u5728\u8BAD\u7EC3\u4E2D\u6DFB\u52A0\u566A\u58F0\u504F\u79FB\u6765\u6539\u826F\u751F\u6210\u975E\u5E38\u6697\u6216\u8005\u975E\u5E38\u4EAE\u7684\u56FE\u50CF\uFF0C\u5982\u679C\u542F\u7528\u63A8\u8350\u4E3A 0.1"),multires_noise_iterations:Schema.number().step(1).description("\u591A\u5206\u8FA8\u7387\uFF08\u91D1\u5B57\u5854\uFF09\u566A\u58F0\u8FED\u4EE3\u6B21\u6570 \u63A8\u8350 6-10\u3002\u65E0\u6CD5\u4E0E noise_offset \u4E00\u540C\u542F\u7528"),multires_noise_discount:Schema.number().step(.01).description("\u591A\u5206\u8FA8\u7387\uFF08\u91D1\u5B57\u5854\uFF09\u8870\u51CF\u7387 \u63A8\u8350 0.3-0.8\uFF0C\u987B\u540C\u65F6\u4E0E\u4E0A\u65B9\u53C2\u6570 multires_noise_iterations \u4E00\u540C\u542F\u7528")}).description("\u566A\u58F0\u8BBE\u7F6E"),Schema.object({color_aug:Schema.boolean().description("\u989C\u8272\u6539\u53D8"),flip_aug:Schema.boolean().description("\u56FE\u50CF\u7FFB\u8F6C"),random_crop:Schema.boolean().description("\u968F\u673A\u526A\u88C1")}).description("\u6570\u636E\u589E\u5F3A"),Schema.object({seed:Schema.number().default(1337).description("\u968F\u673A\u79CD\u5B50"),clip_skip:Schema.number().role("slider").min(0).max(12).step(1).default(2).description("CLIP \u8DF3\u8FC7\u5C42\u6570 *\u7384\u5B66*"),no_metadata:Schema.boolean().description("\u4E0D\u4FDD\u5B58\u6A21\u578B\u5143\u6570\u636E"),ui_custom_params:Schema.string().role("textarea").description("**\u5371\u9669** \u81EA\u5B9A\u4E49\u53C2\u6570\uFF0C\u8BF7\u8F93\u5165 TOML \u683C\u5F0F\uFF0C\u5C06\u4F1A\u76F4\u63A5\u8986\u76D6\u5F53\u524D\u754C\u9762\u5185\u4EFB\u4F55\u53C2\u6570\u3002\u5B9E\u65F6\u66F4\u65B0\uFF0C\u63A8\u8350\u5199\u5B8C\u540E\u518D\u7C98\u8D34\u8FC7\u6765")}).description("\u9AD8\u7EA7\u8BBE\u7F6E"),Schema.object({mixed_precision:Schema.union(["no","fp16","bf16"]).default("fp16").description("\u8BAD\u7EC3\u6DF7\u5408\u7CBE\u5EA6"),full_fp16:Schema.boolean().description("\u5B8C\u5168\u4F7F\u7528 FP16 \u7CBE\u5EA6"),full_bf16:Schema.boolean().description("\u5B8C\u5168\u4F7F\u7528 BF16 \u7CBE\u5EA6"),fp8_base:Schema.boolean().description("\u5BF9\u57FA\u7840\u6A21\u578B\u4F7F\u7528 FP8 \u7CBE\u5EA6"),no_half_vae:Schema.boolean().description("\u4E0D\u4F7F\u7528\u534A\u7CBE\u5EA6 VAE"),xformers:Schema.boolean().default(!0).description("\u542F\u7528 xformers"),lowram:Schema.boolean().default(!1).description("\u4F4E\u5185\u5B58\u6A21\u5F0F \u8BE5\u6A21\u5F0F\u4E0B\u4F1A\u5C06 U-net\u3001\u6587\u672C\u7F16\u7801\u5668\u3001VAE \u76F4\u63A5\u52A0\u8F7D\u5230\u663E\u5B58\u4E2D"),cache_latents:Schema.boolean().default(!0).description("\u7F13\u5B58\u56FE\u50CF latent"),cache_latents_to_disk:Schema.boolean().default(!0).description("\u7F13\u5B58\u56FE\u50CF latent \u5230\u78C1\u76D8"),cache_text_encoder_outputs:Schema.boolean().description("\u7F13\u5B58\u6587\u672C\u7F16\u7801\u5668\u7684\u8F93\u51FA\uFF0C\u51CF\u5C11\u663E\u5B58\u4F7F\u7528\u3002\u4F7F\u7528\u65F6\u9700\u8981\u5173\u95ED shuffle_caption"),cache_text_encoder_outputs_to_disk:Schema.boolean().description("\u7F13\u5B58\u6587\u672C\u7F16\u7801\u5668\u7684\u8F93\u51FA\u5230\u78C1\u76D8"),persistent_data_loader_workers:Schema.boolean().default(!0).description("\u4FDD\u7559\u52A0\u8F7D\u8BAD\u7EC3\u96C6\u7684worker\uFF0C\u51CF\u5C11\u6BCF\u4E2A epoch \u4E4B\u95F4\u7684\u505C\u987F\u3002")}).description("\u901F\u5EA6\u4F18\u5316\u9009\u9879"),Schema.object({ddp_timeout:Schema.number().min(0).description("\u5206\u5E03\u5F0F\u8BAD\u7EC3\u8D85\u65F6\u65F6\u95F4"),ddp_gradient_as_bucket_view:Schema.boolean()}).description("\u5206\u5E03\u5F0F\u8BAD\u7EC3")]),createSchemaDreamboothMaster=()=>Schema.intersect([Schema.intersect([Schema.object({model_train_type:Schema.union(["sd-dreambooth","sdxl-finetune"]).default("sd-dreambooth").description("\u6A21\u578B\u79CD\u7C7B"),pretrained_model_name_or_path:Schema.string().role("textarea").default("./sd-models/model.safetensors").description("\u5E95\u6A21\u6587\u4EF6\u8DEF\u5F84"),vae:Schema.string().role("textarea").description("(\u53EF\u9009) VAE \u6A21\u578B\u6587\u4EF6\u8DEF\u5F84\uFF0C\u4F7F\u7528\u5916\u7F6E VAE \u6587\u4EF6\u8986\u76D6\u6A21\u578B\u5185\u672C\u8EAB\u7684"),v2:Schema.boolean().default(!1).description("\u5E95\u6A21\u4E3A sd2.0 \u4EE5\u540E\u7684\u7248\u672C\u9700\u8981\u542F\u7528")}).description("\u8BAD\u7EC3\u7528\u6A21\u578B"),Schema.union([Schema.object({v2:Schema.const(!0).required(),v_parameterization:Schema.boolean().default(!1).description("v-parameterization \u5B66\u4E60"),scale_v_pred_loss_like_noise_pred:Schema.boolean().default(!1).description("\u7F29\u653E v-prediction \u635F\u5931\uFF08\u4E0Ev-parameterization\u914D\u5408\u4F7F\u7528\uFF09")}),Schema.object({})])]),Schema.object({train_data_dir:Schema.string().role("textarea").default("./train/aki").description("\u8BAD\u7EC3\u6570\u636E\u96C6\u8DEF\u5F84"),reg_data_dir:Schema.string().role("textarea").description("\u6B63\u5219\u5316\u6570\u636E\u96C6\u8DEF\u5F84\u3002\u9ED8\u8BA4\u7559\u7A7A\uFF0C\u4E0D\u4F7F\u7528\u6B63\u5219\u5316\u56FE\u50CF"),prior_loss_weight:Schema.number().step(.1).description("\u6B63\u5219\u5316 - \u5148\u9A8C\u635F\u5931\u6743\u91CD"),resolution:Schema.string().default("512,512").description("\u8BAD\u7EC3\u56FE\u7247\u5206\u8FA8\u7387\uFF0C\u5BBDx\u9AD8\u3002\u652F\u6301\u975E\u6B63\u65B9\u5F62\uFF0C\u4F46\u5FC5\u987B\u662F 64 \u500D\u6570\u3002"),enable_bucket:Schema.boolean().default(!0).description("\u542F\u7528 arb \u6876\u4EE5\u5141\u8BB8\u975E\u56FA\u5B9A\u5BBD\u9AD8\u6BD4\u7684\u56FE\u7247"),min_bucket_reso:Schema.number().default(256).description("arb \u6876\u6700\u5C0F\u5206\u8FA8\u7387"),max_bucket_reso:Schema.number().default(1024).description("arb \u6876\u6700\u5927\u5206\u8FA8\u7387"),bucket_reso_steps:Schema.number().default(64).description("arb \u6876\u5206\u8FA8\u7387\u5212\u5206\u5355\u4F4D\uFF0CSDXL \u53EF\u4EE5\u4F7F\u7528 32")}).description("\u6570\u636E\u96C6\u8BBE\u7F6E"),Schema.object({output_name:Schema.string().default("aki").description("\u6A21\u578B\u4FDD\u5B58\u540D\u79F0"),output_dir:Schema.string().default("./output").description("\u6A21\u578B\u4FDD\u5B58\u6587\u4EF6\u5939"),save_model_as:Schema.union(["safetensors","pt","ckpt"]).default("safetensors").description("\u6A21\u578B\u4FDD\u5B58\u683C\u5F0F"),save_precision:Schema.union(["fp16","float","bf16"]).default("fp16").description("\u6A21\u578B\u4FDD\u5B58\u7CBE\u5EA6"),save_every_n_epochs:Schema.number().default(2).description("\u6BCF N epoch\uFF08\u8F6E\uFF09\u81EA\u52A8\u4FDD\u5B58\u4E00\u6B21\u6A21\u578B")}).description("\u4FDD\u5B58\u8BBE\u7F6E"),Schema.object({max_train_epochs:Schema.number().min(1).default(10).description("\u6700\u5927\u8BAD\u7EC3 epoch\uFF08\u8F6E\u6570\uFF09"),train_batch_size:Schema.number().min(1).default(1).description("\u6279\u91CF\u5927\u5C0F"),stop_text_encoder_training:Schema.number().min(-1).description("\u4EC5 sd-dreambooth \u53EF\u7528\u3002\u5728\u7B2C N \u6B65\u65F6\uFF0C\u505C\u6B62\u8BAD\u7EC3\u6587\u672C\u7F16\u7801\u5668\u3002\u8BBE\u7F6E\u4E3A -1 \u4E0D\u8BAD\u7EC3\u6587\u672C\u7F16\u7801\u5668"),gradient_checkpointing:Schema.boolean().default(!1).description("\u68AF\u5EA6\u68C0\u67E5\u70B9"),gradient_accumulation_steps:Schema.number().min(1).description("\u68AF\u5EA6\u7D2F\u52A0\u6B65\u6570")}).description("\u8BAD\u7EC3\u76F8\u5173\u53C2\u6570"),Schema.intersect([Schema.object({learning_rate:Schema.string().default("1e-6").description("\u5B66\u4E60\u7387"),learning_rate_te:Schema.string().default("5e-7").description("\u6587\u672C\u7F16\u7801\u5668\u5B66\u4E60\u7387"),learning_rate_te1:Schema.string().default("5e-7").description("\u4EC5 SDXL \u53EF\u7528\u3002\u6587\u672C\u7F16\u7801\u5668 1 (ViT-L) \u5B66\u4E60\u7387"),learning_rate_te2:Schema.string().default("5e-7").description("\u4EC5 SDXL \u53EF\u7528\u3002\u6587\u672C\u7F16\u7801\u5668 2 (BiG-G) \u5B66\u4E60\u7387"),lr_scheduler:Schema.union(["linear","cosine","cosine_with_restarts","polynomial","constant","constant_with_warmup"]).default("cosine_with_restarts").description("\u5B66\u4E60\u7387\u8C03\u5EA6\u5668\u8BBE\u7F6E"),lr_warmup_steps:Schema.number().default(0).description("\u5B66\u4E60\u7387\u9884\u70ED\u6B65\u6570")}).description("\u5B66\u4E60\u7387\u4E0E\u4F18\u5316\u5668\u8BBE\u7F6E"),Schema.union([Schema.object({lr_scheduler:Schema.const("cosine_with_restarts"),lr_scheduler_num_cycles:Schema.number().default(1).description("\u91CD\u542F\u6B21\u6570")}),Schema.object({})]),Schema.object({optimizer_type:Schema.union(["AdamW","AdamW8bit","PagedAdamW8bit","Lion","Lion8bit","PagedLion8bit","SGDNesterov","SGDNesterov8bit","DAdaptation","DAdaptAdam","DAdaptAdaGrad","DAdaptAdanIP","DAdaptLion","DAdaptSGD","AdaFactor","Prodigy"]).default("AdamW8bit").description("\u4F18\u5316\u5668\u8BBE\u7F6E"),min_snr_gamma:Schema.number().step(.1).description("\u6700\u5C0F\u4FE1\u566A\u6BD4\u4F3D\u9A6C\u503C\uFF0C\u5982\u679C\u542F\u7528\u63A8\u8350\u4E3A 5")}),Schema.union([Schema.object({optimizer_type:Schema.const("Prodigy").required(),prodigy_d0:Schema.string(),prodigy_d_coef:Schema.string().default("2.0")}),Schema.object({})]),Schema.object({optimizer_args_custom:Schema.array(String).role("table").description("\u81EA\u5B9A\u4E49 optimizer_args\uFF0C\u4E00\u884C\u4E00\u4E2A")})]),Schema.intersect([Schema.object({enable_preview:Schema.boolean().default(!1).description("\u542F\u7528\u8BAD\u7EC3\u9884\u89C8\u56FE")}).description("\u8BAD\u7EC3\u9884\u89C8\u56FE\u8BBE\u7F6E"),Schema.union([Schema.object({enable_preview:Schema.const(!0).required(),sample_prompts:Schema.string().role("textarea").default("(masterpiece, best quality:1.2), 1girl, solo, --n lowres, bad anatomy, bad hands, text, error, missing fingers, extra digit, fewer digits, cropped, worst quality, low quality, normal quality, jpeg artifacts,signature, watermark, username, blurry,  --w 512  --h 768  --l 7  --s 24  --d 1337").description("\u9884\u89C8\u56FE\u751F\u6210\u53C2\u6570\u3002`--n` \u540E\u65B9\u4E3A\u53CD\u5411\u63D0\u793A\u8BCD\uFF0C<br>`--w`\u5BBD\uFF0C`--h`\u9AD8<br>`--l`: CFG Scale<br>`--s`: \u8FED\u4EE3\u6B65\u6570<br>`--d`: \u79CD\u5B50"),sample_sampler:Schema.union(["ddim","pndm","lms","euler","euler_a","heun","dpm_2","dpm_2_a","dpmsolver","dpmsolver++","dpmsingle","k_lms","k_euler","k_euler_a","k_dpm_2","k_dpm_2_a"]).default("euler_a").description("\u751F\u6210\u9884\u89C8\u56FE\u6240\u7528\u91C7\u6837\u5668"),sample_every_n_epochs:Schema.number().default(2).description("\u6BCF N \u4E2A epoch \u751F\u6210\u4E00\u6B21\u9884\u89C8\u56FE")}),Schema.object({})])]),Schema.intersect([Schema.object({log_with:Schema.union(["tensorboard","wandb"]).default("tensorboard").description("\u65E5\u5FD7\u6A21\u5757"),log_prefix:Schema.string().description("\u65E5\u5FD7\u524D\u7F00"),log_tracker_name:Schema.string().description("\u65E5\u5FD7\u8FFD\u8E2A\u5668\u540D\u79F0"),logging_dir:Schema.string().default("./logs").description("\u65E5\u5FD7\u4FDD\u5B58\u6587\u4EF6\u5939")}).description("\u65E5\u5FD7\u8BBE\u7F6E"),Schema.union([Schema.object({log_with:Schema.const("wandb").required(),wandb_api_key:Schema.string().required().description("wandb \u7684 api \u5BC6\u94A5")}),Schema.object({})])]),Schema.object({caption_extension:Schema.string().default(".txt").description("Tag \u6587\u4EF6\u6269\u5C55\u540D"),shuffle_caption:Schema.boolean().default(!0).description("\u8BAD\u7EC3\u65F6\u968F\u673A\u6253\u4E71 tokens"),weighted_captions:Schema.boolean().default(!1).description("\u4F7F\u7528\u5E26\u6743\u91CD\u7684 token\uFF0C\u4E0D\u63A8\u8350\u4E0E shuffle_caption \u4E00\u540C\u5F00\u542F"),keep_tokens:Schema.number().min(0).max(255).step(1).default(0).description("\u5728\u968F\u673A\u6253\u4E71 tokens \u65F6\uFF0C\u4FDD\u7559\u524D N \u4E2A\u4E0D\u53D8"),keep_tokens_separator:Schema.string().description("\u4FDD\u7559 tokens \u65F6\u4F7F\u7528\u7684\u5206\u9694\u7B26"),max_token_length:Schema.number().default(255).description("\u6700\u5927 token \u957F\u5EA6"),caption_dropout_rate:Schema.number().min(0).max(1).step(.1).description("\u4E22\u5F03\u5168\u90E8\u6807\u7B7E\u7684\u6982\u7387\uFF0C\u5BF9\u4E00\u4E2A\u56FE\u7247\u6982\u7387\u4E0D\u4F7F\u7528 caption \u6216 class token"),caption_dropout_every_n_epochs:Schema.number().min(0).max(100).step(1).description("\u6BCF N \u4E2A epoch \u4E22\u5F03\u5168\u90E8\u6807\u7B7E"),caption_tag_dropout_rate:Schema.number().min(0).max(1).step(.1).description("\u6309\u9017\u53F7\u5206\u9694\u7684\u6807\u7B7E\u6765\u968F\u673A\u4E22\u5F03 tag \u7684\u6982\u7387")}).description("caption\uFF08Tag\uFF09\u9009\u9879"),Schema.object({noise_offset:Schema.number().step(.01).description("\u5728\u8BAD\u7EC3\u4E2D\u6DFB\u52A0\u566A\u58F0\u504F\u79FB\u6765\u6539\u826F\u751F\u6210\u975E\u5E38\u6697\u6216\u8005\u975E\u5E38\u4EAE\u7684\u56FE\u50CF\uFF0C\u5982\u679C\u542F\u7528\u63A8\u8350\u4E3A 0.1"),multires_noise_iterations:Schema.number().step(1).description("\u591A\u5206\u8FA8\u7387\uFF08\u91D1\u5B57\u5854\uFF09\u566A\u58F0\u8FED\u4EE3\u6B21\u6570 \u63A8\u8350 6-10\u3002\u65E0\u6CD5\u4E0E noise_offset \u4E00\u540C\u542F\u7528"),multires_noise_discount:Schema.number().step(.1).description("\u591A\u5206\u8FA8\u7387\uFF08\u91D1\u5B57\u5854\uFF09\u8870\u51CF\u7387 \u63A8\u8350 0.3-0.8\uFF0C\u987B\u540C\u65F6\u4E0E\u4E0A\u65B9\u53C2\u6570 multires_noise_iterations \u4E00\u540C\u542F\u7528")}).description("\u566A\u58F0\u8BBE\u7F6E"),Schema.object({seed:Schema.number().default(1337).description("\u968F\u673A\u79CD\u5B50"),clip_skip:Schema.number().role("slider").min(0).max(12).step(1).default(2).description("CLIP \u8DF3\u8FC7\u5C42\u6570 *\u7384\u5B66*"),no_token_padding:Schema.boolean().default(!1).description("\u7981\u7528 token \u586B\u5145\uFF08\u4E0E Diffusers \u7684\u65E7 Dreambooth \u811A\u672C\u4E00\u81F4\uFF09")}).description("\u9AD8\u7EA7\u8BBE\u7F6E"),Schema.object({mixed_precision:Schema.union(["no","fp16","bf16"]).default("fp16").description("\u8BAD\u7EC3\u6DF7\u5408\u7CBE\u5EA6"),full_fp16:Schema.boolean().description("\u5B8C\u5168\u4F7F\u7528 FP16 \u7CBE\u5EA6"),full_bf16:Schema.boolean().description("\u5B8C\u5168\u4F7F\u7528 BF16 \u7CBE\u5EA6 \u4EC5\u652F\u6301 SDXL"),xformers:Schema.boolean().default(!0).description("\u542F\u7528 xformers"),lowram:Schema.boolean().default(!1).description("\u4F4E\u5185\u5B58\u6A21\u5F0F \u8BE5\u6A21\u5F0F\u4E0B\u4F1A\u5C06 U-net\u3001\u6587\u672C\u7F16\u7801\u5668\u3001VAE \u76F4\u63A5\u52A0\u8F7D\u5230\u663E\u5B58\u4E2D"),cache_latents:Schema.boolean().default(!0).description("\u7F13\u5B58\u56FE\u50CF latent"),cache_latents_to_disk:Schema.boolean().default(!0).description("\u7F13\u5B58\u56FE\u50CF latent \u5230\u78C1\u76D8"),persistent_data_loader_workers:Schema.boolean().default(!0).description("\u4FDD\u7559\u52A0\u8F7D\u8BAD\u7EC3\u96C6\u7684worker\uFF0C\u51CF\u5C11\u6BCF\u4E2A epoch \u4E4B\u95F4\u7684\u505C\u987F\u3002")}).description("\u901F\u5EA6\u4F18\u5316\u9009\u9879"),Schema.object({ddp_timeout:Schema.number().min(0).description("\u5206\u5E03\u5F0F\u8BAD\u7EC3\u8D85\u65F6\u65F6\u95F4"),ddp_gradient_as_bucket_view:Schema.boolean()}).description("\u5206\u5E03\u5F0F\u8BAD\u7EC3")]),delay=e=>new Promise(t=>setTimeout(t,e)),getGraphicCards=async()=>{let e=0;const t=3;try{for(;e<t;){let n=await(await get("/api/graphic_cards")).json();if(n.status==="success")return n.data.cards;if(n.status==="pending")await delay(1e3),e++;else return ElMessage.error("\u83B7\u53D6\u663E\u5361\u4FE1\u606F\u5931\u8D25\uFF0C\u5C06\u4F7F\u7528\u9ED8\u8BA4\u663E\u5361\uFF1A"+n.message),[]}ElMessage.error("\u83B7\u53D6\u663E\u5361\u4FE1\u606F\u5931\u8D25\uFF0C\u5C06\u4F7F\u7528\u9ED8\u8BA4\u663E\u5361\uFF1A\u8BF7\u6C42\u8D85\u65F6")}catch(r){ElMessage.error("\u83B7\u53D6\u663E\u5361\u4FE1\u606F\u5931\u8D25\uFF0C\u5C06\u4F7F\u7528\u9ED8\u8BA4\u663E\u5361\uFF1A"+r.message),console.log(r)}return[]};let cards={initial:!1,data:[]};const applyToSchema=async e=>(cards.data=await getGraphicCards(),cards.data.length<2||e.push(Schema.object({gpu_ids:Schema.array(Schema.union(cards.data)).description("\u9009\u62E9\u663E\u5361").role("select")}).description("\u663E\u5361\u8BBE\u7F6E")),e);class SchemaManager{getSchema(t){switch(t){case"lora-basic":return createSchemaLoraBasic();case"lora-master":return createSchemaLoraMaster();case"dreambooth-master":return createSchemaDreamboothMaster();default:return Schema.object({})}}async applySchema(t){let r=this.getSchema(t);return await applyToSchema(r)}}const data$1={history_title:"\u5386\u53F2\u53C2\u6570",export:"\u5BFC\u51FA",import:"\u5BFC\u5165",date:"\u65E5\u671F",name:"\u540D\u79F0",apply:"\u4F7F\u7528",preview:"\u9884\u89C8",set_name:"\u8BBE\u7F6E\u540D\u79F0",delete:"\u5220\u9664",param_warnings:"\u53C2\u6570\u63D0\u793A",param_errors:"\u53C2\u6570\u9519\u8BEF",reset_all:"\u5168\u90E8\u91CD\u7F6E",save_params:"\u4FDD\u5B58\u53C2\u6570",read_params:"\u8BFB\u53D6\u53C2\u6570",download_config:"\u4E0B\u8F7D\u914D\u7F6E\u6587\u4EF6",import_config:"\u5BFC\u5165\u914D\u7F6E\u6587\u4EF6",start_train:"\u5F00\u59CB\u8BAD\u7EC3",stop_train:"\u7EC8\u6B62\u8BAD\u7EC3",output_header:"\u53C2\u6570\u9884\u89C8",wd_14_tagger:"WD 1.4 \u6807\u7B7E\u5668"},data={history_title:"History Parameters",export:"Export",import:"Import",date:"Date",name:"Name",apply:"Apply",preview:"Preview",set_name:"Set Name",delete:"Delete",param_warnings:"Parameter Warnings",param_errors:"Parameter Errors",reset_all:"Reset All",save_params:"Save Parameters",read_params:"Read Parameters",download_config:"Download Config File",import_config:"Import Config File",start_train:"Start Training",stop_train:"Stop Training",output_header:"Output",wd_14_tagger:"WD 1.4 Tagger"};var main_vue_vue_type_style_index_0_lang="";const _hoisted_1$5={class:"example-container"},_hoisted_2$5={class:"history-header"},_hoisted_3$4={class:"header-left"},_hoisted_4$4=["id"],_hoisted_5$4={class:"header-right"},_hoisted_6$4={class:"schema-container"},_hoisted_7$1={class:"right-container"},_hoisted_8$1={class:"theme-default-content"},_hoisted_9$1={class:"params-section"},_hoisted_10$1=createBaseVNode("section",{id:"test-output"},[createBaseVNode("header",null,"Output")],-1),_sfc_main$6=defineComponent({__name:"main",setup(e){let t=null;const r=usePageFrontmatter(),n=new SchemaManager;let s=ref(n.getSchema(r.value.trainType));const a=ref(null),u=ref({}),c=ref(null),l=ref(!1),o=ref([]),d=ref([]),_=["network_args_custom","optimizer_args_custom"],{t:i,setLocaleMessage:F}=useI18n({messages:{"zh-CN":data$1,"en-US":data}});onBeforeMount(async()=>{t=r.value.trainType,s.value=await n.applySchema(t)}),onMounted(async()=>{B(),S()}),onBeforeUnmount(()=>{localStorage.setItem(`configs-${t}-autosave`,JSON.stringify(clone(u.value)))});const S=()=>{try{let p=localStorage.getItem(`configs-${t}-autosave`);if(p!="null"){let g=JSON.parse(p);g!=null&&g!=null&&Object.keys(g).length>0&&(u.value=g,ElMessage.success("\u5DF2\u81EA\u52A8\u52A0\u8F7D\u5386\u53F2\u53C2\u6570"))}}catch(p){console.log(p)}},C=()=>{let p=u.value;_.forEach(m=>{p&&p.hasOwnProperty(m)&&p[m]!=null&&(p[m]=p[m].map(v=>v||""))});let g=s.value(p);return _.forEach(m=>{g.hasOwnProperty(m)&&g[m].length==0&&delete g[m]}),g};computed(()=>{try{return C()}catch(p){console.log(p)}});const E=computed(()=>{try{return L()}catch(p){console.log(p)}}),B=()=>{const p=localStorage.getItem(`configs-${t}`);p?c.value=JSON.parse(p).map(g=>(!g.name&&g.value&&g.value.output_name&&(g.name=g.value.output_name),g)):c.value=[]},A=()=>{localStorage.setItem(`configs-${r.value.trainType}`,JSON.stringify(c.value))},x=()=>{let p={time:new Date().toLocaleString(),value:clone(u.value)};u.value&&u.value.output_name&&(p.name=u.value.output_name),c.value.push(p),A(),ElMessage.success("\u5DF2\u5C06\u4FEE\u6539\u4FDD\u5B58\u81F3\u6D4F\u89C8\u5668")},R=()=>{l.value=!0},L=()=>{let p=C(),g=parseParams(p,t),m=checkParams(g);return o.value=m.warnings,d.value=m.errors,stringify(g)},$=async()=>{const p=parseParams(s.value(u.value),t);p.optimizer_type=="DAdaptation"&&ElMessage.warning({message:"DAdaptation \u8BAD\u7EC3\u65F6\uFF0C\u6240\u6709\u5B66\u4E60\u7387\u5C06\u88AB\u8BBE\u7F6E\u4E3A 1\u3002\u5E76\u4E14\u5B66\u4E60\u7387\u8C03\u5EA6\u5668\u5C06\u88AB\u8BBE\u7F6E\u4E3A constant\u3002",duration:5e3});try{let g=await post("/api/run",JSON.stringify(p),{"Content-Type":"application/json"});if(!g.ok)throw new Error("Network response was not ok");let m=await g.json();m.status=="success"?ElMessage.success("\u8BAD\u7EC3\u4EFB\u52A1\u5DF2\u63D0\u4EA4"+m.message):ElMessage.error("\u8BAD\u7EC3\u4EFB\u52A1\u63D0\u4EA4\u5931\u8D25\uFF1A"+m.message)}catch(g){ElMessage.error("\u65E0\u6CD5\u8FDE\u63A5\u5230\u8BAD\u7EC3\u7AEF\uFF0C\u8BF7\u68C0\u67E5\u662F\u5426\u5F00\u542F\u8BAD\u7EC3\u7AEF\u3002"),console.error("There was a problem with the fetch operation:",g)}},V=async()=>{let p=null;try{let I=(await(await fetch("/api/tasks")).json()).data.tasks.filter(w=>w.status=="RUNNING");if(I.length==0){ElMessage.warning("\u5F53\u524D\u6CA1\u6709\u6B63\u5728\u8FD0\u884C\u7684\u8BAD\u7EC3\u4EFB\u52A1");return}p=I[0]}catch(m){ElMessage.error("\u83B7\u53D6\u8FD0\u884C\u4E2D\u4EFB\u52A1\u5931\u8D25"),console.error("There was a problem with the fetch operation:",m);return}const g=await ElMessageBox.confirm(`\u786E\u5B9A\u8981\u505C\u6B62\u4EFB\u52A1 ${p.id} \u5417\uFF1F`,"\u63D0\u793A",{confirmButtonText:"\u786E\u5B9A",cancelButtonText:"\u53D6\u6D88",type:"warning"});try{if(g==="confirm"){let m=await get(`/api/tasks/terminate/${p.id}`);if(!m.ok)throw new Error("Network response was not ok");let v=await m.json();v.status=="success"?ElMessage.success("\u505C\u6B62\u4EFB\u52A1\u6210\u529F"):ElMessage.error("\u505C\u6B62\u4EFB\u52A1\u5931\u8D25\uFF1A"+v.messsage)}}catch(m){ElMessage.error("\u65E0\u6CD5\u8FDE\u63A5\u5230\u8BAD\u7EC3\u7AEF\uFF0C\u8BF7\u68C0\u67E5\u662F\u5426\u5F00\u542F\u8BAD\u7EC3\u7AEF\u3002"),console.error("There was a problem with the fetch operation:",m)}},z=()=>{u.value=null},j=(p,g)=>{const m=new Blob([g],{type:"text/plain;charset=utf-8"}),v=URL.createObjectURL(m),N=document.createElement("a");N.href=v,N.download=p,N.click(),URL.revokeObjectURL(v)},D=()=>{const p=L(),m=`${new Date().getTime()}.toml`;j(m,p)},O=()=>{const p=document.createElement("input");p.type="file",p.accept=".toml",p.onchange=g=>{const m=g.target.files[0],v=new FileReader;v.onload=N=>{const I=N.target.result;try{let w=parseParamsRe(TomlParse(I));console.log(w);let P=s.value();u.value==null&&(u.value={});for(let H in w)P.hasOwnProperty(H)&&P[H]!=null&&P[H]!=w[H]&&(u.value[H]=w[H]);ElMessage.success("\u5BFC\u5165\u6210\u529F")}catch(w){console.log(w),ElMessage.error("\u5BFC\u5165\u5931\u8D25")}},v.readAsText(m)},p.click()},K=(p,g)=>{u.value=clone(g.value),l.value=!1,ElMessage.success("\u5DF2\u5C06\u5386\u53F2\u53C2\u6570\u5E94\u7528\u81F3\u5F53\u524D\u53C2\u6570")},M=(p,g)=>{const m=stringify(parseParams(s.value(clone(g.value)),t));ElMessageBox.alert(m,"\u9884\u89C8",{confirmButtonText:"\u786E\u5B9A",customStyle:{whiteSpace:"pre-line"}})},W=(p,g)=>{ElMessageBox.confirm("\u6B64\u64CD\u4F5C\u5C06\u6C38\u4E45\u5220\u9664\u8BE5\u53C2\u6570\u914D\u7F6E, \u662F\u5426\u7EE7\u7EED?","\u63D0\u793A",{confirmButtonText:"\u786E\u5B9A",cancelButtonText:"\u53D6\u6D88",type:"warning"}).then(()=>{c.value.splice(p,1),A(),ElMessage.success("\u5220\u9664\u6210\u529F")})},b=(p,g)=>{ElMessageBox.prompt("\u8BF7\u8F93\u5165\u540D\u79F0","\u91CD\u547D\u540D",{confirmButtonText:"\u786E\u8BA4",cancelButtonText:"\u53D6\u6D88"}).then(({value:m})=>{g.name=m,A(),ElMessage.success("\u91CD\u547D\u540D\u6210\u529F")}).catch(()=>{ElMessage.info("\u53D6\u6D88\u91CD\u547D\u540D")})},y=()=>{const g=`${new Date().getTime()}.json`;j(g,JSON.stringify(c.value))},T=()=>{const p=document.createElement("input");p.type="file",p.accept=".json",p.onchange=g=>{const m=g.target.files[0],v=new FileReader;v.onload=N=>{const I=N.target.result;try{const w=JSON.parse(I);w instanceof Array?(c.value=[...c.value,...w],A(),ElMessage.success("\u5BFC\u5165\u6210\u529F")):ElMessage.error("\u5BFC\u5165\u5931\u8D25\uFF1A\u6587\u4EF6\u683C\u5F0F\u9519\u8BEF")}catch{ElMessage.error("\u5BFC\u5165\u5931\u8D25\uFF1A\u6587\u4EF6\u683C\u5F0F\u9519\u8BEF")}},v.readAsText(m)},p.click()};return(p,g)=>{const m=resolveComponent("el-button"),v=resolveComponent("el-table-column"),N=resolveComponent("el-table"),I=resolveComponent("el-dialog"),w=resolveComponent("k-schema"),P=resolveComponent("el-scrollbar"),H=resolveComponent("content"),U=resolveComponent("el-col"),J=resolveComponent("el-row");return openBlock(),createElementBlock("div",_hoisted_1$5,[createVNode(unref(ClientOnly),null,{default:withCtx(()=>[createVNode(I,{modelValue:l.value,"onUpdate:modelValue":g[0]||(g[0]=k=>l.value=k),"append-to-body":!0},{header:withCtx(({close:k,titleId:q,titleClass:G})=>[createBaseVNode("div",_hoisted_2$5,[createBaseVNode("div",_hoisted_3$4,[createBaseVNode("h5",{id:q,class:normalizeClass(G),style:{margin:"0"}},toDisplayString(unref(i)("history_title")),11,_hoisted_4$4)]),createBaseVNode("div",_hoisted_5$4,[createVNode(m,{size:"small",onClick:y},{default:withCtx(()=>[createTextVNode(toDisplayString(unref(i)("export")),1)]),_:1}),createVNode(m,{size:"small",onClick:T},{default:withCtx(()=>[createTextVNode(toDisplayString(unref(i)("import")),1)]),_:1})])])]),default:withCtx(()=>[createVNode(N,{data:c.value,style:{width:"100%"}},{default:withCtx(()=>[createVNode(v,{label:unref(i)("date"),property:"time",width:"180"},null,8,["label"]),createVNode(v,{label:unref(i)("name"),property:"name",width:"180"},null,8,["label"]),createVNode(v,{align:"right"},{default:withCtx(k=>[createVNode(m,{size:"small",onClick:q=>K(k.$index,k.row)},{default:withCtx(()=>[createTextVNode(toDisplayString(unref(i)("apply")),1)]),_:2},1032,["onClick"]),createVNode(m,{size:"small",onClick:q=>M(k.$index,k.row)},{default:withCtx(()=>[createTextVNode(toDisplayString(unref(i)("preview")),1)]),_:2},1032,["onClick"]),createVNode(m,{size:"small",onClick:q=>b(k.$index,k.row)},{default:withCtx(()=>[createTextVNode(toDisplayString(unref(i)("set_name")),1)]),_:2},1032,["onClick"]),createVNode(m,{size:"small",type:"danger",onClick:q=>W(k.$index,k.row)},{default:withCtx(()=>[createTextVNode(toDisplayString(unref(i)("delete")),1)]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),_:1},8,["modelValue"])]),_:1}),createBaseVNode("section",_hoisted_6$4,[createVNode(P,null,{default:withCtx(()=>[createBaseVNode("form",null,[createVNode(w,{modelValue:u.value,"onUpdate:modelValue":g[1]||(g[1]=k=>u.value=k),initial:a.value,schema:unref(s)},null,8,["modelValue","initial","schema"])])]),_:1})]),createBaseVNode("div",_hoisted_7$1,[createBaseVNode("section",_hoisted_8$1,[createVNode(P,null,{default:withCtx(()=>[createBaseVNode("main",null,[createVNode(H)])]),_:1})]),createBaseVNode("section",null,[createBaseVNode("header",null,toDisplayString(unref(i)("output_header")),1),createBaseVNode("main",_hoisted_9$1,[createBaseVNode("code",null,[createVNode(P,{"max-height":"60vh"},{default:withCtx(()=>[o.value.length!=0?(openBlock(),createBlock(unref(ElAlert),{key:0,title:unref(i)("param_warnings"),type:"warning",description:o.value.join(`
`),"show-icon":""},null,8,["title","description"])):createCommentVNode("",!0),d.value.length!=0?(openBlock(),createBlock(unref(ElAlert),{key:1,title:unref(i)("param_errors"),type:"error",description:d.value.join(`
`),"show-icon":""},null,8,["title","description"])):createCommentVNode("",!0),createTextVNode(toDisplayString(E.value),1)]),_:1})])])]),createVNode(J,{gutter:10,style:{margin:"0px 20px 10px 20px"}},{default:withCtx(()=>[createVNode(U,{span:8,style:{"padding-left":"0"}},{default:withCtx(()=>[createVNode(m,{class:"max-btn",onClick:z},{default:withCtx(()=>[createTextVNode(toDisplayString(unref(i)("reset_all")),1)]),_:1})]),_:1}),createVNode(U,{span:8},{default:withCtx(()=>[createVNode(m,{class:"max-btn",onClick:x},{default:withCtx(()=>[createTextVNode(toDisplayString(unref(i)("save_params")),1)]),_:1})]),_:1}),createVNode(U,{span:8},{default:withCtx(()=>[createVNode(m,{class:"max-btn",onClick:R},{default:withCtx(()=>[createTextVNode(toDisplayString(unref(i)("read_params")),1)]),_:1})]),_:1})]),_:1}),createVNode(J,{gutter:10,style:{margin:"0px 20px 10px 20px"}},{default:withCtx(()=>[createVNode(U,{span:12,style:{"padding-left":"0"}},{default:withCtx(()=>[createVNode(m,{class:"max-btn",onClick:D},{default:withCtx(()=>[createTextVNode(toDisplayString(unref(i)("download_config")),1)]),_:1})]),_:1}),createVNode(U,{span:12},{default:withCtx(()=>[createVNode(m,{class:"max-btn",onClick:O},{default:withCtx(()=>[createTextVNode(toDisplayString(unref(i)("import_config")),1)]),_:1})]),_:1})]),_:1}),createVNode(J,{gutter:10,style:{margin:"0px 20px 10px 20px"}},{default:withCtx(()=>[createVNode(U,{span:12,style:{"padding-left":"0"}},{default:withCtx(()=>[createVNode(m,{plain:"",class:"max-btn color-btn",type:"primary",onClick:$},{default:withCtx(()=>[createTextVNode(toDisplayString(unref(i)("start_train")),1)]),_:1})]),_:1}),createVNode(U,{span:12},{default:withCtx(()=>[createVNode(m,{plain:"",class:"max-btn color-btn",type:"warning",onClick:V},{default:withCtx(()=>[createTextVNode(toDisplayString(unref(i)("stop_train")),1)]),_:1})]),_:1})]),_:1}),_hoisted_10$1])])}}});var MainPage=_export_sfc$1(_sfc_main$6,[["__file","main.vue"]]),iframe_vue_vue_type_style_index_0_lang="";const _hoisted_1$4={class:"iframe-container"},_hoisted_2$4=["src"],_sfc_main$5=defineComponent({__name:"iframe",props:{url:{type:String,default:"http://127.0.0.1:6006"},type:String},setup(e){const t=e,r=ref("");if(t.type=="tensorboard"){let n=localStorage.getItem("ui-configs");if(n){let s=JSON.parse(n);s.tensorboard_url?r.value=s.tensorboard_url:r.value=`${window.location.protocol}//${window.location.host}/proxy/tensorboard/`}else r.value=`${window.location.protocol}//${window.location.host}/proxy/tensorboard/`}else t.type=="tageditor"?r.value=`${window.location.protocol}//${window.location.host}/proxy/tageditor/`:r.value=t.url;return(n,s)=>(openBlock(),createElementBlock("div",_hoisted_1$4,[createBaseVNode("iframe",{class:"iframe-main",src:r.value,width:"100%"},null,8,_hoisted_2$4)]))}});var IframePage=_export_sfc$1(_sfc_main$5,[["__file","iframe.vue"]]),tagger_vue_vue_type_style_index_0_scoped_true_lang="";const _withScopeId$2=e=>(pushScopeId("data-v-275870d6"),e=e(),popScopeId(),e),_hoisted_1$3={class:"example-container"},_hoisted_2$3={class:"schema-container"},_hoisted_3$3={class:"right-container"},_hoisted_4$3={class:"theme-default-content"},_hoisted_5$3={id:"test-output"},_hoisted_6$3=_withScopeId$2(()=>createBaseVNode("header",null,"Output",-1)),_sfc_main$4=defineComponent({__name:"tagger",setup(__props){const frontmatter=usePageFrontmatter(),schema=computed(()=>eval(frontmatter.value.code)),initial=ref(null),config=ref(null),output=computed(()=>{try{return schema.value(config.value)}catch(e){console.log(e)}}),runTrain=()=>{const e=schema.value(config.value);e.path=e.path.replaceAll("\\","/"),fetch("/api/interrogate",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)}).then(t=>{if(!t.ok)throw new Error("Network response was not ok");return t.json()}).then(t=>{console.log(t),t.status=="success"?ElMessage.success("Tagger \u4EFB\u52A1\u5DF2\u63D0\u4EA4"):ElMessage.error("Tagger \u4EFB\u52A1\u63D0\u4EA4\u5931\u8D25")}).catch(t=>{ElMessage.error("\u65E0\u6CD5\u8FDE\u63A5\u5230\u540E\u7AEF"),console.error("There was a problem with the fetch operation:",t)})},reset=()=>{config.value=null};return(e,t)=>{const r=resolveComponent("k-schema"),n=resolveComponent("el-scrollbar"),s=resolveComponent("content"),a=resolveComponent("el-button"),u=resolveComponent("json");return openBlock(),createElementBlock("div",_hoisted_1$3,[createBaseVNode("section",_hoisted_2$3,[createVNode(n,null,{default:withCtx(()=>[createBaseVNode("form",null,[createVNode(r,{modelValue:config.value,"onUpdate:modelValue":t[0]||(t[0]=c=>config.value=c),initial:initial.value,schema:schema.value},null,8,["modelValue","initial","schema"])])]),_:1})]),createBaseVNode("div",_hoisted_3$3,[createBaseVNode("section",_hoisted_4$3,[createVNode(n,null,{default:withCtx(()=>[createBaseVNode("main",null,[createVNode(s)])]),_:1})]),createVNode(a,{style:{margin:"10px 20px 0 20px"},onClick:runTrain},{default:withCtx(()=>[createTextVNode("\u542F\u52A8")]),_:1}),createVNode(a,{style:{margin:"10px 20px 10px 20px"},onClick:reset},{default:withCtx(()=>[createTextVNode("\u5168\u90E8\u91CD\u7F6E")]),_:1}),createBaseVNode("section",_hoisted_5$3,[_hoisted_6$3,createBaseVNode("main",null,[createBaseVNode("code",null,[createVNode(u,{data:output.value},null,8,["data"])])])])])])}}});var TaggerPage=_export_sfc$1(_sfc_main$4,[["__scopeId","data-v-275870d6"],["__file","tagger.vue"]]);const _hoisted_1$2={key:0,style:{color:"var(--shiki-token-constant)"}},_hoisted_2$2={key:1,style:{color:"var(--shiki-token-string)"}},_hoisted_3$2={key:2,style:{color:"var(--shiki-token-constant)"}},_hoisted_4$2={key:3,style:{color:"var(--shiki-token-constant)"}},_hoisted_5$2=createBaseVNode("span",{style:{color:"var(--shiki-token-function)"}},"Date",-1),_hoisted_6$2=createBaseVNode("span",{style:{color:"var(--shiki-token-punctuation)"}},"(",-1),_hoisted_7={style:{color:"var(--shiki-token-string)"}},_hoisted_8=createBaseVNode("span",{style:{color:"var(--shiki-token-punctuation)"}},")",-1),_hoisted_9=createBaseVNode("span",{style:{color:"var(--shiki-token-punctuation)"}},"[ ",-1),_hoisted_10={key:0,style:{color:"var(--shiki-token-punctuation)"}},_hoisted_11=createBaseVNode("span",{style:{color:"var(--shiki-token-punctuation)"}}," ]",-1),_hoisted_12=createBaseVNode("span",{style:{color:"var(--shiki-token-punctuation)"}},"{ ",-1),_hoisted_13={key:0,style:{color:"var(--shiki-token-punctuation)"}},_hoisted_14={style:{color:"var(--shiki-token-parameter)"}},_hoisted_15=createBaseVNode("span",{style:{color:"var(--shiki-token-punctuation)"}}," }",-1),_sfc_main$3=defineComponent({__name:"json",props:{data:{}},setup(e){function t(r){return r.replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/\t/g,"\\t")}return(r,n)=>{const s=resolveComponent("json",!0);return unref(isNullable)(e.data)?(openBlock(),createElementBlock("span",_hoisted_1$2,"null")):typeof e.data=="string"?(openBlock(),createElementBlock("span",_hoisted_2$2,'"'+toDisplayString(t(e.data))+'"',1)):typeof e.data=="number"?(openBlock(),createElementBlock("span",_hoisted_3$2,toDisplayString(e.data),1)):typeof e.data=="boolean"?(openBlock(),createElementBlock("span",_hoisted_4$2,toDisplayString(e.data),1)):e.data instanceof Date?(openBlock(),createElementBlock(Fragment,{key:4},[_hoisted_5$2,_hoisted_6$2,createBaseVNode("span",_hoisted_7,toDisplayString(e.data.toISOString()),1),_hoisted_8],64)):e.data instanceof Array?(openBlock(),createElementBlock(Fragment,{key:5},[_hoisted_9,(openBlock(!0),createElementBlock(Fragment,null,renderList(e.data,(a,u)=>(openBlock(),createElementBlock(Fragment,{key:u},[u?(openBlock(),createElementBlock("span",_hoisted_10,", ")):createCommentVNode("",!0),createVNode(s,{data:a},null,8,["data"])],64))),128)),_hoisted_11],64)):(openBlock(),createElementBlock(Fragment,{key:6},[_hoisted_12,(openBlock(!0),createElementBlock(Fragment,null,renderList(Object.entries(e.data),([a,u],c)=>(openBlock(),createElementBlock(Fragment,{key:c},[c?(openBlock(),createElementBlock("span",_hoisted_13,", ")):createCommentVNode("",!0),createBaseVNode("span",_hoisted_14,'"'+toDisplayString(a)+'"',1),createTextVNode(": "),createVNode(s,{data:u},null,8,["data"])],64))),128)),_hoisted_15],64))}}});var Json=_export_sfc$1(_sfc_main$3,[["__file","json.vue"]]),settings_vue_vue_type_style_index_0_scoped_true_lang="";const _withScopeId$1=e=>(pushScopeId("data-v-7b48ba30"),e=e(),popScopeId(),e),_hoisted_1$1={class:"example-container"},_hoisted_2$1={class:"schema-container"},_hoisted_3$1={class:"right-container"},_hoisted_4$1={class:"theme-default-content"},_hoisted_5$1={id:"test-output1"},_hoisted_6$1=_withScopeId$1(()=>createBaseVNode("header",null,"Output",-1)),_sfc_main$2=defineComponent({__name:"settings",setup(__props){onMounted(()=>{loadConfigs()});const frontmatter=usePageFrontmatter(),schema=computed(()=>eval(frontmatter.value.code)),initial=ref(null),config=ref(null),defaultConfig={},output=computed(()=>{try{return schema.value(config.value)}catch(e){console.log(e)}}),loadConfigs=()=>{let e=localStorage.getItem("ui-configs"),t=defaultConfig;e&&(t=JSON.parse(e)),initial.value=clone(t),config.value=clone(t)},saveSettings=()=>{localStorage.setItem("ui-configs",JSON.stringify(schema.value(config.value)))},reset=()=>{initial.value=clone(defaultConfig),config.value=clone(defaultConfig),saveSettings()};return(e,t)=>{const r=resolveComponent("k-schema"),n=resolveComponent("el-scrollbar"),s=resolveComponent("content"),a=resolveComponent("el-button");return openBlock(),createElementBlock("div",_hoisted_1$1,[createBaseVNode("section",_hoisted_2$1,[createVNode(n,null,{default:withCtx(()=>[createBaseVNode("form",null,[createVNode(r,{modelValue:config.value,"onUpdate:modelValue":t[0]||(t[0]=u=>config.value=u),initial:initial.value,schema:schema.value},null,8,["modelValue","initial","schema"])])]),_:1})]),createBaseVNode("div",_hoisted_3$1,[createBaseVNode("section",_hoisted_4$1,[createVNode(n,null,{default:withCtx(()=>[createBaseVNode("main",null,[createVNode(s)])]),_:1})]),createBaseVNode("section",_hoisted_5$1,[_hoisted_6$1,createBaseVNode("main",null,[createBaseVNode("code",null,[createVNode(Json,{data:output.value},null,8,["data"])])])]),createVNode(a,{style:{margin:"10px 20px 0 20px"},onClick:saveSettings},{default:withCtx(()=>[createTextVNode("\u4FDD\u5B58\u8BBE\u7F6E")]),_:1}),createVNode(a,{style:{margin:"10px 20px 10px 20px"},onClick:reset},{default:withCtx(()=>[createTextVNode("\u5168\u90E8\u91CD\u7F6E")]),_:1})])])}}});var SettingsPage=_export_sfc$1(_sfc_main$2,[["__scopeId","data-v-7b48ba30"],["__file","settings.vue"]]),tools_vue_vue_type_style_index_0_scoped_true_lang="";const _withScopeId=e=>(pushScopeId("data-v-d6bf4500"),e=e(),popScopeId(),e),_hoisted_1={class:"example-container"},_hoisted_2={class:"schema-container"},_hoisted_3={class:"right-container"},_hoisted_4={class:"theme-default-content"},_hoisted_5={id:"test-output1"},_hoisted_6=_withScopeId(()=>createBaseVNode("header",null,"\u53C2\u6570\u9884\u89C8",-1)),_sfc_main$1=defineComponent({__name:"tools",setup(__props){onMounted(()=>{});const frontmatter=usePageFrontmatter(),schema=computed(()=>eval(frontmatter.value.code)),initial=ref(null),config=ref(null),output=computed(()=>{try{return schema.value(config.value)}catch(e){console.log(e)}}),reset=()=>{config.value=null},runScript=()=>{schema.value(config.value)||ElMessage.warning("\u53C2\u6570\u4E3A\u7A7A"),fetch("http://127.0.0.1:28000/api/run_script",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(schema.value(config.value))}).then(t=>{if(!t.ok)throw new Error("Network response was not ok");return t.json()}).then(t=>{console.log(t),t.status=="success"?ElMessage.success("\u4EFB\u52A1\u5DF2\u63D0\u4EA4"):ElMessage.error("\u4EFB\u52A1\u63D0\u4EA4\u5931\u8D25")}).catch(t=>{ElMessage.error("\u65E0\u6CD5\u8FDE\u63A5\u5230\u8BAD\u7EC3\u7AEF\uFF0C\u8BF7\u68C0\u67E5\u662F\u5426\u5F00\u542F\u8BAD\u7EC3\u7AEF\u3002"),console.error("There was a problem with the fetch operation:",t)})};return(e,t)=>{const r=resolveComponent("k-schema"),n=resolveComponent("el-scrollbar"),s=resolveComponent("content"),a=resolveComponent("el-button");return openBlock(),createElementBlock("div",_hoisted_1,[createBaseVNode("section",_hoisted_2,[createVNode(n,null,{default:withCtx(()=>[createBaseVNode("form",null,[createVNode(r,{modelValue:config.value,"onUpdate:modelValue":t[0]||(t[0]=u=>config.value=u),initial:initial.value,schema:schema.value},null,8,["modelValue","initial","schema"])])]),_:1})]),createBaseVNode("div",_hoisted_3,[createBaseVNode("section",_hoisted_4,[createVNode(n,null,{default:withCtx(()=>[createBaseVNode("main",null,[createVNode(s)])]),_:1})]),createBaseVNode("section",_hoisted_5,[_hoisted_6,createBaseVNode("main",null,[createBaseVNode("code",null,[createVNode(Json,{data:output.value},null,8,["data"])])])]),createVNode(a,{style:{margin:"10px 20px 0 20px"},onClick:runScript},{default:withCtx(()=>[createTextVNode("\u542F\u52A8")]),_:1}),createVNode(a,{style:{margin:"10px 20px 10px 20px"},onClick:reset},{default:withCtx(()=>[createTextVNode("\u5168\u90E8\u91CD\u7F6E")]),_:1})])])}}});var ToolsPage=_export_sfc$1(_sfc_main$1,[["__scopeId","data-v-d6bf4500"],["__file","tools.vue"]]);const _sfc_main=defineComponent({__name:"layout",setup(e){const t=usePageData();return(r,n)=>(openBlock(),createBlock(ParentLayout,null,createSlots({_:2},[unref(t).frontmatter.example?{name:"page",fn:withCtx(()=>[(openBlock(),createBlock(MainPage,{key:unref(t).key}))]),key:"0"}:unref(t).frontmatter.type=="iframe"?{name:"page",fn:withCtx(()=>[(openBlock(),createBlock(IframePage,{key:unref(t).key,type:unref(t).frontmatter.subtype},null,8,["type"]))]),key:"1"}:unref(t).frontmatter.type=="tagger"?{name:"page",fn:withCtx(()=>[(openBlock(),createBlock(TaggerPage,{key:unref(t).key}))]),key:"2"}:unref(t).frontmatter.type=="settings"?{name:"page",fn:withCtx(()=>[(openBlock(),createBlock(SettingsPage,{key:unref(t).key}))]),key:"3"}:unref(t).frontmatter.type=="tools"?{name:"page",fn:withCtx(()=>[(openBlock(),createBlock(ToolsPage,{key:unref(t).key}))]),key:"4"}:void 0]),1024))}});var layout=_export_sfc$1(_sfc_main,[["__file","layout.vue"]]);export{layout as default};
