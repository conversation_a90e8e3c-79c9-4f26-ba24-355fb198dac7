# EvoTune公式与网络架构对应关系 - 完整版

## 1. 网络架构图与公式完整对应表

### 1.1 模块级对应关系

| 网络模块 | 公式范围 | 核心公式 | 创新点 |
|----------|----------|----------|--------|
| **EvoTune Scheduler<br/>EvoTune调谐器** | Eq. 1-8 | $\tilde{t} = t/1000$<br/>$\phi_{early/late}$<br/>$b_1(t), s_1(t), w_{attn}(t)$ | 🎯 **统一时间步调谐** |
| **Timestep-aware Agent Attention<br/>时间步感知Agent注意力** | Eq. 9-16 | $A = \text{AdaptivePool}(X)$<br/>$Y_{out} = Y \times w_{attn}(t)$ | 🎯 **时间步权重调制** |
| **Timestep-aware Frequency Processing<br/>时间步感知频域处理** | Eq. 17-21 | $X_{freq} = \text{FFT2D}(Y_{out})$<br/>$\text{Mask}$ based on $s_1(t), s_2(t)$ | 🎯 **自适应频域滤波** |

### 1.2 详细公式对应关系

#### 🔧 EvoTune调谐器模块

| 子模块 | 公式编号 | 数学表达式 | 网络架构图位置 | 功能说明 |
|--------|----------|------------|----------------|----------|
| Time Normalization<br/>时间步标准化 | **Eq. 1** | $\tilde{t} = \frac{t}{1000}$ | 调谐器第一层 | 标准化时间步到[0,1] |
| Phase Computation<br/>相位计算 | **Eq. 2** | $\phi_{early}(t) = \sigma(10(\tilde{t} - 0.8))$ | 调谐器第二层 | 早期相位函数 |
| | **Eq. 3** | $\phi_{late}(t) = \sigma(10(0.2 - \tilde{t}))$ | 调谐器第二层 | 后期相位函数 |
| Parameter Generation<br/>参数生成 | **Eq. 4** | $b_1(t) = 1.5 + 0.3 \times \phi_{early}(t) \times 0.5$ | 调谐器第三层 | 骨干特征权重1 |
| | **Eq. 5** | $b_2(t) = 1.2 + 0.3 \times \phi_{late}(t) \times 0.3$ | 调谐器第三层 | 骨干特征权重2 |
| | **Eq. 6** | $s_1(t) = 0.8 + 0.3 \times \phi_{late}(t) \times 0.4$ | 调谐器第三层 | 频域缩放参数1 |
| | **Eq. 7** | $s_2(t) = 0.5 + 0.3 \times \phi_{late}(t) \times 0.3$ | 调谐器第三层 | 频域缩放参数2 |
| | **Eq. 8** | $w_{attn}(t) = 0.5 + 0.5 \times \sigma(8(0.3 - \tilde{t}))$ | 调谐器第三层 | **注意力权重** |

#### 🎯 时间步感知Agent注意力模块

| 子模块 | 公式编号 | 数学表达式 | 网络架构图位置 | 功能说明 |
|--------|----------|------------|----------------|----------|
| Input Processing<br/>输入处理 | **Eq. 9** | $X_{seq} = \text{Flatten}(X)^T \in \mathbb{R}^{B \times (H \times W) \times C}$ | 注意力第一层 | 序列化输入特征 |
| Agent Generation<br/>Agent生成 | **Eq. 10** | $A = \text{AdaptiveAvgPool2d}(X) \in \mathbb{R}^{B \times 49 \times C}$ | 注意力第二层 | 生成49个Agent tokens |
| QKV Computation<br/>QKV计算 | **Eq. 11** | $Q, K = \text{Linear}(A) \in \mathbb{R}^{B \times 49 \times C}$ | 注意力第三层 | Query和Key计算 |
| | **Eq. 12** | $V = \text{DepthwiseConv2d}(X_{seq}) \in \mathbb{R}^{B \times (H \times W) \times C}$ | 注意力第三层 | Value计算 |
| Bi-directional Attention<br/>双向注意力 | **Eq. 13** | $\text{Attn}_1 = \text{softmax}\left(\frac{Q \cdot K^T}{\sqrt{d}}\right)$ | 注意力第四层 | Agent到空间注意力 |
| | **Eq. 14** | $\text{Attn}_2 = \text{softmax}\left(\frac{K \cdot Q^T}{\sqrt{d}}\right)$ | 注意力第四层 | 空间到Agent注意力 |
| Feature Fusion<br/>特征融合 | **Eq. 15** | $Y = \text{Attn}_2 \cdot (\text{Attn}_1 \cdot V) + V$ | 注意力第五层 | 双向注意力特征融合 |
| **Timestep Modulation<br/>时间步调制** | **Eq. 16** | $Y_{out} = Y \times w_{attn}(t)$ | 注意力第六层 | **🎯 核心创新：时间步权重调制** |

#### 🌊 时间步感知频域处理模块

| 子模块 | 公式编号 | 数学表达式 | 网络架构图位置 | 功能说明 |
|--------|----------|------------|----------------|----------|
| FFT Transform<br/>FFT变换 | **Eq. 17** | $X_{freq} = \text{FFT2D}(Y_{out})$ | 频域第一层 | 空间域到频域变换 |
| Dynamic Threshold<br/>动态阈值 | **Eq. 18** | $\tau(t) = 7 + \lfloor 5(1 - \tilde{t}) \rfloor$ | 频域第二层 | 时间步感知的阈值 |
| **Adaptive Filtering<br/>自适应滤波** | **Eq. 19** | $\text{Mask}(u,v) = \begin{cases} s_1(t) & \text{if } \|u-u_c\|,\|v-v_c\| \leq \tau(t) \\ 1 & \text{otherwise} \end{cases}$ | 频域第二层 | **🎯 核心创新：自适应滤波器** |
| Frequency Filtering<br/>频域滤波 | **Eq. 20** | $X_{filtered} = X_{freq} \odot \text{Mask}(u, v)$ | 频域第二层 | 应用自适应滤波 |
| IFFT Transform<br/>IFFT逆变换 | **Eq. 21** | $X_{enhanced} = \text{IFFT2D}(X_{filtered})$ | 频域第三层 | 频域到空间域变换 |

#### 🔄 完整前向传播

| 模块 | 公式编号 | 数学表达式 | 网络架构图位置 | 功能说明 |
|------|----------|------------|----------------|----------|
| Complete Forward<br/>完整前向传播 | **Eq. 22** | $\hat{x}_{t-1} = \text{EvoTune}(x_t, t)$ | 输出层 | 完整的EvoTune处理 |
| Loss Function<br/>损失函数 | **Eq. 23** | $\mathcal{L} = \mathbb{E}_{x_0, \epsilon, t}[\|\epsilon - \epsilon_\theta(\text{EvoTune}(x_t, t))\|_2^2]$ | - | 训练损失函数 |

## 2. 参数演化与网络模块关系

### 2.1 时间步参数演化表

| 时间步 $t$ | $\tilde{t}$ | $\phi_{early}$ | $\phi_{late}$ | $b_1(t)$ | $s_1(t)$ | $w_{attn}(t)$ | 主导模块 |
|------------|-------------|----------------|---------------|----------|----------|---------------|----------|
| **900** | 0.900 | **0.731** | 0.001 | **1.610** ↑ | 0.800 | 0.504 | **调谐器** → 结构建立 |
| **500** | 0.500 | 0.047 | 0.047 | 1.507 | 0.806 | 0.584 | **平衡** → 过渡阶段 |
| **100** | 0.100 | 0.001 | **0.731** | 1.500 | **0.888** ↑ | **0.916** ↑ | **注意力+频域** → 细节优化 |

### 2.2 模块激活强度

```
时间步演化: t=1000 ────────── t=500 ────────── t=0
           早期             中期             后期

调谐器:     ████████████    ████████████    ████████████
           (始终活跃)       (始终活跃)       (始终活跃)

Agent注意力: ████████        ████████████    ████████████████
           (弱激活)         (中等激活)       (强激活)

频域处理:   ████████        ████████████    ████████████████
           (保持结构)       (适度滤波)       (强化细节)
```

## 3. 创新点在网络架构中的体现

### 3.1 核心创新1：统一时间步调谐

**网络位置**：EvoTune Scheduler模块
**关键公式**：Eq. 1-8
**创新体现**：
- 一个调谐器控制多个执行模块
- 时间步感知的参数生成
- 相位函数设计体现扩散过程特性

### 3.2 核心创新2：时间步感知Agent注意力

**网络位置**：Timestep Modulation子模块
**关键公式**：Eq. 16 - $Y_{out} = Y \times w_{attn}(t)$
**创新体现**：
- Agent Attention不是独立模块，而是时间步调谐的执行器
- 通过$w_{attn}(t)$实现时间步感知
- 后期增强注意力，前期减弱注意力

### 3.3 核心创新3：时间步感知频域处理

**网络位置**：Adaptive Filtering子模块
**关键公式**：Eq. 19 - 基于$s_1(t), s_2(t)$的自适应滤波器
**创新体现**：
- 频域处理也是时间步调谐的执行器
- 动态阈值$\tau(t)$随时间步变化
- 早期保持结构，后期增强细节

## 4. 数据流向与公式执行顺序

### 4.1 完整数据流

```
输入 (x_t, t) 
    │
    ├─→ EvoTune调谐器 (Eq. 1-8)
    │   └─→ 参数生成: {b₁(t), s₁(t), s₂(t), w_attn(t)}
    │
    └─→ Agent注意力 (Eq. 9-15)
        └─→ 时间步调制 (Eq. 16) ←── w_attn(t)
            └─→ 频域处理 (Eq. 17-21) ←── s₁(t), s₂(t)
                └─→ 增强特征 (Eq. 22)
```

### 4.2 公式执行顺序

1. **并行执行**：Eq. 1-8 (调谐器) + Eq. 9-15 (注意力前处理)
2. **串行执行**：Eq. 16 (时间步调制) → Eq. 17-21 (频域处理) → Eq. 22 (输出)
3. **参数传递**：调谐器输出 → 执行模块输入

## 5. 论文写作建议

### 5.1 图表组合

**Figure 1**: 详细版网络架构图 + 公式对应表
**Figure 2**: 简化版网络架构图 + 创新点标注
**Figure 3**: 参数演化图 + 时间步分析

### 5.2 文字描述模板

```
如图1所示，EvoTune包含三个主要模块：(1) EvoTune调谐器根据时间步t生成自适应参数(Eq. 1-8)；
(2) 时间步感知Agent注意力通过w_attn(t)实现时间步权重调制(Eq. 16)；
(3) 时间步感知频域处理通过s₁(t), s₂(t)实现自适应滤波(Eq. 19)。
关键创新在于统一的时间步调谐框架，使得所有模块都具有时间步感知能力。
```

### 5.3 公式引用规范

- **调谐器**：参考Eq. 1-8
- **注意力调制**：重点引用Eq. 16
- **自适应滤波**：重点引用Eq. 19
- **完整系统**：引用Eq. 22

这个完整的对应关系确保了网络架构图与数学公式的完美匹配，符合顶会论文的严谨标准。
