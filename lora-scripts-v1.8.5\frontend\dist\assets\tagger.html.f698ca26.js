import{_ as a,o as n,c as r,a as e,b as t}from"./app.9273d30a.js";const o={},s=e("h1",{id:"tagger-\u6807\u6CE8\u5DE5\u5177",tabindex:"-1"},[e("a",{class:"header-anchor",href:"#tagger-\u6807\u6CE8\u5DE5\u5177","aria-hidden":"true"},"#"),t(" Tagger \u6807\u6CE8\u5DE5\u5177")],-1),c=e("p",null,"\u540E\u7AEF\u57FA\u4E8E wd14-tagger \u5F00\u53D1\u3002",-1),d=e("p",null,[t("\uFF08\u5982\u679C\u4F60\u81EA\u884C\u90E8\u7F72\uFF0C\u6216\u6709\u826F\u597D\u7684\u7F51\u7EDC\u8BBF\u95EE\u73AF\u5883\uFF0C\u8BF7\u5FFD\u7565\u8FD9\u884C\uFF09\u8BAD\u7EC3\u5305\u5185\u81EA\u5E26\u9ED8\u8BA4\u79BB\u7EBF\u6A21\u578B "),e("code",null,"SmilingWolf/wd-v1-4-convnextv2-tagger-v2"),t(" \u5982\u679C\u4F60\u9009\u62E9\u4E86\u5176\u4ED6\u6A21\u578B\uFF0C\u53EF\u80FD\u9700\u8981\u989D\u5916\u8FDE\u63A5\u5230 Huggingface \u8FDB\u884C\u4E0B\u8F7D\u3002")],-1),_=e("p",null,"\u5DF2\u66F4\u65B0 v3 \u7684 Tagger \u6A21\u578B\u3002",-1),i=e("h3",{id:"\u63A8\u8350\u53C2\u6570",tabindex:"-1"},[e("a",{class:"header-anchor",href:"#\u63A8\u8350\u53C2\u6570","aria-hidden":"true"},"#"),t(" \u63A8\u8350\u53C2\u6570")],-1),l=e("p",null,"\u9608\u503C\u5927\u4E8E 0.35",-1),g=[s,c,d,_,i,l];function h(u,f){return n(),r("div",null,g)}var v=a(o,[["render",h],["__file","tagger.html.vue"]]);export{v as default};
