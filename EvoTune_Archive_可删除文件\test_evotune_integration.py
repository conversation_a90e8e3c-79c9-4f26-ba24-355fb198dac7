"""
测试EvoTune集成功能
"""

import sys
import os

# 添加SD WebUI路径
webui_path = "sd-webui-aki-v4.4"
sys.path.append(webui_path)

def test_evotune_script():
    """测试EvoTune Script是否正确加载"""
    try:
        # 导入EvoTune Script
        sys.path.append(os.path.join(webui_path, "extensions", "evotune", "scripts"))
        from evotune_script import EvoTuneScript
        
        # 创建实例
        script = EvoTuneScript()
        
        print("✅ EvoTune Script加载成功")
        print(f"   标题: {script.title()}")
        print(f"   显示模式: {script.show(False)}")
        
        # 测试参数处理
        class MockP:
            def __init__(self):
                self.cfg_scale = 7.0
                self.steps = 20
                self.extra_generation_params = {}
        
        mock_p = MockP()
        
        # 测试process方法
        script.process(mock_p, True, 1.5, 1.2, 0.8, 0.5, True)
        
        print("✅ EvoTune参数处理成功")
        print(f"   CFG Scale: {mock_p.cfg_scale}")
        print(f"   Steps: {mock_p.steps}")
        print(f"   Extra params: {mock_p.extra_generation_params}")
        
        return True
        
    except Exception as e:
        print(f"❌ EvoTune Script测试失败: {e}")
        return False

def test_evotune_extension():
    """测试EvoTune扩展是否正确加载"""
    try:
        sys.path.append(os.path.join(webui_path, "extensions", "evotune", "scripts"))
        from evotune_extension import evotune_available, get_evotune_config
        
        print(f"✅ EvoTune扩展状态: {'可用' if evotune_available else '不可用'}")
        
        config = get_evotune_config()
        print(f"✅ EvoTune配置: {config}")
        
        return True
        
    except Exception as e:
        print(f"❌ EvoTune扩展测试失败: {e}")
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("    EvoTune集成功能测试")
    print("=" * 50)
    
    # 测试扩展
    print("\n1. 测试EvoTune扩展...")
    ext_ok = test_evotune_extension()
    
    # 测试Script
    print("\n2. 测试EvoTune Script...")
    script_ok = test_evotune_script()
    
    print("\n" + "=" * 50)
    if ext_ok and script_ok:
        print("🎉 所有测试通过！EvoTune已成功集成到SD WebUI")
        print("\n使用方法:")
        print("1. 启动SD WebUI")
        print("2. 在生成界面底部找到'EvoTune优化'面板")
        print("3. 启用EvoTune并调整参数")
        print("4. 正常生成图像")
    else:
        print("⚠️ 部分测试失败，请检查配置")
    print("=" * 50)
