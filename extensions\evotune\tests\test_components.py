"""
EvoTune组件单元测试
"""

import unittest
import torch
import torch.nn as nn
import sys
import os

# 添加模块路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)

from modules.timestep_scheduler import TimestepScheduler
from modules.agent_attention import AgentAttention, TimestepAwareAgentAttention
from modules.fourier_processor import AdaptiveFourierProcessor
from modules.evotune_controller import EvoTuneController

class TestTimestepScheduler(unittest.TestCase):
    """测试时间步调谐器"""
    
    def setUp(self):
        self.scheduler = TimestepScheduler(total_timesteps=1000)
    
    def test_normalize_timestep(self):
        """测试时间步标准化"""
        # 测试标量输入
        t = 500
        t_norm = self.scheduler.normalize_timestep(t)
        self.assertAlmostEqual(t_norm.item(), 0.5, places=3)
        
        # 测试张量输入
        t_batch = torch.tensor([0, 250, 500, 750, 999])
        t_norm_batch = self.scheduler.normalize_timestep(t_batch)
        expected = torch.tensor([0.0, 0.25, 0.5, 0.75, 1.0])
        torch.testing.assert_close(t_norm_batch, expected, atol=1e-3, rtol=1e-3)
    
    def test_phase_functions(self):
        """测试相位函数"""
        t_norm = torch.tensor([0.0, 0.5, 1.0])
        phi_early, phi_late = self.scheduler.compute_phase_functions(t_norm)
        
        # 检查边界值
        self.assertAlmostEqual(phi_early[0].item(), 1.0, places=3)  # cos(0) = 1
        self.assertAlmostEqual(phi_late[0].item(), 0.0, places=3)   # sin(0) = 0
        self.assertAlmostEqual(phi_early[2].item(), 0.0, places=3)  # cos(π/2) = 0
        self.assertAlmostEqual(phi_late[2].item(), 1.0, places=3)   # sin(π/2) = 1
    
    def test_parameter_generation(self):
        """测试参数生成"""
        timesteps = torch.tensor([0, 500, 999])
        params = self.scheduler.get_adaptive_params(timesteps)
        
        # 检查返回的参数
        self.assertIn('b_weights', params)
        self.assertIn('s_weights', params)
        self.assertIn('w_attn', params)
        
        # 检查形状
        self.assertEqual(params['b_weights'].shape, (3, 2))
        self.assertEqual(params['s_weights'].shape, (3, 2))
        self.assertEqual(params['w_attn'].shape, (3,))
    
    def test_parameter_ranges(self):
        """测试参数范围"""
        timesteps = torch.linspace(0, 999, 100).int()
        params = self.scheduler.get_adaptive_params(timesteps)
        
        # 检查b权重范围
        b_weights = params['b_weights']
        self.assertTrue(torch.all(b_weights[:, 0] >= self.scheduler.b1_range[0]))
        self.assertTrue(torch.all(b_weights[:, 0] <= self.scheduler.b1_range[1]))
        
        # 检查s权重范围
        s_weights = params['s_weights']
        self.assertTrue(torch.all(s_weights[:, 0] >= self.scheduler.s1_range[0]))
        self.assertTrue(torch.all(s_weights[:, 0] <= self.scheduler.s1_range[1]))


class TestAgentAttention(unittest.TestCase):
    """测试Agent Attention机制"""
    
    def setUp(self):
        self.channels = 64
        self.batch_size = 2
        self.height = 32
        self.width = 32
        self.agent_attention = AgentAttention(
            channels=self.channels,
            num_agents=49,
            agent_dim=32
        )
    
    def test_agent_token_generation(self):
        """测试Agent token生成"""
        x = torch.randn(self.batch_size, self.channels, self.height, self.width)
        agents = self.agent_attention.generate_agent_tokens(x)
        
        # 检查形状
        self.assertEqual(agents.shape, (self.batch_size, 49, 32))
    
    def test_forward_pass(self):
        """测试前向传播"""
        x = torch.randn(self.batch_size, self.channels, self.height, self.width)
        output, agents, spatial_attn = self.agent_attention(x)
        
        # 检查输出形状
        self.assertEqual(output.shape, x.shape)
        self.assertEqual(agents.shape, (self.batch_size, 49, 32))
        self.assertEqual(spatial_attn.shape, (self.batch_size, self.height * self.width, 49))
    
    def test_timestep_aware_attention(self):
        """测试时间步感知注意力"""
        timestep_attention = TimestepAwareAgentAttention(
            channels=self.channels,
            num_agents=49,
            agent_dim=32
        )
        
        x = torch.randn(self.batch_size, self.channels, self.height, self.width)
        w_attn = torch.tensor([0.5, 0.8])
        
        output = timestep_attention(x, w_attn)
        self.assertEqual(output.shape, x.shape)


class TestAdaptiveFourierProcessor(unittest.TestCase):
    """测试自适应频域处理器"""
    
    def setUp(self):
        self.channels = 64
        self.batch_size = 2
        self.height = 32
        self.width = 32
        self.processor = AdaptiveFourierProcessor(channels=self.channels)
    
    def test_dynamic_threshold(self):
        """测试动态阈值计算"""
        t_norm = torch.tensor([0.0, 0.5, 1.0])
        threshold = self.processor.compute_dynamic_threshold(t_norm)
        
        # 检查阈值范围
        self.assertTrue(torch.all(threshold >= 7))
        self.assertTrue(torch.all(threshold <= 12))
        
        # 检查单调性（时间步越早，阈值越大）
        self.assertGreaterEqual(threshold[0].item(), threshold[2].item())
    
    def test_filter_creation(self):
        """测试滤波器创建"""
        H, W = 32, 32
        threshold = torch.tensor(8.0)
        s1 = torch.tensor(0.8)
        s2 = torch.tensor(0.6)
        device = torch.device('cpu')
        
        filter_mask = self.processor.create_adaptive_filter(H, W, threshold, s1, s2, device)
        
        # 检查形状
        self.assertEqual(filter_mask.shape, (1, 1, H, W))
        
        # 检查中心区域应用了s1
        center = H // 2
        self.assertAlmostEqual(filter_mask[0, 0, center, center].item(), s1.item(), places=3)
    
    def test_fft_operations(self):
        """测试FFT操作"""
        x = torch.randn(self.batch_size, self.channels, self.height, self.width)
        
        # 测试安全FFT
        x_freq = self.processor.safe_fft2d(x)
        self.assertEqual(x_freq.shape, x.shape)
        self.assertTrue(x_freq.dtype in [torch.complex64, torch.complex128])
        
        # 测试安全IFFT
        x_reconstructed = self.processor.safe_ifft2d(x_freq)
        self.assertEqual(x_reconstructed.shape, x.shape)
        self.assertTrue(x_reconstructed.dtype in [torch.float32, torch.float64])
    
    def test_forward_pass(self):
        """测试前向传播"""
        x = torch.randn(self.batch_size, self.channels, self.height, self.width)
        s1 = torch.tensor([0.8, 0.9])
        s2 = torch.tensor([0.6, 0.7])
        t_norm = torch.tensor([0.3, 0.7])
        
        output = self.processor(x, s1, s2, t_norm)
        
        # 检查输出形状
        self.assertEqual(output.shape, x.shape)
        self.assertEqual(output.dtype, x.dtype)


class TestEvoTuneController(unittest.TestCase):
    """测试EvoTune控制器"""
    
    def setUp(self):
        self.channels = 64
        self.batch_size = 2
        self.height = 32
        self.width = 32
        self.controller = EvoTuneController(
            channels=self.channels,
            total_timesteps=1000
        )
    
    def test_dimension_adapter(self):
        """测试维度适配器"""
        # 测试匹配通道数
        adapter_same = self.controller.get_dimension_adapter(self.channels)
        self.assertIsInstance(adapter_same, nn.Identity)
        
        # 测试不匹配通道数
        adapter_diff = self.controller.get_dimension_adapter(128)
        self.assertIsInstance(adapter_diff, nn.Sequential)
    
    def test_feature_modulation(self):
        """测试特征调制"""
        x = torch.randn(self.batch_size, self.channels, self.height, self.width)
        timesteps = torch.tensor([100, 800])
        
        # 测试骨干特征调制
        output_backbone = self.controller.modulate_backbone_features(x, timesteps)
        self.assertEqual(output_backbone.shape, x.shape)
        
        # 测试跳跃连接调制
        output_skip = self.controller.modulate_skip_features(x, timesteps)
        self.assertEqual(output_skip.shape, x.shape)
    
    def test_forward_pass(self):
        """测试前向传播"""
        x = torch.randn(self.batch_size, self.channels, self.height, self.width)
        timesteps = torch.tensor([100, 800])
        
        # 测试不同调制类型
        output_backbone = self.controller(x, timesteps, 'backbone')
        output_skip = self.controller(x, timesteps, 'skip')
        
        self.assertEqual(output_backbone.shape, x.shape)
        self.assertEqual(output_skip.shape, x.shape)
    
    def test_error_handling(self):
        """测试错误处理"""
        x = torch.randn(self.batch_size, self.channels, self.height, self.width)
        timesteps = torch.tensor([100, 800])
        
        # 测试无效调制类型
        with self.assertRaises(ValueError):
            self.controller(x, timesteps, 'invalid_type')


class TestIntegration(unittest.TestCase):
    """集成测试"""
    
    def test_end_to_end_pipeline(self):
        """测试端到端流水线"""
        # 创建组件
        scheduler = TimestepScheduler(total_timesteps=1000)
        controller = EvoTuneController(channels=64, total_timesteps=1000)
        
        # 创建测试数据
        batch_size = 2
        channels = 64
        height = 32
        width = 32
        x = torch.randn(batch_size, channels, height, width)
        timesteps = torch.tensor([100, 800])
        
        # 测试完整流程
        params = scheduler.get_adaptive_params(timesteps)
        output = controller(x, timesteps, 'backbone')
        
        # 验证输出
        self.assertEqual(output.shape, x.shape)
        self.assertFalse(torch.isnan(output).any())
        self.assertFalse(torch.isinf(output).any())
    
    def test_gradient_flow(self):
        """测试梯度流"""
        controller = EvoTuneController(channels=64, total_timesteps=1000)
        
        x = torch.randn(2, 64, 32, 32, requires_grad=True)
        timesteps = torch.tensor([100, 800])
        
        output = controller(x, timesteps, 'backbone')
        loss = output.sum()
        loss.backward()
        
        # 检查梯度
        self.assertIsNotNone(x.grad)
        self.assertFalse(torch.isnan(x.grad).any())


def run_tests():
    """运行所有测试"""
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试类
    test_classes = [
        TestTimestepScheduler,
        TestAgentAttention,
        TestAdaptiveFourierProcessor,
        TestEvoTuneController,
        TestIntegration
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    return result.wasSuccessful()


if __name__ == '__main__':
    success = run_tests()
    if success:
        print("\n✅ All tests passed!")
    else:
        print("\n❌ Some tests failed!")
        sys.exit(1)
