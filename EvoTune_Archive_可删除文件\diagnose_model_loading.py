"""
SD模型加载诊断脚本
"""

import os
import hashlib
import json
from pathlib import Path

def get_file_hash(file_path, chunk_size=8192):
    """计算文件hash"""
    hash_sha256 = hashlib.sha256()
    try:
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(chunk_size), b""):
                hash_sha256.update(chunk)
        return hash_sha256.hexdigest()
    except Exception as e:
        return f"Error: {e}"

def check_model_files():
    """检查模型文件状态"""
    print("=== SD模型文件诊断 ===")
    print()
    
    # 模型目录
    model_dir = Path("sd-webui-aki-v4.4/models/Stable-diffusion")
    
    if not model_dir.exists():
        print(f"❌ 模型目录不存在: {model_dir}")
        return
    
    print(f"📁 模型目录: {model_dir}")
    print()
    
    # 检查配置中的模型
    config_file = Path("sd-webui-aki-v4.4/config.json")
    expected_model = None
    
    if config_file.exists():
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
                expected_model = config.get('sd_model_checkpoint', '')
                expected_hash = config.get('sd_checkpoint_hash', '')
            
            print(f"📋 配置中的模型: {expected_model}")
            print(f"📋 期望的hash: {expected_hash[:12]}...")
            print()
        except Exception as e:
            print(f"❌ 读取配置文件失败: {e}")
    
    # 扫描所有模型文件
    model_files = []
    for ext in ['*.ckpt', '*.safetensors']:
        model_files.extend(model_dir.rglob(ext))
    
    print(f"🔍 找到 {len(model_files)} 个模型文件:")
    print()
    
    for model_file in sorted(model_files):
        relative_path = model_file.relative_to(model_dir)
        file_size = model_file.stat().st_size / (1024**3)  # GB
        
        print(f"📄 {relative_path}")
        print(f"   大小: {file_size:.2f} GB")
        
        # 检查文件是否可读
        try:
            with open(model_file, 'rb') as f:
                f.read(1024)  # 尝试读取前1KB
            print(f"   状态: ✅ 可读")
        except Exception as e:
            print(f"   状态: ❌ 读取失败 - {e}")
            continue
        
        # 如果是期望的模型，计算hash验证
        if expected_model and str(relative_path) in expected_model:
            print(f"   🎯 这是配置中的模型，正在验证hash...")
            file_hash = get_file_hash(model_file)
            if file_hash.startswith("Error"):
                print(f"   Hash: ❌ {file_hash}")
            else:
                print(f"   Hash: {file_hash[:12]}...")
                if expected_hash and file_hash == expected_hash:
                    print(f"   验证: ✅ Hash匹配")
                elif expected_hash:
                    print(f"   验证: ❌ Hash不匹配")
                else:
                    print(f"   验证: ⚠️ 配置中无hash记录")
        
        print()

def check_webui_logs():
    """检查WebUI日志"""
    print("=== WebUI启动日志分析 ===")
    print()
    
    # 检查是否有启动日志
    log_files = [
        "sd-webui-aki-v4.4/webui.log",
        "sd-webui-aki-v4.4/errors.log",
        "sd-webui-aki-v4.4/stdout.txt"
    ]
    
    for log_file in log_files:
        if os.path.exists(log_file):
            print(f"📋 发现日志文件: {log_file}")
            try:
                with open(log_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    if lines:
                        print(f"   最后几行:")
                        for line in lines[-5:]:
                            print(f"   {line.strip()}")
                print()
            except Exception as e:
                print(f"   ❌ 读取失败: {e}")
        else:
            print(f"📋 日志文件不存在: {log_file}")
    
    print()

def check_system_resources():
    """检查系统资源"""
    print("=== 系统资源检查 ===")
    print()
    
    try:
        import psutil
        
        # 内存检查
        memory = psutil.virtual_memory()
        print(f"💾 系统内存:")
        print(f"   总计: {memory.total / (1024**3):.1f} GB")
        print(f"   可用: {memory.available / (1024**3):.1f} GB")
        print(f"   使用率: {memory.percent}%")
        print()
        
        # 磁盘检查
        disk = psutil.disk_usage('.')
        print(f"💿 磁盘空间:")
        print(f"   总计: {disk.total / (1024**3):.1f} GB")
        print(f"   可用: {disk.free / (1024**3):.1f} GB")
        print(f"   使用率: {(disk.used / disk.total) * 100:.1f}%")
        print()
        
    except ImportError:
        print("⚠️ psutil未安装，跳过系统资源检查")
        print("   可运行: pip install psutil")
        print()

def suggest_solutions():
    """建议解决方案"""
    print("=== 建议解决方案 ===")
    print()
    
    print("🔧 如果模型加载超时，尝试以下方法:")
    print()
    print("1. 检查模型文件完整性:")
    print("   - 重新下载损坏的模型文件")
    print("   - 验证文件hash是否正确")
    print()
    print("2. 减少内存使用:")
    print("   - 添加启动参数: --lowvram 或 --medvram")
    print("   - 关闭其他占用内存的程序")
    print()
    print("3. 清理缓存:")
    print("   - 删除 sd-webui-aki-v4.4/cache 目录")
    print("   - 重置配置文件")
    print()
    print("4. 使用更小的模型:")
    print("   - 尝试加载 v1-5-pruned.ckpt")
    print("   - 确认模型加载正常后再换回大模型")
    print()
    print("5. 检查启动参数:")
    print("   - 移除可能冲突的参数")
    print("   - 使用最小参数启动")

def main():
    """主函数"""
    print("🔍 SD模型加载问题诊断工具")
    print("=" * 50)
    print()
    
    # 检查模型文件
    check_model_files()
    
    # 检查日志
    check_webui_logs()
    
    # 检查系统资源
    check_system_resources()
    
    # 建议解决方案
    suggest_solutions()
    
    print("=" * 50)
    print("诊断完成！请根据上述信息排查问题。")

if __name__ == "__main__":
    main()
