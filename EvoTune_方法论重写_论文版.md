# EvoTune方法论 - 论文版重写

## 3. 方法论 (Methodology)

### 3.1 问题定义与动机

针对数据集中积水干扰样本稀缺的问题，本文提出了一种名为EvoTune的数据增强方法。该方法的核心创新在于将EvoTune设计为**UNet内部的智能控制器**，深度集成到Stable Diffusion的扩散去噪过程中，通过时间步感知的动态调谐机制，在UNet的关键连接点精确控制积水区域的生成质量。

与传统的外部后处理方法不同，EvoTune直接作用于UNet的**编码器-解码器连接点**和**跳跃连接处**，实现真正的内部特征调制。该方法认识到扩散去噪过程的不同阶段具有不同的生成需求：早期阶段需要建立积水区域的基础结构，后期阶段需要优化水纹理的细节特征。基于这一洞察，EvoTune设计了统一的时间步调谐框架，使UNet能够在不同扩散阶段采用最适合的特征处理策略，从而生成更真实、细节更丰富的积水干扰图像。

### 3.2 EvoTune作为UNet中间控制器的架构设计

EvoTune的整体架构如图1所示，其核心设计理念是将EvoTune作为**UNet中间控制器**，位于编码器和解码器的关键连接点。整个系统由四个协同工作的模块组成：**时间步感知调谐器**、**Agent Attention机制**、**自适应频域处理器**和**UNet特征调制器**。

#### 3.2.1 EvoTune在UNet中的精确定位

根据图1所示的架构，EvoTune控制器被精确定位在UNet的两个关键位置：

1. **骨干调制位置**：编码器最深层与解码器第一层之间的连接点
2. **跳跃调制位置**：编码器各层到解码器对应层的跳跃连接处

这种定位设计确保EvoTune能够在特征从编码阶段向解码阶段传递的关键时刻进行干预，对积水区域的生成进行精确控制。

#### 3.2.2 时间步感知调谐框架

EvoTune控制器的核心是统一的时间步感知调谐框架，该框架为UNet中的特征调制提供动态参数。给定当前时间步$t \in [0, 1000]$，调谐器首先进行时间步标准化和相位计算：

$$\tilde{t} = \frac{t}{T}, \quad \phi_{\mathrm{early}}(\tilde{t}) = \sigma(10(\tilde{t} - 0.8)), \quad \phi_{\mathrm{late}}(\tilde{t}) = \sigma(10(0.2 - \tilde{t})) \tag{1}$$

其中$T=1000$为总时间步数，$\phi_{\mathrm{early}}(\tilde{t})$和$\phi_{\mathrm{late}}(\tilde{t})$分别为早期和后期相位函数，用于识别扩散过程中积水区域生成的不同阶段需求。

**这一步的作用**：为后续的UNet特征调制提供时间步感知的基础信号，确保积水区域的生成策略能够随扩散阶段动态调整。

基于相位函数，调谐器采用统一的向量化公式生成用于UNet内部调制的参数：

**骨干特征权重统一调谐**：
$$\boldsymbol{b}(t) = \boldsymbol{b}_0 + \alpha \cdot \boldsymbol{\phi}_{\mathrm{backbone}}(t) \odot \boldsymbol{c}_{\mathrm{backbone}} \tag{2}$$

其中：
$$\boldsymbol{b}_0 = \begin{bmatrix} 1.5 \\ 1.2 \end{bmatrix}, \quad \boldsymbol{\phi}_{\mathrm{backbone}}(t) = \begin{bmatrix} \phi_{\mathrm{early}}(\tilde{t}) \\ \phi_{\mathrm{late}}(\tilde{t}) \end{bmatrix}, \quad \boldsymbol{c}_{\mathrm{backbone}} = \begin{bmatrix} 0.5 \\ 0.3 \end{bmatrix}, \quad \alpha = 0.3$$

**这一步的作用**：生成用于UNet骨干特征和跳跃连接调制的动态权重。$b_1(t)$在早期激活，用于在编码器-解码器连接点增强骨干特征，建立积水区域的基础结构；$b_2(t)$在后期激活，用于在跳跃连接处优化特征融合，完善积水区域的细节表达。

**频域滤波参数统一调谐**：
$$\boldsymbol{s}(t) = \boldsymbol{s}_0 + \alpha \cdot \boldsymbol{\phi}_{\mathrm{fourier}}(t) \odot \boldsymbol{c}_{\mathrm{fourier}} \tag{3}$$

其中：
$$\boldsymbol{s}_0 = \begin{bmatrix} 0.8 \\ 0.5 \end{bmatrix}, \quad \boldsymbol{\phi}_{\mathrm{fourier}}(t) = \begin{bmatrix} \phi_{\mathrm{late}}(\tilde{t}) \\ \phi_{\mathrm{late}}(\tilde{t}) \end{bmatrix}, \quad \boldsymbol{c}_{\mathrm{fourier}} = \begin{bmatrix} 0.4 \\ 0.3 \end{bmatrix}$$

**这一步的作用**：生成用于频域滤波的动态缩放参数。$s_1(t)$和$s_2(t)$都在后期激活，用于在UNet特征处理过程中对水纹理的频域成分进行精细控制，增强积水区域的真实感。

**注意力权重调谐**：
$$w_{\mathrm{attn}}(t) = 0.5 + 0.5 \times \sigma(8(0.3 - \tilde{t})) \tag{4}$$

**这一步的作用**：生成用于Agent Attention调制的动态权重，在后期阶段增强注意力机制对水油边界的精细化处理能力。

这种统一的调谐设计实现了多个UNet调制参数的协同优化，避免了参数冲突，确保了积水区域生成的一致性和高质量。

#### 3.2.3 Agent Attention机制在积水区域生成中的关键作用

为了在UNet的特征处理过程中精确识别和增强积水区域，EvoTune引入了专门针对水油场景优化的Agent Attention机制。该机制通过空间代理策略解决了传统注意力机制在积水生成中的局限性，实现了高效且精确的积水区域建模。

##### 3.2.3.1 传统注意力机制的局限性与Agent Attention的创新

传统的自注意力机制在处理积水干扰图像生成时存在计算复杂度高($O(N^2)$)、缺乏空间结构感知、时间步不敏感等问题。EvoTune的Agent Attention机制通过以下创新设计解决了这些问题：

**空间代理机制**：使用49个Agent tokens代替全像素注意力，将计算复杂度从$O(N^2)$降低到$O(49N)$，同时通过$7 \times 7$网格结构更好地捕获积水区域的空间分布特征。

**Agent Token生成**：
$$\boldsymbol{A} = \mathrm{AdaptiveAvgPool2d}(\boldsymbol{H}_{\mathrm{encoder}}) \in \mathbb{R}^{B \times 49 \times C} \tag{5}$$

其中$\boldsymbol{H}_{\mathrm{encoder}} \in \mathbb{R}^{B \times C \times H \times W}$是来自UNet编码器的中间特征表示。49个Agent tokens形成$7 \times 7$网格，每个Agent token代表一个空间区域，能够识别积水中心、水油交界等关键区域。

**这一步的作用**：将UNet编码器提取的高维空间特征转换为紧凑的Agent表示，实现对积水区域全局分布模式的高效捕获，为后续的精确建模提供基础。

##### 3.2.3.2 双向注意力机制的积水区域建模

**双向注意力计算**：
$$\begin{aligned}
\boldsymbol{Q}, \boldsymbol{K} &= \mathrm{Linear}(\boldsymbol{A}) \in \mathbb{R}^{B \times 49 \times C} \\
\boldsymbol{V} &= \mathrm{DepthwiseConv2d}(\boldsymbol{H}_{\mathrm{encoder}}) \in \mathbb{R}^{B \times (H \times W) \times C} \\
\boldsymbol{A}_{\mathrm{agent}} &= \mathrm{softmax}\left(\frac{\boldsymbol{Q}\boldsymbol{K}^{\mathrm{T}}}{\sqrt{d}}\right) \in \mathbb{R}^{B \times 49 \times 49} \\
\boldsymbol{A}_{\mathrm{spatial}} &= \mathrm{softmax}\left(\frac{\boldsymbol{K}\boldsymbol{Q}^{\mathrm{T}}}{\sqrt{d}}\right) \in \mathbb{R}^{B \times (H \times W) \times 49}
\end{aligned} \tag{6}$$

双向注意力机制专门针对积水区域的空间特征进行建模：

**Agent间协作**（$\boldsymbol{A}_{\mathrm{agent}}$）：建立不同空间区域Agent之间的协作关系，确保积水区域在全局范围内的连贯性。例如，积水中心区域的Agent与边界区域的Agent建立强关联，保证积水区域的整体一致性。

**精确空间定位**（$\boldsymbol{A}_{\mathrm{spatial}}$）：建立每个像素位置与全局Agent的对应关系，实现积水区域的精确定位。通过这种机制，每个像素都能获得来自全局Agent的上下文信息，提升积水区域边界的清晰度。

**这一步的作用**：通过双向注意力机制，积水区域的特征能够在全局范围内有效传播和增强，同时实现对水油交界、积水纹理等关键特征的精确建模。

##### 3.2.3.3 时间步感知的动态注意力调制

**时间步感知注意力调制**：
$$\boldsymbol{Y} = \boldsymbol{A}_{\mathrm{spatial}} \cdot (\boldsymbol{A}_{\mathrm{agent}} \cdot \boldsymbol{V}) + \boldsymbol{V} \tag{7a}$$
$$\boldsymbol{Y}_{\mathrm{out}} = \boldsymbol{Y} \times w_{\mathrm{attn}}(t) \tag{7b}$$

EvoTune的核心创新在于时间步感知的动态调制机制，该机制根据扩散过程的不同阶段采用不同的注意力策略：

**早期阶段**（$t=900$）：$w_{\mathrm{attn}}(t) = 0.504$ ↓，注意力权重较低，避免过度关注细节噪声，专注于建立积水区域的基础空间结构和整体分布模式。

**后期阶段**（$t=100$）：$w_{\mathrm{attn}}(t) = 0.916$ ↑，注意力权重显著增强，精细化处理水油边界、水面反射、纹理细节等复杂特征，提升积水区域的真实感。

**阶段适应性机制**：这种动态调制确保了Agent Attention能够在扩散过程的不同阶段发挥最适合的作用，早期建立结构、后期优化细节，与扩散模型的去噪特性完美匹配。

**这一步的作用**：通过时间步感知的动态调制，Agent Attention实现了对积水区域生成过程的精确控制，确保在合适的时机应用合适强度的注意力处理，为后续的频域处理和特征融合提供高质量的空间感知特征。

**Agent Attention在统一调制中的角色**：$\boldsymbol{Y}_{\mathrm{out}}$作为Agent Attention的输出，将作为EvoTune统一调制机制的第一步，为后续的频域处理提供经过空间优化的特征表示，确保整个调制流程的协同性和一致性。

#### 3.2.4 时间步感知的自适应频域处理机制

针对积水环境下水纹理细节的生成需求，EvoTune设计了时间步感知的自适应频域处理机制。该机制作为统一调制流程的第二步，接收Agent Attention增强的特征$\boldsymbol{Y}_{\mathrm{out}}$，通过分层滤波器设计对水纹理的频域成分进行精细控制。

**自适应频域滤波器设计**：
$$\tau(t) = 7 + \lfloor 5(1 - \tilde{t}) \rfloor \tag{8a}$$
$$\boldsymbol{M}_{u,v}(t) = \begin{cases}
s_1(t), & \text{if } \max(|u - u_c|, |v - v_c|) \leq \tau(t) \\
s_2(t), & \text{if } \tau(t) < \max(|u - u_c|, |v - v_c|) \leq 2\tau(t) \\
1, & \text{otherwise}
\end{cases} \tag{8b}$$

其中$\tau(t)$为时间步感知的动态阈值，$(u_c, v_c)$为频域中心坐标。

**这一步的作用**：设计时间步感知的分层滤波器，使用来自公式(3)的$s_1(t)$控制低频成分（积水区域的整体结构），$s_2(t)$控制中频成分（水纹理的细节特征），高频成分保持不变（保持图像的锐度），实现对积水区域不同频率成分的精细调控。

**频域处理在统一调制中的作用**：该滤波器将在后续的统一调制机制中应用于Agent Attention增强的特征，与门控融合机制协同工作，确保水纹理的频域特性得到精确优化，同时保持与原始特征的智能平衡。

#### 3.2.5 EvoTune在UNet中的统一特征调制机制

EvoTune作为UNet的内部控制器，通过统一的特征调制机制在关键连接点进行干预。该机制将Agent Attention增强、频域处理和时间步权重调制整合为一个完整的处理流程。\boldsymbol{H}_{\mathrm{freq}} = \mathrm{FFT2D}(\boldsymbol{H}_{\mathrm{attended}}) 
\boldsymbol{H}_{\mathrm{filtered}} = \boldsymbol{H}_{\mathrm{freq}} \odot \boldsymbol{M}(t) 
\boldsymbol{H}_{\mathrm{fourier}} = \mathrm{IFFT2D}(\boldsymbol{H}_{\mathrm{filtered}}) \tag{9d}

**统一特征调制的完整流程**：

对于UNet中的任意中间特征$\boldsymbol{H}_{\mathrm{input}} \in \mathbb{R}^{B \times C \times H \times W}$（可以是骨干特征或跳跃连接特征），EvoTune执行以下统一处理：

**步骤1：Agent Attention增强**
$$\boldsymbol{Y}_{\mathrm{out}} = \text{AgentAttention}(\boldsymbol{H}_{\mathrm{input}}, w_{\mathrm{attn}}(t)) \tag{9a}$$

其中AgentAttention按照公式(7a-7b)的完整流程执行，输出经过时间步感知调制的特征。

**步骤2：频域处理**
$$\boldsymbol{H}_{\mathrm{freq}} = \mathrm{FFT2D}(\boldsymbol{Y}_{\mathrm{out}}) \tag{9b}$$
$$\boldsymbol{H}_{\mathrm{filtered}} = \boldsymbol{H}_{\mathrm{freq}} \odot \boldsymbol{M}(t) \tag{9c}$$
$$\boldsymbol{H}_{\mathrm{fourier}} = \mathrm{IFFT2D}(\boldsymbol{H}_{\mathrm{filtered}}) \tag{9d}$$

**步骤3：自适应门控融合**
$$\boldsymbol{G} = \sigma(\text{Conv}([\boldsymbol{H}_{\mathrm{input}}, \boldsymbol{H}_{\mathrm{fourier}}])) \tag{9e}$$
$$\boldsymbol{F} = \text{Conv}([\boldsymbol{H}_{\mathrm{input}}, \boldsymbol{H}_{\mathrm{fourier}}]) \tag{9f}$$
$$\boldsymbol{H}_{\mathrm{enhanced}} = \boldsymbol{H}_{\mathrm{input}} \odot (1 - \boldsymbol{G} \odot b(t)) + \boldsymbol{F} \odot (\boldsymbol{G} \odot b(t)) \tag{9g}$$

**这一步的作用**：

- **公式(9a)**：调用完整的Agent Attention机制（公式7a-7b），对输入特征进行水油区域感知的增强和时间步感知的动态调制
- **公式(9b-9d)**：在频域对Agent Attention增强特征$\boldsymbol{Y}_{\mathrm{out}}$进行时间步感知的滤波处理，使用公式(8a-8b)设计的自适应滤波器优化水纹理的频域特性
- **公式(9e-9f)**：生成自适应门控信号和融合特征，实现原始特征与频域处理特征的智能选择
- **公式(9g)**：通过门控机制将原始输入特征和频域处理特征进行时间步感知的自适应融合，权重由$b(t)$动态控制

**在UNet不同位置的具体应用**：

**骨干特征调制**（编码器-解码器连接点）：
$$\boldsymbol{H}_{\mathrm{backbone}}^{\mathrm{enhanced}} = \text{EvoTuneModulation}(\boldsymbol{H}_{\mathrm{backbone}}, b_1(t), \boldsymbol{s}(t), w_{\mathrm{attn}}(t)) \tag{10}$$

**跳跃连接特征调制**（跳跃连接处）：
$$\boldsymbol{H}_{\mathrm{skip}}^{\mathrm{enhanced}} = \text{EvoTuneModulation}(\boldsymbol{H}_{\mathrm{skip}}, b_2(t), \boldsymbol{s}(t), w_{\mathrm{attn}}(t)) \tag{11}$$

其中$\text{EvoTuneModulation}(\cdot)$表示上述公式(9a-9g)定义的完整调制过程。

**这一步的作用**：
- **早期阶段**：$b_1(t)$增强，骨干特征权重提升，在编码器-解码器连接点增强特征传递，专注于建立积水区域的基础结构
- **后期阶段**：$b_2(t)$增强，跳跃连接权重提升，在跳跃连接处优化特征融合，专注于积水区域的细节特征完善

**与传统FreeU方法的根本区别**：

1. **动态vs静态**：EvoTune使用时间步感知的动态参数，而FreeU使用固定参数
2. **智能融合vs简单缩放**：EvoTune采用门控融合机制，而FreeU仅进行简单的特征缩放
3. **场景专用vs通用**：EvoTune专门针对水油场景优化，而FreeU是通用方法
4. **多模块协同vs单一处理**：EvoTune整合了注意力、频域和权重调制，而FreeU仅处理频域

### 3.3 EvoTune作为UNet中间控制器的协同优化机制

EvoTune的核心优势在于其作为UNet中间控制器的统一调谐框架，实现了多个处理模块在UNet内部的深度协同优化：

#### 3.3.1 统一的时间步调度机制
所有关键参数($\boldsymbol{b}(t)$, $\boldsymbol{s}(t)$, $w_{\mathrm{attn}}(t)$)都由统一的相位函数生成，确保在UNet的不同处理阶段参数变化的一致性。这种设计避免了参数冲突，保证了积水区域生成的稳定性和高质量。

**时间步统一调度的优势**：
- **参数协同性**：所有模块的参数演化曲线协调一致，避免相互干扰
- **阶段适应性**：早期专注结构建立，后期专注细节优化
- **计算效率**：统一的参数生成避免了重复计算

#### 3.3.2 特征空间的一致性处理
EvoTune的所有处理都在UNet的中间特征空间进行，实现了：

**空间一致性**：
- 所有模块都作用于相同的特征表示空间$\mathbb{R}^{B \times C \times H \times W}$
- 避免了多次特征空间转换带来的信息损失
- 保持了特征的语义连贯性

**处理流程的无缝集成**：
1. **Agent Attention**：在UNet特征空间进行水油区域的空间建模
2. **频域处理**：在相同特征空间进行纹理细节优化
3. **门控融合**：在特征空间内实现智能的自适应融合

#### 3.3.3 多模块功能的深度协同
EvoTune通过统一的调制机制实现了多个功能模块的深度协同：

**功能互补性**：
- **Agent Attention**：专注于空间结构和水油区域识别
- **频域处理**：专注于纹理细节和频域特性优化
- **门控融合**：专注于智能特征选择和自适应权重分配

**协同优化效应**：
- Agent Attention的空间感知为频域处理提供了精确的区域定位
- 频域处理的纹理增强为门控融合提供了高质量的候选特征
- 门控融合的智能选择确保了最优特征的传递

#### 3.3.4 深度集成的技术优势
与传统的外部后处理方法相比，EvoTune作为UNet中间控制器的设计实现了：

**真正的内部增强**：
- 直接作用于UNet的特征传播过程，而非简单的输出后处理
- 在特征生成的关键节点进行精确干预
- 保持了扩散模型的原有生成能力

**端到端的优化能力**：
- 所有处理模块都集成在UNet的前向传播中
- 支持统一的梯度反向传播和参数优化
- 实现了数据增强与模型训练的深度耦合

**计算效率的提升**：
- 避免了多次特征提取和空间转换
- 复用了UNet的中间计算结果
- 相比独立的后处理模块，计算开销更小

通过这种深度集成的协同优化机制，EvoTune能够生成在视觉上更逼真、细节更丰富的积水干扰图像，有效提升分割模型在复杂潮湿环境下的鲁棒性。

### 3.4 EvoTune作为UNet中间控制器的完整工作流程

基于上述各个模块的协同设计，EvoTune作为UNet中间控制器的完整工作流程如下：

#### 3.4.1 时间步感知参数生成阶段
1. **时间步标准化**：根据当前扩散时间步$t$，通过公式(1)计算标准化时间步$\tilde{t}$和相位函数
2. **统一参数调谐**：通过公式(2)-(4)生成所有时间步感知参数$\{\boldsymbol{b}(t), \boldsymbol{s}(t), w_{\mathrm{attn}}(t)\}$
3. **参数分发**：将生成的参数分发给UNet中的各个EvoTune调制点

#### 3.4.2 UNet前向传播中的EvoTune统一调制
1. **编码阶段**：UNet编码器正常提取多尺度特征$\boldsymbol{H}_{\mathrm{encoder}}$
2. **骨干特征调制**：在编码器-解码器连接点应用公式(10)的统一调制机制
   - 使用公式(9a)进行Agent Attention增强
   - 使用公式(9b-9d)进行频域处理
   - 使用公式(9e-9g)进行门控融合，权重参数为$b_1(t)$
3. **跳跃连接调制**：在各跳跃连接处应用公式(11)的统一调制机制
   - 执行相同的Agent Attention + 频域处理 + 门控融合流程
   - 权重参数为$b_2(t)$
4. **解码阶段**：UNet解码器使用EvoTune增强的特征进行最终图像生成

#### 3.4.3 统一调制机制的关键优势
- **处理一致性**：所有调制点使用相同的处理流程，确保特征增强的一致性
- **参数协同性**：不同位置使用协调的时间步感知参数，避免冲突
- **计算效率**：统一的调制机制减少了代码复杂度和计算开销
- **端到端优化**：整个调制过程集成在UNet前向传播中，支持梯度优化

#### 3.4.4 与传统方法的根本区别
**传统FreeU方法**：
- 使用固定参数进行简单的频域缩放
- 缺乏时间步感知和场景适应性
- 仅在频域进行单一处理

**EvoTune创新方法**：
- 使用时间步感知的动态参数调谐
- 集成Agent Attention、频域处理和门控融合
- 专门针对积水场景的多模块协同优化
- 在UNet内部实现真正的深度集成

### 3.5 算法复杂度分析

EvoTune作为UNet中间控制器的计算开销主要来自统一调制机制的各个组成部分：

#### 3.5.1 单次调制的计算复杂度
对于每个调制点（骨干特征或跳跃连接），统一调制机制的复杂度分解如下：

1. **时间步调谐**：$O(1)$
   - 相位函数计算和参数生成仅涉及标量运算
   - 一次计算可供所有调制点共享

2. **Agent Attention**：$O(49^2 + HW \times 49)$
   - Agent token生成：$O(HW)$（自适应平均池化）
   - Agent间注意力：$O(49^2)$（$49 \ll HW$）
   - 空间注意力：$O(HW \times 49)$（主要计算开销）

3. **频域处理**：$O(HW \log(HW))$
   - FFT变换：$O(HW \log(HW))$
   - 滤波器应用：$O(HW)$
   - IFFT变换：$O(HW \log(HW))$

4. **门控融合**：$O(HW \times C)$
   - 特征拼接：$O(HW \times C)$
   - 门控信号生成：$O(HW \times C)$（卷积操作）
   - 自适应融合：$O(HW \times C)$

#### 3.5.2 总体复杂度分析
**单次调制复杂度**：$O(HW \log(HW) + HW \times C)$

**总体额外开销**：
- EvoTune在UNet中有2个主要调制点（骨干+跳跃连接）
- 考虑到多尺度特征，实际调制次数约为4-6次
- 总额外复杂度：$O(k \times (HW \log(HW) + HW \times C))$，其中$k \approx 5$

#### 3.5.3 效率优势分析
**相对开销**：相对于原始UNet约增加15-20%的计算量

**效率优势**：
1. **参数共享**：时间步调谐参数在所有调制点间共享
2. **特征复用**：在UNet的中间特征上直接处理，避免重复特征提取
3. **内存友好**：Agent Attention将$O(N^2)$复杂度降低到$O(49N)$
4. **并行友好**：所有调制操作都支持GPU并行计算

**与替代方案的比较**：
- **vs 外部后处理**：避免了额外的特征提取和空间转换开销
- **vs 多模型集成**：单一模型内的集成避免了多次前向传播
- **vs 传统数据增强**：在线生成避免了大量数据存储和I/O开销

总体而言，EvoTune通过深度集成的设计在可接受的计算开销下实现了显著的积水干扰图像生成质量提升，为变电设备渗漏油分割提供了高效的数据增强解决方案。







**作用描述**： "该步骤将Agent Attention增强的特征转换到频域空间，利用时间步感知的自适应滤波器$\boldsymbol{M}(t)$对积水区域的频域成分进行精细调控。通过分层滤波策略，$s_1(t)$控制低频成分以优化积水的整体结构，$s_2(t)$控制中频成分以增强水纹理细节，而高频成分保持不变以维持图像锐度。这种频域处理专门针对积水环境下的视觉特征进行优化，显著提升了生成图像中水面反射、波纹纹理等关键细节的真实感。"

### **步骤3：自适应门控融合**

**作用描述**： "该步骤通过智能门控机制实现原始特征与频域增强特征的自适应融合。门控信号$\boldsymbol{G}$根据特征内容自动识别需要增强的区域，而时间步权重$b(t)$确保融合策略与扩散过程的去噪阶段相匹配。在早期阶段，较小的$b(t)$值保留更多原始特征以维持结构稳定性；在后期阶段，较大的$b(t)$值更多采用处理后特征以优化细节质量。这种动态融合机制避免了简单线性组合的局限性，实现了时间步感知和空间自适应的特征优化。"

### **对研究的核心价值**

**整体意义**： "这两个步骤的协同作用构成了EvoTune的核心技术优势：频域处理专门针对积水场景的视觉特征进行优化，而门控融合确保了处理效果与扩散过程的完美匹配。相比传统的固定参数方法，这种动态调制机制能够根据生成阶段和特征内容智能调整处理策略，显著提升了积水干扰图像的生成质量，为后续的渗漏油分割模型提供了更加真实和多样化的训练数据。"

------

## 🎯 **精简版本（适合论文）**

**步骤2**："通过时间步感知的自适应频域滤波，精确控制积水区域的低频结构和中频纹理，提升水面视觉特征的真实感。"

**步骤3**："采用智能门控机制实现原始特征与频域增强特征的动态融合，确保处理效果与扩散去噪过程的时间步特性相匹配。"

**核心价值**："两步骤协同实现了积水场景的专用优化，相比固定参数方法显著提升了生成图像质量和训练数据多样性。"
