
/* general gradio fixes */

:root, .dark{
    --checkbox-label-gap: 0.25em 0.1em;
    --section-header-text-size: 12pt;
    --block-background-fill: transparent;
}

.block.padded:not(.gradio-accordion) {
    padding: 0 !important;
}

div.gradio-container{
    max-width: unset !important;
}

.hidden{
    display: none;
}

.compact{
    background: transparent !important;
    padding: 0 !important;
}

div.form{
    border-width: 0;
    box-shadow: none;
    background: transparent;
    overflow: visible;
    gap: 0.5em;
}

.block.gradio-dropdown,
.block.gradio-slider,
.block.gradio-checkbox,
.block.gradio-textbox,
.block.gradio-radio,
.block.gradio-checkboxgroup,
.block.gradio-number,
.block.gradio-colorpicker
{
    border-width: 0 !important;
    box-shadow: none !important;
}

.gap.compact{
    padding: 0;
    gap: 0.2em 0;
}

div.compact{
    gap: 1em;
}

.gradio-dropdown label span:not(.has-info),
.gradio-textbox label span:not(.has-info),
.gradio-number label span:not(.has-info)
{
    margin-bottom: 0;
}

.gradio-dropdown ul.options{
    z-index: 3000;
    min-width: fit-content;
    max-width: inherit;
    white-space: nowrap;
}

.gradio-dropdown ul.options li.item {
    padding: 0.05em 0;
}

.gradio-dropdown ul.options li.item.selected {
    background-color: var(--neutral-100);
}

.dark .gradio-dropdown ul.options li.item.selected {
    background-color: var(--neutral-900);
}

.gradio-dropdown div.wrap.wrap.wrap.wrap{
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.gradio-dropdown:not(.multiselect) .wrap-inner.wrap-inner.wrap-inner{
    flex-wrap: unset;
}

.gradio-dropdown .single-select{
    white-space: nowrap;
    overflow: hidden;
}

.gradio-dropdown .token-remove.remove-all.remove-all{
    display: none;
}

.gradio-dropdown.multiselect .token-remove.remove-all.remove-all{
    display: flex;
}

.gradio-slider input[type="number"]{
    width: 6em;
}

.block.gradio-checkbox {
    margin: 0.75em 1.5em 0 0;
}

.gradio-html div.wrap{
    height: 100%;
}
div.gradio-html.min{
    min-height: 0;
}

.block.gradio-gallery{
    background: var(--input-background-fill);
}

.gradio-container .prose a, .gradio-container .prose a:visited{
    color: unset;
    text-decoration: none;
}



/* general styled components */

.gradio-button.tool{
    max-width: 2.2em;
    min-width: 2.2em !important;
    height: 2.4em;
    align-self: end;
    line-height: 1em;
    border-radius: 0.5em;
}

.gradio-button.secondary-down{
    background: var(--button-secondary-background-fill);
    color: var(--button-secondary-text-color);
}
.gradio-button.secondary-down, .gradio-button.secondary-down:hover{
    box-shadow: 1px 1px 1px rgba(0,0,0,0.25) inset, 0px 0px 3px rgba(0,0,0,0.15) inset;
}
.gradio-button.secondary-down:hover{
    background: var(--button-secondary-background-fill-hover);
    color: var(--button-secondary-text-color-hover);
}

.checkboxes-row{
    margin-bottom: 0.5em;
    margin-left: 0em;
}
.checkboxes-row > div{
    flex: 0;
    white-space: nowrap;
    min-width: auto;
}

button.custom-button{
    border-radius: var(--button-large-radius);
    padding: var(--button-large-padding);
    font-weight: var(--button-large-text-weight);
    border: var(--button-border-width) solid var(--button-secondary-border-color);
    background: var(--button-secondary-background-fill);
    color: var(--button-secondary-text-color);
    font-size: var(--button-large-text-size);
    display: inline-flex;
    justify-content: center;
    align-items: center;
    transition: var(--button-transition);
    box-shadow: var(--button-shadow);
    text-align: center;
}

/* specific styled components */

.token-counter{
    position: absolute;
    display: inline-block;
    right: 2em;
    min-width: 0 !important;
    width: auto;
    z-index: 100;
}

.token-counter div{
    display: inline;
}

.token-counter span{
    padding: 0.1em 0.75em;
    background: var(--input-background-fill) !important;
    box-shadow: 0 0 0.0 0.3em rgba(192,192,192,0.15), inset 0 0 0.6em rgba(192,192,192,0.075);
    border: 2px solid rgba(192,192,192,0.4) !important;
    border-radius: 0.4em;
}

/* footer */

.versions{
    text-align: center;
    font-size: 85%;
    opacity: 0.85;
}