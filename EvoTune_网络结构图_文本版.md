# EvoTune网络结构图 - 文本版

## 1. 简化的Mermaid代码（兼容版本）

```mermaid
graph TB
    A[输入: 噪声图像 x_t, 时间步 t] --> B[时间步标准化]
    B --> C[相位计算]
    C --> D[参数调谐: b1, b2, s1, s2]
    
    A --> E[Agent生成: 49个tokens]
    E --> F[双向注意力]
    F --> G[时间步调制]
    D --> G
    
    G --> H[FFT变换]
    H --> I[自适应滤波]
    I --> J[IFFT逆变换]
    D --> I
    
    J --> K[输出: 增强特征]
    
    style A fill:#e1f5fe
    style D fill:#fff3e0
    style G fill:#f3e5f5
    style I fill:#fce4ec
    style K fill:#e8f5e8
```

## 2. 网络结构文字描述

### 整体架构
```
输入层 → 三大核心创新模块 → 输出层
```

### 详细流程

#### 输入层
- **输入**: 噪声图像 x_t ∈ R^(B×C×H×W)
- **时间步**: t ∈ [0, 1000]

#### 核心创新1: 动态时间步调谐
```
时间步 t → 标准化 → 相位计算 → 参数调谐
                                    ↓
                            b1(t), b2(t), s1(t), s2(t)
```

#### 核心创新2: 水油专用Agent Attention  
```
输入特征 → Agent生成 → 双向注意力 → 时间步调制
                                        ↑
                                   参数调谐输出
```

#### 核心创新3: 自适应频域处理
```
注意力输出 → FFT变换 → 自适应滤波 → IFFT逆变换
                           ↑
                      参数调谐输出
```

#### 输出层
- **输出**: 增强特征 → 送入原始UNet

## 3. 模块间的数据流

### 主要数据流向
1. **输入** → **时间步调谐** → **参数生成**
2. **输入** → **Agent Attention** → **特征增强**  
3. **特征增强** → **频域处理** → **最终输出**

### 参数传递
- 时间步调谐模块生成的参数 (b1, b2, s1, s2, w_attn) 
- 传递给Agent Attention模块 (使用w_attn)
- 传递给频域处理模块 (使用s1, s2)

## 4. 关键创新点标注

### 🎯 核心创新1: 动态时间步调谐
- **位置**: 网络前端
- **功能**: 根据扩散阶段生成自适应参数
- **输出**: b1(t), b2(t), s1(t), s2(t), w_attn(t)

### 🎯 核心创新2: 水油专用Agent Attention
- **位置**: 网络中段
- **功能**: 针对水油场景的专用注意力机制
- **特点**: 49个Agent tokens + 双向注意力

### 🎯 核心创新3: 自适应频域处理
- **位置**: 网络后段  
- **功能**: 基于时间步的动态频域滤波
- **特点**: FFT + 自适应滤波 + IFFT

## 5. 网络结构的设计原理

### 串行处理设计
- **时间步调谐** → **Agent Attention** → **频域处理**
- 每个模块都利用前一个模块的输出
- 形成递进式的特征增强

### 参数共享机制
- 时间步调谐模块生成的参数被后续模块共享
- 保证了整个网络的时间步一致性
- 避免了参数冲突和不一致

### 模块化设计
- 每个创新点都是独立的模块
- 可以单独验证和优化
- 便于消融实验和性能分析

## 6. 与传统方法的对比

### 传统FreeU
```
输入 → 固定参数处理 → 输出
```

### EvoTune
```
输入 → 动态参数调谐 → Agent Attention → 频域处理 → 输出
```

### 关键区别
1. **参数**: 固定 vs 动态自适应
2. **注意力**: 无 vs 水油专用
3. **频域**: 固定滤波 vs 自适应滤波

## 7. 论文中的图表建议

### 主网络结构图
- 使用上述简化的流程图
- 突出三大核心创新模块
- 用不同颜色标注创新点

### 参数演化图
- 横轴: 时间步 t
- 纵轴: 参数值 (b1, s1, w_attn)
- 展示参数的动态变化曲线

### 模块详细图
- 每个核心创新模块的内部结构
- Agent Attention的双向机制
- 频域处理的滤波过程

## 8. 图表制作工具推荐

### 在线工具
- **Draw.io**: 免费，功能强大
- **Lucidchart**: 专业图表工具
- **Figma**: 设计工具，适合精美图表

### 学术工具
- **TikZ (LaTeX)**: 高质量学术图表
- **Matplotlib**: Python绘图，适合参数曲线
- **Visio**: Microsoft专业图表工具

### Mermaid替代
如果Mermaid有问题，建议使用Draw.io或TikZ制作最终的论文图表。

---

这个文本版本提供了完整的网络结构描述，您可以根据这个描述使用任何绘图工具制作最终的论文图表。
