"""
EvoTune Extension for Stable Diffusion WebUI
Enhanced water interference generation for power equipment oil leak detection
"""

__version__ = "1.0.0"
__author__ = "EvoTune Research Team"
__description__ = "Advanced diffusion model timestep tuning for water interference generation"

# 导入核心组件
from .modules.evotune_controller import EvoTuneController
from .modules.evotune_wrapper import EvoTuneWrapper, wrap_unet_with_evotune
from .modules.timestep_scheduler import TimestepScheduler
from .modules.agent_attention import AgentAttention, TimestepAwareAgentAttention
from .modules.fourier_processor import AdaptiveFourierProcessor

# 导入UI组件
try:
    from .ui.evotune_ui import EvoTuneUI, create_evotune_interface
except ImportError:
    # Gradio可能不可用
    EvoTuneUI = None
    create_evotune_interface = None

# 导入脚本
try:
    from .scripts.evotune_script import EvoTuneScript, create_evotune_script
except ImportError:
    # WebUI模块可能不可用
    EvoTuneScript = None
    create_evotune_script = None

__all__ = [
    'EvoTuneController',
    'EvoTuneWrapper', 
    'wrap_unet_with_evotune',
    'TimestepScheduler',
    'AgentAttention',
    'TimestepAwareAgentAttention', 
    'AdaptiveFourierProcessor',
    'EvoTuneUI',
    'create_evotune_interface',
    'EvoTuneScript',
    'create_evotune_script'
]
