"""
EvoTune UNet包装器
实现零侵入式的UNet增强
"""

import torch
import torch.nn as nn
from typing import Optional, Dict, Any, List, Tuple
import logging
import time
from contextlib import contextmanager

from .evotune_controller import EvoTuneController

logger = logging.getLogger(__name__)

class EvoTuneWrapper(nn.Module):
    """
    EvoTune UNet包装器
    通过包装原始UNet实现零侵入式增强
    """
    
    def __init__(self, 
                 original_unet: nn.Module,
                 enabled: bool = True,
                 target_channels: int = 320,
                 performance_monitoring: bool = True):
        """
        初始化EvoTune包装器
        
        Args:
            original_unet: 原始UNet模型
            enabled: 是否启用EvoTune
            target_channels: 目标特征通道数
            performance_monitoring: 是否启用性能监控
        """
        super().__init__()
        
        self.original_unet = original_unet
        self.enabled = enabled
        self.target_channels = target_channels
        self.performance_monitoring = performance_monitoring
        
        # EvoTune控制器
        if self.enabled:
            self.evotune_controller = EvoTuneController(
                channels=target_channels,
                total_timesteps=1000
            )
        else:
            self.evotune_controller = None
        
        # 性能监控
        self.performance_stats = {
            'total_calls': 0,
            'evotune_time': 0.0,
            'original_time': 0.0,
            'memory_peak': 0
        }
        
        # 调制点配置
        self.modulation_points = self._configure_modulation_points()
        
        # 注册前向钩子
        self._register_hooks()
        
        logger.info(f"EvoTuneWrapper initialized, enabled={enabled}")
    
    def _configure_modulation_points(self) -> Dict[str, Dict]:
        """
        配置EvoTune调制点
        
        Returns:
            modulation_points: 调制点配置
        """
        # 根据Stable Diffusion UNet结构配置关键调制点
        points = {
            # 编码器-解码器连接点（骨干特征）
            'mid_block': {
                'type': 'backbone',
                'enabled': True,
                'priority': 1
            },
            
            # 跳跃连接点
            'up_blocks.0.resnets.0': {
                'type': 'skip',
                'enabled': True,
                'priority': 2
            },
            'up_blocks.1.resnets.0': {
                'type': 'skip',
                'enabled': True,
                'priority': 3
            },
            'up_blocks.2.resnets.0': {
                'type': 'skip',
                'enabled': True,
                'priority': 4
            }
        }
        
        return points
    
    def _register_hooks(self):
        """注册前向钩子到关键模块"""
        if not self.enabled:
            return
        
        self.hooks = []
        
        for name, module in self.original_unet.named_modules():
            if name in self.modulation_points:
                config = self.modulation_points[name]
                if config['enabled']:
                    hook = module.register_forward_hook(
                        self._create_hook_function(name, config)
                    )
                    self.hooks.append(hook)
                    logger.debug(f"Registered hook for {name}")
    
    def _create_hook_function(self, module_name: str, config: Dict):
        """
        创建钩子函数
        
        Args:
            module_name: 模块名称
            config: 配置信息
            
        Returns:
            hook_function: 钩子函数
        """
        def hook_function(module, input_data, output):
            if not self.enabled or self.evotune_controller is None:
                return output
            
            try:
                # 获取当前时间步（从全局上下文）
                from .timestep_scheduler import timestep_context
                timesteps, params = timestep_context.get_context()
                
                if timesteps is None:
                    logger.warning(f"No timestep context for {module_name}")
                    return output
                
                # 性能监控开始
                start_time = time.time() if self.performance_monitoring else None
                
                # 应用EvoTune调制
                if isinstance(output, torch.Tensor):
                    enhanced_output = self.evotune_controller(
                        output, timesteps, config['type']
                    )
                elif isinstance(output, (tuple, list)):
                    # 处理多输出情况
                    enhanced_output = []
                    for item in output:
                        if isinstance(item, torch.Tensor):
                            enhanced_item = self.evotune_controller(
                                item, timesteps, config['type']
                            )
                            enhanced_output.append(enhanced_item)
                        else:
                            enhanced_output.append(item)
                    enhanced_output = type(output)(enhanced_output)
                else:
                    enhanced_output = output
                
                # 性能监控结束
                if self.performance_monitoring and start_time is not None:
                    self.performance_stats['evotune_time'] += time.time() - start_time
                
                return enhanced_output
                
            except Exception as e:
                logger.error(f"EvoTune hook failed for {module_name}: {e}")
                return output  # 降级策略：返回原始输出
        
        return hook_function
    
    @contextmanager
    def _performance_monitor(self):
        """性能监控上下文管理器"""
        if self.performance_monitoring:
            start_time = time.time()
            start_memory = torch.cuda.memory_allocated() if torch.cuda.is_available() else 0
            
            try:
                yield
            finally:
                end_time = time.time()
                end_memory = torch.cuda.memory_allocated() if torch.cuda.is_available() else 0
                
                self.performance_stats['total_calls'] += 1
                self.performance_stats['original_time'] += end_time - start_time
                self.performance_stats['memory_peak'] = max(
                    self.performance_stats['memory_peak'], 
                    end_memory - start_memory
                )
        else:
            yield
    
    def forward(self, 
                sample: torch.Tensor,
                timestep: torch.Tensor,
                encoder_hidden_states: Optional[torch.Tensor] = None,
                **kwargs) -> torch.Tensor:
        """
        包装器前向传播
        
        Args:
            sample: 输入样本 [B, C, H, W]
            timestep: 时间步 [B] or scalar
            encoder_hidden_states: 编码器隐藏状态
            **kwargs: 其他参数
            
        Returns:
            output: 输出样本 [B, C, H, W]
        """
        # 设置时间步上下文
        if self.enabled and self.evotune_controller is not None:
            from .timestep_scheduler import timestep_context
            params = self.evotune_controller.timestep_scheduler.get_adaptive_params(timestep)
            timestep_context.set_context(timestep, params)
        
        try:
            with self._performance_monitor():
                # 调用原始UNet
                output = self.original_unet(
                    sample=sample,
                    timestep=timestep,
                    encoder_hidden_states=encoder_hidden_states,
                    **kwargs
                )
            
            return output
            
        finally:
            # 清理上下文
            if self.enabled:
                from .timestep_scheduler import timestep_context
                timestep_context.clear_context()
    
    def enable_evotune(self):
        """启用EvoTune"""
        self.enabled = True
        if self.evotune_controller is None:
            self.evotune_controller = EvoTuneController(
                channels=self.target_channels,
                total_timesteps=1000
            )
        logger.info("EvoTune enabled")
    
    def disable_evotune(self):
        """禁用EvoTune"""
        self.enabled = False
        logger.info("EvoTune disabled")
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        if not self.performance_monitoring:
            return {"monitoring": "disabled"}
        
        stats = self.performance_stats.copy()
        
        if stats['total_calls'] > 0:
            stats['avg_evotune_time'] = stats['evotune_time'] / stats['total_calls']
            stats['avg_original_time'] = stats['original_time'] / stats['total_calls']
            stats['evotune_overhead'] = (stats['evotune_time'] / stats['original_time']) * 100
        
        return stats
    
    def reset_performance_stats(self):
        """重置性能统计"""
        self.performance_stats = {
            'total_calls': 0,
            'evotune_time': 0.0,
            'original_time': 0.0,
            'memory_peak': 0
        }
        logger.info("Performance stats reset")
    
    def configure_modulation_points(self, points_config: Dict[str, Dict]):
        """
        配置调制点
        
        Args:
            points_config: 调制点配置
        """
        # 移除旧钩子
        if hasattr(self, 'hooks'):
            for hook in self.hooks:
                hook.remove()
        
        # 更新配置
        self.modulation_points.update(points_config)
        
        # 重新注册钩子
        self._register_hooks()
        
        logger.info(f"Modulation points reconfigured: {list(points_config.keys())}")
    
    def get_modulation_status(self) -> Dict[str, Any]:
        """获取调制状态信息"""
        return {
            'enabled': self.enabled,
            'modulation_points': self.modulation_points,
            'controller_initialized': self.evotune_controller is not None,
            'hooks_registered': len(getattr(self, 'hooks', [])),
            'target_channels': self.target_channels
        }
    
    def __del__(self):
        """析构函数，清理钩子"""
        if hasattr(self, 'hooks'):
            for hook in self.hooks:
                try:
                    hook.remove()
                except:
                    pass


def wrap_unet_with_evotune(unet: nn.Module, 
                          enabled: bool = True,
                          **kwargs) -> EvoTuneWrapper:
    """
    便捷函数：将UNet包装为EvoTune增强版本
    
    Args:
        unet: 原始UNet模型
        enabled: 是否启用EvoTune
        **kwargs: 其他配置参数
        
    Returns:
        wrapped_unet: EvoTune包装的UNet
    """
    wrapped_unet = EvoTuneWrapper(unet, enabled=enabled, **kwargs)
    logger.info(f"UNet wrapped with EvoTune (enabled={enabled})")
    return wrapped_unet


class EvoTuneMonitor:
    """
    EvoTune监控器
    提供实时监控和调试功能
    """
    
    def __init__(self, wrapper: EvoTuneWrapper):
        self.wrapper = wrapper
        self.log_history = []
    
    def log_generation_step(self, step: int, timestep: int, stats: Dict):
        """记录生成步骤"""
        log_entry = {
            'step': step,
            'timestep': timestep,
            'timestamp': time.time(),
            'stats': stats
        }
        self.log_history.append(log_entry)
    
    def get_real_time_stats(self) -> Dict[str, Any]:
        """获取实时统计信息"""
        return {
            'wrapper_status': self.wrapper.get_modulation_status(),
            'performance': self.wrapper.get_performance_stats(),
            'recent_logs': self.log_history[-10:] if self.log_history else []
        }
    
    def export_logs(self, filepath: str):
        """导出日志到文件"""
        import json
        with open(filepath, 'w') as f:
            json.dump(self.log_history, f, indent=2)
        logger.info(f"Logs exported to {filepath}")
