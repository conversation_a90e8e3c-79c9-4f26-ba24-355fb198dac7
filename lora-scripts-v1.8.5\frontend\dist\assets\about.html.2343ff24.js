import{_ as n,r as o,o as s,c,a as e,b as r,d as a}from"./app.9273d30a.js";const h={},i=e("h2",{id:"\u5173\u4E8E",tabindex:"-1"},[e("a",{class:"header-anchor",href:"#\u5173\u4E8E","aria-hidden":"true"},"#"),r(" \u5173\u4E8E")],-1),d={href:"https://github.com/shigma/schemastery",target:"_blank",rel:"noopener noreferrer"},l=e("h3",{id:"\u53CD\u9988",tabindex:"-1"},[e("a",{class:"header-anchor",href:"#\u53CD\u9988","aria-hidden":"true"},"#"),r(" \u53CD\u9988")],-1),_={href:"https://github.com/Akegarasu/lora-scripts/issues",target:"_blank",rel:"noopener noreferrer"},u=e("h3",{id:"\u8054\u7CFB\u65B9\u5F0F",tabindex:"-1"},[e("a",{class:"header-anchor",href:"#\u8054\u7CFB\u65B9\u5F0F","aria-hidden":"true"},"#"),r(" \u8054\u7CFB\u65B9\u5F0F")],-1),p=e("p",null,"\u90AE\u7BB1\<EMAIL>",-1),f={href:"https://discord.gg/Uu3syD9PnR",target:"_blank",rel:"noopener noreferrer"};function m(b,k){const t=o("ExternalLinkIcon");return s(),c("div",null,[i,e("p",null,[r("\u7531 "),e("a",d,[r("schemastery"),a(t)]),r(" \u5F3A\u529B\u9A71\u52A8")]),l,e("p",null,[r("\u8BF7\u524D\u5F80 Github \u63D0\u4EA4 "),e("a",_,[r("issue"),a(t)])]),u,p,e("p",null,[e("a",f,[r("discord \u9891\u9053"),a(t)])])])}var x=n(h,[["render",m],["__file","about.html.vue"]]);export{x as default};
